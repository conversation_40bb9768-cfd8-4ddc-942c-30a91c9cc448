/**
 * 同层私聊文件处理增强功能
 * 支持各种文件类型的上传和AI分析
 * 作者: kencuo
 * 版本: 1.0.0
 */

// 支持的文件类型配置
const SUPPORTED_FILE_TYPES = {
  // 文档类型
  documents: {
    'text/plain': { icon: '📄', name: 'TXT文档' },
    'application/json': { icon: '📋', name: 'JSON文件' },
    'text/csv': { icon: '📊', name: 'CSV表格' },
    'text/html': { icon: '🌐', name: 'HTML文件' },
    'text/xml': { icon: '📰', name: 'XML文件' },
    'application/xml': { icon: '📰', name: 'XML文件' },
    'text/markdown': { icon: '📝', name: 'Markdown文档' },
    'application/pdf': { icon: '📕', name: 'PDF文档' },
    'application/msword': { icon: '📘', name: 'Word文档' },
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { icon: '📘', name: 'Word文档' }
  },
  // 图片类型
  images: {
    'image/jpeg': { icon: '🖼️', name: 'JPEG图片' },
    'image/jpg': { icon: '🖼️', name: 'JPG图片' },
    'image/png': { icon: '🖼️', name: 'PNG图片' },
    'image/gif': { icon: '🎞️', name: 'GIF动图' },
    'image/webp': { icon: '🖼️', name: 'WebP图片' },
    'image/svg+xml': { icon: '🎨', name: 'SVG图形' }
  },
  // 音频类型
  audio: {
    'audio/mpeg': { icon: '🎵', name: 'MP3音频' },
    'audio/wav': { icon: '🎵', name: 'WAV音频' },
    'audio/ogg': { icon: '🎵', name: 'OGG音频' },
    'audio/mp4': { icon: '🎵', name: 'M4A音频' }
  },
  // 视频类型
  video: {
    'video/mp4': { icon: '🎬', name: 'MP4视频' },
    'video/webm': { icon: '🎬', name: 'WebM视频' },
    'video/ogg': { icon: '🎬', name: 'OGV视频' }
  }
};

// 文件大小限制（MB）
const FILE_SIZE_LIMITS = {
  documents: 50,  // 文档50MB
  images: 20,     // 图片20MB
  audio: 100,     // 音频100MB
  video: 200      // 视频200MB
};

/**
 * 获取文件类型信息
 */
function getFileTypeInfo(file) {
  const mimeType = file.type;
  
  // 检查各个类型分类
  for (const [category, types] of Object.entries(SUPPORTED_FILE_TYPES)) {
    if (types[mimeType]) {
      return {
        category,
        ...types[mimeType],
        mimeType,
        supported: true
      };
    }
  }
  
  // 未知类型
  return {
    category: 'unknown',
    icon: '📁',
    name: '未知文件',
    mimeType,
    supported: false
  };
}

/**
 * 验证文件是否符合要求
 */
function validateFile(file) {
  const fileInfo = getFileTypeInfo(file);
  const maxSize = FILE_SIZE_LIMITS[fileInfo.category] || 10;
  const maxBytes = maxSize * 1024 * 1024;
  
  if (!fileInfo.supported) {
    throw new Error(`不支持的文件类型: ${file.type || '未知'}`);
  }
  
  if (file.size > maxBytes) {
    throw new Error(`文件过大，${fileInfo.category}类型文件最大支持${maxSize}MB`);
  }
  
  return fileInfo;
}

/**
 * 读取文件内容
 */
async function readFileContent(file, fileInfo) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const result = e.target.result;
        
        if (fileInfo.category === 'images') {
          // 图片返回base64数据
          resolve({
            type: 'image',
            data: result,
            preview: result
          });
        } else if (fileInfo.category === 'documents') {
          // 文档返回文本内容
          let content = result;
          
          // JSON文件格式化
          if (fileInfo.mimeType === 'application/json') {
            try {
              const jsonObj = JSON.parse(content);
              content = JSON.stringify(jsonObj, null, 2);
            } catch (e) {
              // 如果JSON解析失败，保持原内容
            }
          }
          
          resolve({
            type: 'document',
            data: content,
            preview: content.length > 500 ? content.substring(0, 500) + '...' : content
          });
        } else {
          // 其他类型返回base64
          resolve({
            type: 'binary',
            data: result,
            preview: `${fileInfo.name} (${(file.size / 1024).toFixed(1)} KB)`
          });
        }
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = () => reject(new Error('文件读取失败'));
    
    // 根据文件类型选择读取方式
    if (fileInfo.category === 'documents') {
      reader.readAsText(file, 'UTF-8');
    } else {
      reader.readAsDataURL(file);
    }
  });
}

/**
 * 处理文件上传并发送到聊天
 */
async function handleFileUploadToChat(file) {
  try {
    // 显示处理状态
    showUploadStatus(`正在处理文件: ${file.name}...`);
    
    // 验证文件
    const fileInfo = validateFile(file);
    
    // 读取文件内容
    const fileContent = await readFileContent(file, fileInfo);
    
    // 创建文件消息
    const fileMessage = {
      type: 'file',
      user: getCurrentUser(),
      time: getTimeStr(),
      fileName: file.name,
      fileSize: file.size,
      fileType: fileInfo.mimeType,
      fileCategory: fileInfo.category,
      fileIcon: fileInfo.icon,
      fileData: fileContent.data,
      filePreview: fileContent.preview,
      needsAIAnalysis: true // 标记需要AI分析
    };
    
    // 发送文件消息
    sendMessage(fileMessage);
    
    // 显示成功状态
    showUploadStatus(`文件上传成功: ${file.name}`, 'success');
    
    // 延迟触发AI分析
    setTimeout(() => {
      requestAIFileAnalysis(fileMessage);
    }, 1000);
    
    return fileMessage;
    
  } catch (error) {
    console.error('文件处理失败:', error);
    showUploadStatus(`文件处理失败: ${error.message}`, 'error');
    throw error;
  }
}

/**
 * 请求AI分析文件
 */
async function requestAIFileAnalysis(fileMessage) {
  try {
    // 检查AI生成函数是否可用
    if (!AI_GENERATE && !AI_GENERATE_RAW) {
      console.warn('AI生成函数不可用，跳过文件分析');
      return;
    }
    
    // 构建AI分析提示
    let analysisPrompt = '';
    
    if (fileMessage.fileCategory === 'documents') {
      analysisPrompt = `我刚刚上传了一个文档文件"${fileMessage.fileName}"，请帮我分析这个文档的内容：

文档内容：
${fileMessage.filePreview}

请提供以下分析：
1. 文档的主要内容和主题
2. 关键信息摘要
3. 重要观点或数据
4. 如果有问题或需要进一步讨论的地方

请用简洁明了的方式回复。`;
    } else if (fileMessage.fileCategory === 'images') {
      analysisPrompt = `我刚刚上传了一张图片"${fileMessage.fileName}"，请分析这张图片的内容，描述你看到的内容，包括：
1. 图片中的主要对象和场景
2. 颜色、构图等视觉特征
3. 可能的含义或用途
4. 任何值得注意的细节`;
    } else {
      analysisPrompt = `我刚刚上传了一个${fileMessage.fileIcon}${fileMessage.fileName}文件，这是一个${fileMessage.fileCategory}类型的文件。请根据文件信息给出相应的回复和建议。`;
    }
    
    // 发送AI分析请求消息
    const analysisMessage = {
      type: 'text',
      user: getCurrentUser(),
      time: getTimeStr(),
      message: analysisPrompt,
      isAIRequest: true // 标记为AI请求
    };
    
    sendMessage(analysisMessage);
    
    // 触发AI回复
    setTimeout(() => {
      requestAiReply();
    }, 500);
    
  } catch (error) {
    console.error('AI文件分析请求失败:', error);
  }
}

/**
 * 创建文件消息元素
 */
function createFileMessageElement(message) {
  const messageDiv = document.createElement('div');
  messageDiv.className = `message ${message.user === getCurrentUser() ? 'sent' : 'received'}`;
  
  const avatar = getUserAvatar(message.user);
  const time = message.time || getTimeStr();
  
  let fileContentHTML = '';
  
  if (message.fileCategory === 'images') {
    // 图片文件显示预览
    fileContentHTML = `
      <div class="file-content image-content">
        <img src="${message.fileData}" alt="${message.fileName}" style="max-width: 200px; max-height: 200px; border-radius: 8px;">
      </div>
    `;
  } else if (message.fileCategory === 'documents') {
    // 文档文件显示预览文本
    fileContentHTML = `
      <div class="file-content document-content">
        <div class="document-preview">
          <pre style="white-space: pre-wrap; font-family: inherit; margin: 0; max-height: 150px; overflow-y: auto;">${message.filePreview}</pre>
        </div>
      </div>
    `;
  } else {
    // 其他文件显示基本信息
    fileContentHTML = `
      <div class="file-content binary-content">
        <div class="file-info">
          <div class="file-description">${message.filePreview}</div>
        </div>
      </div>
    `;
  }
  
  messageDiv.innerHTML = `
    <div class="message-content">
      <div class="message-header">
        <img class="avatar" src="${avatar}" alt="${message.user}">
        <div class="message-info">
          <div class="username">${message.user}</div>
          <div class="timestamp">${time}</div>
        </div>
      </div>
      <div class="file-message">
        <div class="file-header">
          <span class="file-icon">${message.fileIcon}</span>
          <div class="file-details">
            <div class="file-name">${message.fileName}</div>
            <div class="file-meta">${(message.fileSize / 1024).toFixed(1)} KB • ${SUPPORTED_FILE_TYPES[message.fileCategory]?.[message.fileType]?.name || message.fileType}</div>
          </div>
        </div>
        ${fileContentHTML}
      </div>
    </div>
  `;
  
  return messageDiv;
}

/**
 * 显示上传状态
 */
function showUploadStatus(message, type = 'info') {
  // 这里可以使用toastr或其他通知方式
  if (typeof toastr !== 'undefined') {
    if (type === 'success') {
      toastr.success(message);
    } else if (type === 'error') {
      toastr.error(message);
    } else {
      toastr.info(message);
    }
  } else {
    console.log(`[文件上传] ${message}`);
  }
}

/**
 * 添加文件上传按钮到界面
 */
function addFileUploadButton() {
  // 查找现有的文件按钮或创建新的
  let fileBtn = document.getElementById('fileBtn');
  
  if (!fileBtn) {
    // 如果没有文件按钮，创建一个
    fileBtn = document.createElement('button');
    fileBtn.id = 'fileBtn';
    fileBtn.className = 'action-btn';
    fileBtn.innerHTML = '📎';
    fileBtn.title = '上传文件';
    
    // 添加到操作栏
    const actionGrid = document.querySelector('.action-grid');
    if (actionGrid) {
      actionGrid.appendChild(fileBtn);
    }
  }
  
  // 创建隐藏的文件输入
  let fileInput = document.getElementById('universalFileInput');
  if (!fileInput) {
    fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.id = 'universalFileInput';
    fileInput.style.display = 'none';
    fileInput.multiple = false;
    
    // 设置接受的文件类型
    const acceptTypes = Object.values(SUPPORTED_FILE_TYPES)
      .flatMap(category => Object.keys(category))
      .join(',');
    fileInput.accept = acceptTypes;
    
    document.body.appendChild(fileInput);
  }
  
  // 绑定事件
  fileBtn.onclick = () => fileInput.click();
  fileInput.onchange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleFileUploadToChat(file);
    }
    // 清空输入，允许重复选择同一文件
    e.target.value = '';
  };
}

// 导出函数供外部使用
window.FileUploadEnhancer = {
  handleFileUploadToChat,
  requestAIFileAnalysis,
  createFileMessageElement,
  getFileTypeInfo,
  validateFile,
  addFileUploadButton,
  SUPPORTED_FILE_TYPES,
  FILE_SIZE_LIMITS
};

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
  addFileUploadButton();
  console.log('[文件上传增强] 已初始化');
});
