import './index.scss';

interface Task {
    type: '已接取任务' | '可接取任务' | '剧情任务';
    content: string;
}

class TavernTaskSystem {
    private acceptedTasks: string[] = [];
    private availableTasks: string[] = [];
    private storyTasks: string[] = [];
    private taskContainer: HTMLElement | null = null;
    private observer: MutationObserver | null = null;

    constructor() {
        this.init();
    }

    private init(): void {
        this.createTaskContainer();
        this.observeMessages();
        this.parseAllMessages();
    }

    private createTaskContainer(): void {
        // 创建任务容器
        this.taskContainer = document.createElement('div');
        this.taskContainer.className = 'tavern-task-container';
        this.taskContainer.innerHTML = `
            <div class="task-section accepted-tasks">
                <div class="section-title">已接取任务</div>
                <div id="accepted-tasks-list">
                    <div class="empty-section">暂无已接取的任务</div>
                </div>
            </div>
            <div class="task-section available-tasks">
                <div class="section-title">可接取任务</div>
                <div id="available-tasks-list">
                    <div class="empty-section">暂无可接取的任务</div>
                </div>
            </div>
            <div class="task-section story-tasks">
                <div class="section-title">剧情任务</div>
                <div id="story-tasks-list">
                    <div class="empty-section">暂无剧情任务</div>
                </div>
            </div>
        `;

        // 将任务容器添加到页面
        this.appendToPage();
    }

    private appendToPage(): void {
        // 尝试找到合适的位置插入任务容器
        const rightPanel = document.querySelector('#right-nav-panel') || 
                          document.querySelector('.right_panel') ||
                          document.querySelector('#rightSendForm') ||
                          document.body;

        if (rightPanel && this.taskContainer) {
            rightPanel.appendChild(this.taskContainer);
        }
    }

    private observeMessages(): void {
        this.observer = new MutationObserver(() => {
            this.parseAllMessages();
        });

        // 监听聊天容器的变化
        const chatContainer = document.querySelector('#chat') || 
                             document.querySelector('.chat') ||
                             document.querySelector('#sheld') ||
                             document.body;

        if (chatContainer) {
            this.observer.observe(chatContainer, {
                childList: true,
                subtree: true
            });
        }
    }

    private parseAllMessages(): void {
        // 清空现有任务
        this.acceptedTasks = [];
        this.availableTasks = [];
        this.storyTasks = [];

        // 获取所有消息内容
        const messageSelectors = [
            '.mes_text',
            '.message-text', 
            '[class*="message"]',
            '.mes',
            '.swipe_text'
        ];

        let messages: NodeListOf<Element> | null = null;
        
        for (const selector of messageSelectors) {
            messages = document.querySelectorAll(selector);
            if (messages.length > 0) break;
        }

        if (messages) {
            messages.forEach(message => {
                const text = (message as HTMLElement).textContent || 
                           (message as HTMLElement).innerText || '';
                this.parseTasksFromText(text);
            });
        }

        this.renderTasks();
    }

    private parseTasksFromText(text: string): void {
        // 匹配 <task>...</task> 格式
        const taskRegex = /<task>([\s\S]*?)<\/task>/g;
        let match: RegExpExecArray | null;

        while ((match = taskRegex.exec(text)) !== null) {
            const taskContent = match[1].trim();
            this.parseTaskContent(taskContent);
        }
    }

    private parseTaskContent(content: string): void {
        // 解析任务内容格式：[任务类型|任务内容]
        const taskPattern = /\[(已接取任务|可接取任务|剧情任务)\|(.*?)\]/g;
        let match: RegExpExecArray | null;

        while ((match = taskPattern.exec(content)) !== null) {
            const taskType = match[1] as Task['type'];
            const taskText = match[2].trim();

            switch (taskType) {
                case '已接取任务':
                    if (!this.acceptedTasks.includes(taskText)) {
                        this.acceptedTasks.push(taskText);
                    }
                    break;
                case '可接取任务':
                    if (!this.availableTasks.includes(taskText)) {
                        this.availableTasks.push(taskText);
                    }
                    break;
                case '剧情任务':
                    if (!this.storyTasks.includes(taskText)) {
                        this.storyTasks.push(taskText);
                    }
                    break;
            }
        }
    }

    private renderTasks(): void {
        if (!this.taskContainer) return;

        this.renderTaskSection('accepted-tasks-list', this.acceptedTasks, 'accepted', '已接取');
        this.renderTaskSection('available-tasks-list', this.availableTasks, 'available', '可接取');
        this.renderTaskSection('story-tasks-list', this.storyTasks, 'story', '剧情');
    }

    private renderTaskSection(containerId: string, tasks: string[], taskClass: string, statusText: string): void {
        const container = this.taskContainer?.querySelector(`#${containerId}`);
        if (!container) return;
        
        if (tasks.length === 0) {
            container.innerHTML = `<div class="empty-section">暂无${statusText}任务</div>`;
            return;
        }

        container.innerHTML = tasks.map(task => `
            <div class="task-item ${taskClass}-task">
                <div class="task-status status-${taskClass}">${statusText}</div>
                <div class="task-content">${this.escapeHtml(task)}</div>
            </div>
        `).join('');
    }

    private escapeHtml(text: string): string {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    public destroy(): void {
        if (this.observer) {
            this.observer.disconnect();
        }
        if (this.taskContainer && this.taskContainer.parentNode) {
            this.taskContainer.parentNode.removeChild(this.taskContainer);
        }
    }

    public refresh(): void {
        this.parseAllMessages();
    }
}

// 全局实例
let taskSystemInstance: TavernTaskSystem | null = null;

// 初始化函数
function initTaskSystem(): void {
    if (taskSystemInstance) {
        taskSystemInstance.destroy();
    }
    taskSystemInstance = new TavernTaskSystem();
}

// 清理函数
function destroyTaskSystem(): void {
    if (taskSystemInstance) {
        taskSystemInstance.destroy();
        taskSystemInstance = null;
    }
}

// 刷新函数
function refreshTaskSystem(): void {
    if (taskSystemInstance) {
        taskSystemInstance.refresh();
    }
}

// 导出函数供外部调用
(window as any).TavernTaskSystem = {
    init: initTaskSystem,
    destroy: destroyTaskSystem,
    refresh: refreshTaskSystem
};

// 页面加载完成后自动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initTaskSystem);
} else {
    initTaskSystem();
}

// 导出类型和实例
export { TavernTaskSystem, Task };
export default taskSystemInstance;
