# 🌙 Kimi识图功能使用说明

## 功能概述

我已经为您专门添加了Kimi（月之暗面）的识图支持！现在您可以使用Kimi的强大AI能力来识别图片内容了。

## 🚀 使用方法

### 1. 获取Kimi API密钥
1. 访问 [Kimi开放平台](https://platform.moonshot.cn/)
2. 注册/登录账号
3. 创建API密钥
4. 复制您的API密钥

### 2. 配置Kimi识图
1. 打开聊天界面的设置面板
2. 找到"🖼️ 识图API配置"部分
3. 在"识图方式"下拉菜单中选择"使用Kimi API（月之暗面）"
4. 输入您的Kimi API密钥
5. 选择Kimi模型（推荐使用 moonshot-v1-8k）
6. 点击"测试Kimi连接"验证配置

### 3. 开始使用
1. 上传图片到聊天界面
2. 点击左上角的AI回复按钮
3. 系统会显示"🌙 Kimi识图中..."
4. 识图完成后显示"✅ Kimi已识图"
5. AI会基于Kimi的识图结果进行回复

## 🎯 Kimi模型选择

### moonshot-v1-8k（推荐）
- **上下文长度**：8K tokens
- **适用场景**：日常图片识别
- **特点**：速度快，成本低

### moonshot-v1-32k
- **上下文长度**：32K tokens
- **适用场景**：复杂图片分析
- **特点**：更强的理解能力

### moonshot-v1-128k
- **上下文长度**：128K tokens
- **适用场景**：超长文档图片
- **特点**：最强的上下文理解

## 🔧 技术实现

### API兼容性处理
我实现了双重兼容性：

1. **Kimi原生格式**：
```javascript
{
  "model": "moonshot-v1-8k",
  "messages": [...],
  "files": [
    {
      "type": "image",
      "url": "data:image/jpeg;base64,..."
    }
  ]
}
```

2. **OpenAI兼容格式**（备用）：
```javascript
{
  "model": "moonshot-v1-8k",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请描述这张图片"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,..."
          }
        }
      ]
    }
  ]
}
```

### 智能回退机制
- Kimi识图失败 → 自动回退到酒馆API
- 酒馆API失败 → 使用原有分析方式
- 确保识图功能始终可用

## 🌟 Kimi的优势

### 1. 中文理解能力强
- 专为中文优化的AI模型
- 对中文场景和文化的理解更准确
- 识图结果更符合中文表达习惯

### 2. 成本效益高
- 相比国外API，价格更优惠
- 国内访问速度更快
- 无需科学上网

### 3. 模型能力强
- 基于先进的多模态大模型
- 支持图片中的文字识别
- 能理解复杂的视觉场景

## 📊 使用体验

### 状态指示器
- `🌙 Kimi识图中...` - 正在调用Kimi API
- `✅ Kimi已识图` - Kimi识图完成
- `❌ Kimi识图失败` - 识图失败，会自动回退

### 识图结果示例
```
用户发送了一张图片。

图片内容描述：这是一张在咖啡厅拍摄的照片。画面中央是一杯精美的拿铁咖啡，白色陶瓷杯中的咖啡表面有着精致的拉花图案，呈现出叶子的形状。杯子放置在深色的木质桌面上，背景是温暖的咖啡厅环境，光线柔和，营造出舒适惬意的氛围。桌面上还可以看到一些模糊的装饰元素，整体色调以暖棕色为主。
```

## 🛠️ 故障排除

### 常见问题

1. **"请配置Kimi API密钥"**
   - 检查是否正确输入了API密钥
   - 确认密钥没有多余的空格

2. **"Kimi API连接失败"**
   - 检查网络连接
   - 确认API密钥是否有效
   - 检查是否有足够的API配额

3. **"Kimi API返回数据格式不正确"**
   - 这通常是临时问题，系统会自动回退到其他方式
   - 可以稍后重试

### 调试信息
打开浏览器开发者工具（F12），在控制台中可以看到详细的调试信息：
- `🌙 Kimi识图完成: [识图结果]`
- `Kimi识图失败: [错误信息]`

## 💡 使用建议

1. **首次使用**：
   - 先测试连接确保配置正确
   - 从简单的图片开始测试

2. **模型选择**：
   - 日常使用推荐 moonshot-v1-8k
   - 复杂图片可以尝试 moonshot-v1-32k

3. **成本控制**：
   - 关注API使用量
   - 合理选择模型规格

4. **备用方案**：
   - 保持酒馆API配置作为备用
   - 可以随时切换识图方式

## 🎉 总结

现在您可以享受Kimi强大的中文识图能力了！Kimi在理解中文场景、识别中文文字、描述中国文化元素等方面都有出色的表现。

如果在使用过程中遇到任何问题，系统会自动回退到其他识图方式，确保功能的稳定性。

祝您使用愉快！🌙✨
