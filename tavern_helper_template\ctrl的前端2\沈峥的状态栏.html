
``
<!DOCTYPE html>
<body>
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script>
    $(document).ready(function () {
      const HTML_URL = 'https://my-pages-2md.pages.dev/ctrl的同层私聊.html';
      const RETRY_COUNT = 3;

      const injectionLine = `const charName='{{char}}';
        const userName = "{{user}}";
        const userAvatarPath ="{{userAvatarPath}}";
        const charAvatarPath = "{{charAvatarPath}}";`;

      // 保存当前状态到localStorage
      function saveCurrentChatState() {
        try {
          if (typeof window.state !== 'undefined' && window.state.messageHistory) {
            const stateToSave = {
              messageHistory: window.state.messageHistory,
              charName: typeof window.charName !== 'undefined' ? window.charName : '',
              userName: typeof window.userName !== 'undefined' ? window.userName : '',
              userAvatarPath: typeof window.userAvatarPath !== 'undefined' ? window.userAvatarPath : '',
              charAvatarPath: typeof window.charAvatarPath !== 'undefined' ? window.charAvatarPath : '',
              timestamp: Date.now()
            };
            localStorage.setItem('chatState_backup', JSON.stringify(stateToSave));
            console.log('💾 状态已保存，消息数量:', stateToSave.messageHistory.length);
          }
        } catch (e) {
          console.warn('⚠️ 保存状态失败:', e);
        }
      }

      // 恢复状态到新加载的页面
      function restoreChatState() {
        try {
          const savedState = localStorage.getItem('chatState_backup');
          if (savedState) {
            const parsed = JSON.parse(savedState);
            // 检查是否是最近保存的状态（10分钟内）
            if (Date.now() - parsed.timestamp < 10 * 60 * 1000) {
              if (parsed.messageHistory && parsed.messageHistory.length > 0) {
                console.log('🔄 正在恢复状态，消息数量:', parsed.messageHistory.length);
                
                // 等待页面完全加载后恢复状态
                setTimeout(() => {
                  if (typeof window.state !== 'undefined') {
                    window.state.messageHistory = parsed.messageHistory;
                    
                    // 重新渲染消息
                    if (typeof window.renderAllMessages === 'function') {
                      window.renderAllMessages();
                      console.log('✅ 消息历史已恢复并重新渲染');
                    } else {
                      console.warn('⚠️ renderAllMessages 函数未找到，尝试其他渲染方法');
                      // 尝试其他可能的渲染函数
                      if (typeof window.renderAll === 'function') {
                        window.renderAll();
                      }
                    }
                  } else {
                    console.warn('⚠️ window.state 未定义，延迟重试');
                    // 如果state还没有初始化，再等一会儿
                    setTimeout(() => {
                      if (typeof window.state !== 'undefined') {
                        window.state.messageHistory = parsed.messageHistory;
                        if (typeof window.renderAllMessages === 'function') {
                          window.renderAllMessages();
                        }
                      }
                    }, 1000);
                  }
                }, 500);
              }
            } else {
              // 清除过期的备份
              localStorage.removeItem('chatState_backup');
              console.log('🗑️ 清除过期的状态备份');
            }
          }
        } catch (e) {
          console.warn('⚠️ 恢复状态失败:', e);
          localStorage.removeItem('chatState_backup');
        }
      }

      function injectAndRender(htmlString) {
        if (!htmlString) return;
        
        console.log('🔄 开始更新页面内容...');
        
        // 在替换页面内容前保存当前状态
        saveCurrentChatState();
        
        const modifiedHtml = htmlString.replace('<script>', '<script>' + injectionLine);
        $('body').html(modifiedHtml);
        
        console.log('✅ 页面内容已更新');
        
        // 页面内容替换后，恢复之前的状态
        setTimeout(restoreChatState, 100);
      }

      async function fetchWithRetries(url, options = {}) {
        for (let attempt = 1; attempt <= RETRY_COUNT; attempt++) {
          try {
            const response = await fetch(url, options);
            if (!response.ok) throw new Error(`请求失败: ${response.status}`);
            return await response.text();
          } catch (error) {
            console.warn(`⚠️ (第${attempt}次)请求 ${url} 失败:`, error.message);
            if (attempt < RETRY_COUNT) {
              await new Promise(resolve => setTimeout(resolve, 1500));
            } else {
              throw error;
            }
          }
        }
      }

      async function performInitialLoad() {
        try {
          console.log('🚀 开始加载聊天界面...');
          const newHtml = await fetchWithRetries(HTML_URL);
          injectAndRender(newHtml);
        } catch (error) {
          console.error('❌ 加载失败，无法从网络获取内容，请检查网络连接后刷新。');
          // 显示错误信息给用户
          $('body').html(`
            <div style="display: flex; align-items: center; justify-content: center; height: 100vh; font-family: Arial, sans-serif;">
              <div style="text-align: center; color: #e74c3c;">
                <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                <div style="font-size: 18px; margin-bottom: 10px;">加载失败</div>
                <div style="font-size: 14px; color: #666; margin-bottom: 20px;">无法从网络获取内容，请检查网络连接</div>
                <button onclick="location.reload()" style="padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">
                  重新加载
                </button>
              </div>
            </div>
          `);
        }
      }

      // 定期保存状态（每30秒）
      setInterval(() => {
        saveCurrentChatState();
      }, 30000);

      // 页面卸载前保存状态
      $(window).on('beforeunload', function() {
        saveCurrentChatState();
      });

      // 监听页面可见性变化，在页面隐藏时保存状态
      document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
          saveCurrentChatState();
        }
      });

      async function main() {
        // 显示加载界面
        $('body').html(`
          <div style="display: flex; align-items: center; justify-content: center; height: 100vh; font-family: Arial, sans-serif;">
            <div style="text-align: center;">
              <div style="font-size: 24px; margin-bottom: 10px;">🐱</div>
              <div style="font-size: 18px; margin-bottom: 10px;">正在加载聊天界面...</div>
              <div style="font-size: 14px; color: #666;">请稍候，正在从服务器获取最新内容</div>
              <div style="margin-top: 20px;">
                <div style="display: inline-block; width: 20px; height: 20px; border: 2px solid #3498db; border-radius: 50%; border-top-color: transparent; animation: spin 1s linear infinite;"></div>
              </div>
            </div>
          </div>
          <style>
            @keyframes spin {
              to { transform: rotate(360deg); }
            }
          </style>
        `);
        
        performInitialLoad();
      }
      
      main();
    });
  </script>
</body>
</html>
```