<!DOCTYPE html>
<body>
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script>
    $(document).ready(function () {
      const HTML_URL = 'https://my-pages-2md.pages.dev/ctrl的同层私聊.html';
      const RETRY_COUNT = 3;
      const CACHE_NAME = 'sillytavern-chat-cache-v1';

      // 网络环境检测 - 检测是否需要梯子
      async function detectNetworkEnvironment() {
        const testUrls = [
          { url: 'https://www.google.com/favicon.ico', name: 'Google', needsVPN: true },
          { url: 'https://github.com/favicon.ico', name: 'GitHub', needsVPN: true },
          { url: 'https://pages.dev/favicon.ico', name: 'Cloudflare Pages', needsVPN: true },
          { url: 'https://www.baidu.com/favicon.ico', name: '百度', needsVPN: false }
        ];

        const results = [];

        for (const test of testUrls) {
          try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时

            const response = await fetch(test.url, {
              method: 'HEAD',
              signal: controller.signal,
              cache: 'no-cache'
            });

            clearTimeout(timeoutId);

            if (response.ok) {
              results.push(`✅ ${test.name}: 可访问`);
            } else {
              results.push(`❌ ${test.name}: HTTP ${response.status}`);
            }
          } catch (error) {
            if (error.name === 'AbortError') {
              results.push(`⏱️ ${test.name}: 超时`);
            } else {
              results.push(`❌ ${test.name}: ${error.message}`);
            }
          }
        }

        // 分析结果
        const canAccessVPNSites = testUrls.filter(t => t.needsVPN).some(t =>
          results.some(r => r.includes(t.name) && r.includes('✅'))
        );

        const canAccessDomestic = results.some(r => r.includes('百度') && r.includes('✅'));

        let networkStatus = '';
        if (canAccessVPNSites && canAccessDomestic) {
          networkStatus = '🌐 网络环境良好，可以访问国外网站（可能使用了梯子）';
        } else if (!canAccessVPNSites && canAccessDomestic) {
          networkStatus = '🚫 无法访问国外网站，可能需要开启梯子/VPN';
        } else if (!canAccessDomestic) {
          networkStatus = '❌ 网络连接异常，请检查网络设置';
        } else {
          networkStatus = '⚠️ 网络状态不明确';
        }

        return {
          details: results.join('\n'),
          status: networkStatus,
          needsVPN: !canAccessVPNSites && canAccessDomestic
        };
      }

      // 显示错误弹窗
      function showErrorDialog(title, message, details = '') {
        const errorHtml = `
          <div id="errorDialog" style="
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.7); z-index: 10000; display: flex;
            align-items: center; justify-content: center; font-family: Arial, sans-serif;
          ">
            <div style="
              background: white; padding: 30px; border-radius: 10px;
              max-width: 500px; width: 90%; box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            ">
              <div style="color: #e74c3c; font-size: 20px; margin-bottom: 15px; display: flex; align-items: center;">
                <span style="font-size: 24px; margin-right: 10px;">⚠️</span>
                ${title}
              </div>
              <div style="color: #333; font-size: 16px; margin-bottom: 15px; line-height: 1.5;">
                ${message}
              </div>
              ${details ? `
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                  <div style="font-weight: bold; margin-bottom: 10px; color: #666;">详细错误信息：</div>
                  <div style="font-family: monospace; font-size: 14px; color: #666; word-break: break-all;">
                    ${details}
                  </div>
                </div>
              ` : ''}
              <div style="text-align: right;">
                <button onclick="document.getElementById('errorDialog').remove()" style="
                  background: #95a5a6; color: white; border: none; padding: 10px 20px;
                  border-radius: 5px; cursor: pointer; margin-right: 10px;
                ">关闭</button>
                <button onclick="location.reload()" style="
                  background: #3498db; color: white; border: none; padding: 10px 20px;
                  border-radius: 5px; cursor: pointer;
                ">重新加载</button>
              </div>
            </div>
          </div>
        `;
        $('body').append(errorHtml);
      }

      // 缓存管理
      async function loadFromCache() {
        try {
          const cache = await caches.open(CACHE_NAME);
          const cachedResponse = await cache.match(HTML_URL);

          if (cachedResponse) {
            console.log('📦 从缓存加载内容...');
            const cachedHtml = await cachedResponse.text();
            injectAndRender(cachedHtml);
            return true;
          }
          return false;
        } catch (error) {
          console.warn('⚠️ 缓存访问失败:', error);
          return false;
        }
      }

      async function saveToCache(htmlContent) {
        try {
          const cache = await caches.open(CACHE_NAME);
          await cache.put(HTML_URL, new Response(htmlContent));
          console.log('💾 内容已保存到缓存');
        } catch (error) {
          console.warn('⚠️ 保存到缓存失败:', error);
        }
      }

      // 显示网络诊断信息
      async function showNetworkDiagnostics() {
        const diagnostics = [];

        // 检查SillyTavern环境
        diagnostics.push('🔍 SillyTavern环境检测:');
        if (typeof window.SillyTavern !== 'undefined') {
          diagnostics.push('✅ SillyTavern对象存在');
        } else {
          diagnostics.push('❌ SillyTavern对象不存在 (可能不在ST环境中)');
        }

        // 检查是否在iframe中
        if (window.self !== window.top) {
          diagnostics.push('✅ 运行在iframe中 (正常ST环境)');
        } else {
          diagnostics.push('⚠️ 不在iframe中 (可能直接打开了文件)');
        }

        // 检查网络连接
        if (!navigator.onLine) {
          diagnostics.push('❌ 设备离线，请检查网络连接');
        } else {
          diagnostics.push('✅ 设备在线');
        }

        // 检查jQuery是否加载成功
        if (typeof $ !== 'undefined') {
          diagnostics.push('✅ jQuery库加载成功 (版本: ' + ($.fn.jquery || '未知') + ')');
        } else {
          diagnostics.push('❌ jQuery库加载失败');
        }

        // 检查localStorage是否可用
        try {
          localStorage.setItem('test', 'test');
          localStorage.removeItem('test');
          diagnostics.push('✅ 本地存储可用');
        } catch (e) {
          diagnostics.push('❌ 本地存储不可用: ' + e.message);
        }

        // 检查缓存API
        try {
          if ('caches' in window) {
            const cache = await caches.open(CACHE_NAME);
            const cachedResponse = await cache.match(HTML_URL);
            if (cachedResponse) {
              diagnostics.push('✅ 缓存可用，已有缓存内容');
            } else {
              diagnostics.push('✅ 缓存可用，但无缓存内容');
            }
          } else {
            diagnostics.push('❌ 浏览器不支持缓存API');
          }
        } catch (e) {
          diagnostics.push('❌ 缓存检测失败: ' + e.message);
        }

        // 网络环境检测
        diagnostics.push('\n🌐 网络环境检测:');
        try {
          const networkInfo = await detectNetworkEnvironment();
          diagnostics.push(networkInfo.status);
          diagnostics.push('\n详细检测结果:');
          diagnostics.push(networkInfo.details);
        } catch (e) {
          diagnostics.push('❌ 网络环境检测失败: ' + e.message);
        }

        // 检查目标URL的可达性
        diagnostics.push('\n🎯 目标服务器:');
        diagnostics.push('目标URL: ' + HTML_URL);

        return diagnostics.join('\n');
      }

      const injectionLine = `const charName='{{char}}';
        const userName = "{{user}}";
        const userAvatarPath ="{{userAvatarPath}}";
        const charAvatarPath = "{{charAvatarPath}}";`;

      // 保存当前状态到localStorage
      function saveCurrentChatState() {
        try {
          if (typeof window.state !== 'undefined' && window.state.messageHistory) {
            const stateToSave = {
              messageHistory: window.state.messageHistory,
              charName: typeof window.charName !== 'undefined' ? window.charName : '',
              userName: typeof window.userName !== 'undefined' ? window.userName : '',
              userAvatarPath: typeof window.userAvatarPath !== 'undefined' ? window.userAvatarPath : '',
              charAvatarPath: typeof window.charAvatarPath !== 'undefined' ? window.charAvatarPath : '',
              timestamp: Date.now()
            };
            localStorage.setItem('chatState_backup', JSON.stringify(stateToSave));
            console.log('💾 状态已保存，消息数量:', stateToSave.messageHistory.length);
          }
        } catch (e) {
          console.warn('⚠️ 保存状态失败:', e);
          showErrorDialog(
            '状态保存失败',
            '无法保存聊天记录到本地存储，可能会丢失聊天历史。',
            e.message
          );
        }
      }

      // 恢复状态到新加载的页面
      function restoreChatState() {
        try {
          const savedState = localStorage.getItem('chatState_backup');
          if (savedState) {
            const parsed = JSON.parse(savedState);
            // 检查是否是最近保存的状态（10分钟内）
            if (Date.now() - parsed.timestamp < 10 * 60 * 1000) {
              if (parsed.messageHistory && parsed.messageHistory.length > 0) {
                console.log('🔄 正在恢复状态，消息数量:', parsed.messageHistory.length);

                // 等待页面完全加载后恢复状态
                setTimeout(() => {
                  if (typeof window.state !== 'undefined') {
                    window.state.messageHistory = parsed.messageHistory;

                    // 重新渲染消息
                    if (typeof window.renderAllMessages === 'function') {
                      window.renderAllMessages();
                      console.log('✅ 消息历史已恢复并重新渲染');
                    } else {
                      console.warn('⚠️ renderAllMessages 函数未找到，尝试其他渲染方法');
                      // 尝试其他可能的渲染函数
                      if (typeof window.renderAll === 'function') {
                        window.renderAll();
                      } else {
                        showErrorDialog(
                          '渲染函数缺失',
                          '无法找到消息渲染函数，聊天记录可能无法正确显示。',
                          '缺少 renderAllMessages 或 renderAll 函数'
                        );
                      }
                    }
                  } else {
                    console.warn('⚠️ window.state 未定义，延迟重试');
                    // 如果state还没有初始化，再等一会儿
                    setTimeout(() => {
                      if (typeof window.state !== 'undefined') {
                        window.state.messageHistory = parsed.messageHistory;
                        if (typeof window.renderAllMessages === 'function') {
                          window.renderAllMessages();
                        }
                      } else {
                        showErrorDialog(
                          '状态初始化失败',
                          '页面状态对象未能正确初始化，聊天功能可能无法正常工作。',
                          'window.state 对象未定义'
                        );
                      }
                    }, 1000);
                  }
                }, 500);
              }
            } else {
              // 清除过期的备份
              localStorage.removeItem('chatState_backup');
              console.log('🗑️ 清除过期的状态备份');
            }
          }
        } catch (e) {
          console.warn('⚠️ 恢复状态失败:', e);
          localStorage.removeItem('chatState_backup');
          showErrorDialog(
            '状态恢复失败',
            '无法恢复之前的聊天记录，可能是数据损坏。',
            e.message
          );
        }
      }

      function injectAndRender(htmlString) {
        if (!htmlString) return;

        console.log('🔄 开始更新页面内容...');
        console.log('📄 HTML内容长度:', htmlString.length);
        console.log('🔧 注入的变量:', injectionLine);

        // 在替换页面内容前保存当前状态
        saveCurrentChatState();

        // 检查HTML内容是否包含script标签
        if (!htmlString.includes('<script>')) {
          console.warn('⚠️ HTML内容中未找到<script>标签，无法注入变量');
          showErrorDialog(
            '内容格式错误',
            '获取的HTML内容格式不正确，缺少必要的script标签。',
            '未找到<script>标签用于变量注入'
          );
        }

        const modifiedHtml = htmlString.replace('<script>', '<script>' + injectionLine);

        // 验证注入是否成功
        if (modifiedHtml === htmlString) {
          console.warn('⚠️ 变量注入可能失败，HTML内容未发生变化');
        } else {
          console.log('✅ 变量注入成功');
        }

        $('body').html(modifiedHtml);

        console.log('✅ 页面内容已更新');

        // 页面内容替换后，恢复之前的状态
        setTimeout(restoreChatState, 100);

        // 验证页面是否正确加载
        setTimeout(() => {
          const scriptTags = document.querySelectorAll('script').length;
          const hasContent = document.body.children.length > 0;
          console.log(`📊 页面验证: ${scriptTags}个script标签, 内容元素: ${hasContent ? '存在' : '不存在'}`);

          if (!hasContent) {
            showErrorDialog(
              '页面加载异常',
              '页面内容加载后为空，可能是HTML格式问题或脚本执行失败。',
              `Script标签数量: ${scriptTags}\nHTML长度: ${htmlString.length}`
            );
          }
        }, 1000);
      }

      async function fetchWithRetries(url, options = {}) {
        for (let attempt = 1; attempt <= RETRY_COUNT; attempt++) {
          try {
            console.log(`🔄 第${attempt}次尝试加载: ${url}`);

            // 添加超时控制
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

            const response = await fetch(url, {
              ...options,
              signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
              throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const text = await response.text();
            console.log(`✅ 成功获取内容，大小: ${text.length} 字符`);
            return text;

          } catch (error) {
            console.warn(`⚠️ (第${attempt}次)请求 ${url} 失败:`, error.message);

            // 显示重试进度
            if (attempt < RETRY_COUNT) {
              const retryMsg = `第${attempt}次尝试失败，${RETRY_COUNT - attempt}次重试剩余...`;
              console.log(`🔄 ${retryMsg}`);

              // 更新加载界面显示重试信息
              const loadingDiv = $('body').find('div:contains("正在加载聊天界面")').parent();
              if (loadingDiv.length > 0) {
                loadingDiv.find('div:contains("请稍候")').text(`${retryMsg} (${error.message})`);
              }

              await new Promise(resolve => setTimeout(resolve, 1500));
            } else {
              // 最后一次尝试失败，抛出详细错误
              const detailedError = new Error(`所有${RETRY_COUNT}次尝试均失败`);
              detailedError.originalError = error;
              detailedError.url = url;
              throw detailedError;
            }
          }
        }
      }

      async function performInitialLoad() {
        try {
          console.log('🚀 开始加载聊天界面...');

          // 先检测网络环境
          const networkInfo = await detectNetworkEnvironment();
          console.log('🌐 网络环境:', networkInfo.status);

          // 如果检测到需要VPN但无法访问外网，提前警告
          if (networkInfo.needsVPN) {
            showErrorDialog(
              '网络访问受限',
              '检测到无法访问国外网站，目标服务器 (pages.dev) 可能需要开启梯子/VPN才能访问。',
              networkInfo.details + '\n\n建议：\n1. 开启VPN/梯子\n2. 检查防火墙设置\n3. 尝试更换网络环境'
            );
            // 仍然尝试加载，可能用户有其他方式访问
          }

          const newHtml = await fetchWithRetries(HTML_URL);

          // 验证获取的内容
          if (!newHtml || newHtml.trim().length === 0) {
            throw new Error('获取的内容为空');
          }

          if (!newHtml.includes('<script>') && !newHtml.includes('<html>')) {
            throw new Error('获取的内容格式不正确，可能不是有效的HTML');
          }

          // 保存到缓存
          await saveToCache(newHtml);

          injectAndRender(newHtml);

        } catch (error) {
          console.error('❌ 加载失败:', error);

          // 尝试从缓存加载
          const cacheLoaded = await loadFromCache();
          if (cacheLoaded) {
            showErrorDialog(
              '网络加载失败，已从缓存加载',
              '无法从网络获取最新内容，已加载缓存版本。内容可能不是最新的。',
              error.message
            );
            return;
          }

          // 获取网络诊断信息
          const diagnostics = await showNetworkDiagnostics();

          // 确定错误类型和消息
          let errorTitle = '加载失败';
          let errorMessage = '无法加载聊天界面，请检查网络连接后重试。';
          let errorDetails = error.message;

          if (error.name === 'AbortError') {
            errorTitle = '请求超时';
            errorMessage = '服务器响应超时，可能需要开启梯子/VPN访问。';
          } else if (error.message.includes('Failed to fetch')) {
            errorTitle = '网络连接失败';
            errorMessage = '无法连接到 pages.dev 服务器，可能需要梯子/VPN。';
          } else if (error.message.includes('HTTP')) {
            errorTitle = '服务器错误';
            errorMessage = '服务器返回错误响应，可能是服务器维护中。';
          } else if (error.originalError) {
            errorDetails = `${error.message}\n原始错误: ${error.originalError.message}`;
          }

          // 添加诊断信息到错误详情
          errorDetails += '\n\n系统诊断:\n' + diagnostics;

          // 显示详细的错误弹窗
          showErrorDialog(errorTitle, errorMessage, errorDetails);

          // 同时显示简化的错误页面
          $('body').html(`
            <div style="display: flex; align-items: center; justify-content: center; height: 100vh; font-family: Arial, sans-serif;">
              <div style="text-align: center; color: #e74c3c;">
                <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                <div style="font-size: 18px; margin-bottom: 10px;">${errorTitle}</div>
                <div style="font-size: 14px; color: #666; margin-bottom: 20px;">${errorMessage}</div>
                <div style="margin-bottom: 20px;">
                  <button onclick="location.reload()" style="padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                    重新加载
                  </button>
                  <button onclick="showNetworkDiagnostics().then(d => showErrorDialog('网络诊断', '详细的网络环境检测结果', d))" style="padding: 10px 20px; background: #95a5a6; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    网络诊断
                  </button>
                </div>
                <div style="font-size: 12px; color: #999; margin-bottom: 10px;">
                  URL: ${HTML_URL}
                </div>
                <div style="font-size: 12px; color: #f39c12;">
                  💡 提示：如果是 pages.dev 域名，可能需要开启梯子/VPN
                </div>
              </div>
            </div>
          `);
        }
      }

      // 定期保存状态（每30秒）
      setInterval(() => {
        saveCurrentChatState();
      }, 30000);

      // 页面卸载前保存状态
      $(window).on('beforeunload', function() {
        saveCurrentChatState();
      });

      // 监听页面可见性变化，在页面隐藏时保存状态
      document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
          saveCurrentChatState();
        }
      });

      // SillyTavern特殊检测
      function checkSillyTavernEnvironment() {
        const issues = [];

        // 检查是否在正确的ST环境中
        if (window.self === window.top) {
          issues.push('⚠️ 警告: 似乎不在SillyTavern的iframe环境中运行');
        }

        // 检查ST特有的全局变量
        const stGlobals = ['SillyTavern', 'characters', 'chat', 'this_chid'];
        const missingGlobals = stGlobals.filter(global => typeof window[global] === 'undefined');
        if (missingGlobals.length > 0) {
          issues.push(`⚠️ 缺少ST全局变量: ${missingGlobals.join(', ')}`);
        }

        // 检查模板变量是否被正确替换
        const templateVars = ['{{char}}', '{{user}}', '{{userAvatarPath}}', '{{charAvatarPath}}'];
        const unreplacedVars = templateVars.filter(tvar => injectionLine.includes(tvar));
        if (unreplacedVars.length > 0) {
          issues.push(`❌ 模板变量未被替换: ${unreplacedVars.join(', ')}`);
          issues.push('这通常意味着文件不是通过SillyTavern的状态栏功能加载的');
        }

        return issues;
      }

      async function main() {
        try {
          // 检查基本环境
          if (typeof $ === 'undefined') {
            throw new Error('jQuery未加载');
          }

          // SillyTavern环境检测
          const stIssues = checkSillyTavernEnvironment();
          if (stIssues.length > 0) {
            console.warn('SillyTavern环境问题:', stIssues);
            showErrorDialog(
              'SillyTavern环境问题',
              '检测到可能的环境配置问题，这可能影响功能正常运行。',
              stIssues.join('\n')
            );
            // 不要return，继续尝试加载
          }

          if (!navigator.onLine) {
            showErrorDialog(
              '网络连接问题',
              '设备当前处于离线状态，请检查网络连接。',
              '设备离线状态'
            );
            return;
          }

          // 首先尝试从缓存加载
          const cacheLoaded = await loadFromCache();
          if (cacheLoaded) {
            console.log('✅ 从缓存成功加载，在后台检查更新...');
            // 在后台检查更新
            setTimeout(async () => {
              try {
                const newHtml = await fetchWithRetries(HTML_URL);
                if (newHtml) {
                  await saveToCache(newHtml);
                  console.log('🔄 后台更新完成，下次访问将使用新版本');
                }
              } catch (error) {
                console.log('🔄 后台更新失败，继续使用缓存版本:', error.message);
              }
            }, 1000);
            return;
          }

          // 显示加载界面
          $('body').html(`
            <div style="display: flex; align-items: center; justify-content: center; height: 100vh; font-family: Arial, sans-serif;">
              <div style="text-align: center;">
                <div style="font-size: 24px; margin-bottom: 10px;">🐱</div>
                <div style="font-size: 18px; margin-bottom: 10px;">正在加载聊天界面...</div>
                <div style="font-size: 14px; color: #666;">请稍候，正在从服务器获取最新内容</div>
                <div style="margin-top: 20px;">
                  <div style="display: inline-block; width: 20px; height: 20px; border: 2px solid #3498db; border-radius: 50%; border-top-color: transparent; animation: spin 1s linear infinite;"></div>
                </div>
                <div style="margin-top: 15px; font-size: 12px; color: #999;">
                  正在连接: ${HTML_URL}
                </div>
                <div style="margin-top: 10px;">
                  <button onclick="showErrorDialog('系统诊断', '点击查看详细的系统诊断信息', showNetworkDiagnostics())"
                          style="padding: 5px 10px; background: #95a5a6; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;">
                    系统诊断
                  </button>
                </div>
                ${stIssues.length > 0 ? `
                  <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; font-size: 12px;">
                    <div style="color: #856404; font-weight: bold;">⚠️ 环境警告</div>
                    <div style="color: #856404; margin-top: 5px;">${stIssues.slice(0, 2).join('<br>')}</div>
                  </div>
                ` : ''}
              </div>
            </div>
            <style>
              @keyframes spin {
                to { transform: rotate(360deg); }
              }
            </style>
          `);

          // 添加全局错误处理
          window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error);
            showErrorDialog(
              '页面运行错误',
              '页面运行时发生错误，可能影响正常功能。',
              `${event.error.message}\n文件: ${event.filename}\n行号: ${event.lineno}`
            );
          });

          window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise拒绝:', event.reason);
            showErrorDialog(
              'Promise错误',
              '异步操作失败，可能影响页面功能。',
              event.reason.toString()
            );
          });

          performInitialLoad();

        } catch (error) {
          console.error('初始化失败:', error);
          showErrorDialog(
            '初始化失败',
            '页面初始化过程中发生错误。',
            error.message
          );
        }
      }
      
      main();
    });
  </script>
</body>
</html>
