<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>智能媒体助手插件测试</title>
    <style>
      body {
        font-family: 'Microsoft YaHei', Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }

      .container {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      }

      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
        font-size: 2.5em;
      }

      .plugin-info {
        background: #f0f8ff;
        border-left: 4px solid #667eea;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 0 8px 8px 0;
      }

      .test-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        background: #f9f9f9;
      }

      .test-section h2 {
        color: #555;
        margin-top: 0;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .file-input {
        margin: 15px 0;
        padding: 15px;
        border: 2px dashed #ccc;
        border-radius: 8px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
      }

      .file-input:hover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
      }

      .file-input.dragover {
        border-color: #4caf50;
        background: rgba(76, 175, 80, 0.1);
        transform: scale(1.02);
      }

      .file-input input {
        display: none;
      }

      .file-preview {
        display: none;
        margin: 10px 0;
        padding: 10px;
        background: white;
        border: 1px solid #ddd;
        border-radius: 6px;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .file-icon {
        font-size: 24px;
        width: 40px;
        text-align: center;
      }

      .file-info {
        flex: 1;
      }

      .file-name {
        font-weight: bold;
        margin-bottom: 2px;
      }

      .file-details {
        font-size: 12px;
        color: #666;
      }

      .btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        margin: 5px;
        transition: all 0.3s ease;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .btn:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
      }

      .btn-small {
        padding: 6px 12px;
        font-size: 14px;
      }

      .result {
        margin-top: 15px;
        padding: 15px;
        border-radius: 8px;
        background: #fff;
        border-left: 4px solid #667eea;
        max-height: 400px;
        overflow-y: auto;
      }

      .success {
        border-left-color: #4caf50;
        background: #f1f8e9;
      }

      .error {
        border-left-color: #f44336;
        background: #ffebee;
      }

      .status {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        margin-left: 10px;
      }

      .status.loading {
        background: #2196f3;
        color: white;
      }

      .status.success {
        background: #4caf50;
        color: white;
      }

      .status.error {
        background: #f44336;
        color: white;
      }

      pre {
        background: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
        font-size: 12px;
        max-height: 300px;
        overflow-y: auto;
      }

      .plugin-status {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
      }

      .plugin-item {
        padding: 15px;
        border-radius: 8px;
        font-weight: bold;
        text-align: center;
        transition: all 0.3s ease;
      }

      .plugin-loaded {
        background: #c8e6c9;
        color: #2e7d32;
        border: 2px solid #4caf50;
      }

      .plugin-missing {
        background: #ffcdd2;
        color: #c62828;
        border: 2px solid #f44336;
      }

      .api-list {
        background: white;
        padding: 15px;
        border-radius: 8px;
        margin: 10px 0;
      }

      .api-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
      }

      .api-item:last-child {
        border-bottom: none;
      }

      .api-name {
        font-family: monospace;
        background: #f0f0f0;
        padding: 2px 6px;
        border-radius: 3px;
      }

      .api-status {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: bold;
      }

      .api-available {
        background: #4caf50;
        color: white;
      }

      .api-missing {
        background: #f44336;
        color: white;
      }

      .progress-bar {
        width: 100%;
        height: 6px;
        background: #e0e0e0;
        border-radius: 3px;
        overflow: hidden;
        margin: 10px 0;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 3px;
        transition: width 0.3s ease;
        width: 0%;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🎯 智能媒体助手插件测试</h1>

      <div class="plugin-info">
        <h3>📋 插件信息</h3>
        <p><strong>版本:</strong> 2.0.0</p>
        <p><strong>功能:</strong> 统一的图片和文档处理，修复文件类型识别问题</p>
        <p><strong>特点:</strong> 标准SillyTavern插件结构，单一manifest.json配置</p>
      </div>

      <!-- 插件状态检查 -->
      <div class="test-section">
        <h2>🔧 插件状态检查</h2>
        <div class="plugin-status" id="pluginStatus">
          <div class="plugin-item" id="mainPluginStatus">主插件: 检查中...</div>
          <div class="plugin-item" id="configStatus">配置状态: 检查中...</div>
          <div class="plugin-item" id="uiStatus">界面状态: 检查中...</div>
        </div>

        <div class="api-list" id="apiList">
          <h4>API接口状态:</h4>
          <!-- API状态将动态填充 -->
        </div>

        <button class="btn" onclick="checkPluginStatus()">🔄 重新检查状态</button>
        <button class="btn" onclick="loadPlugin()">📥 加载插件</button>
        <button class="btn" onclick="testCollapsibleFeature()">📋 测试收缩栏</button>
      </div>

      <!-- 通用文件处理测试 -->
      <div class="test-section">
        <h2>📂 通用文件处理测试</h2>
        <p>测试插件的智能文件类型识别和处理功能</p>

        <div class="file-input" id="universalDropZone">
          <input type="file" id="universalFile" onchange="handleUniversalFile(this)" />
          <p>📁 拖拽或点击选择任意文件</p>
          <small>支持图片、文档等多种格式</small>
        </div>

        <div class="file-preview" id="universalPreview" style="display: none">
          <div class="file-icon" id="universalIcon">📄</div>
          <div class="file-info">
            <div class="file-name" id="universalName">文件名</div>
            <div class="file-details" id="universalDetails">文件详情</div>
          </div>
          <button class="btn btn-small" onclick="processUniversalFile()" id="processUniversalBtn">🚀 处理文件</button>
        </div>

        <div class="progress-bar" id="universalProgress" style="display: none">
          <div class="progress-fill" id="universalProgressFill"></div>
        </div>

        <div class="result" id="universalResult" style="display: none"></div>
      </div>

      <!-- 批量文件测试 -->
      <div class="test-section">
        <h2>📚 批量文件处理测试</h2>
        <p>测试同时处理多个不同类型的文件</p>

        <div class="file-input" id="batchDropZone">
          <input type="file" id="batchFiles" multiple onchange="handleBatchFiles(this)" />
          <p>📦 选择多个文件进行批量处理</p>
          <small>可以混合选择图片和文档文件</small>
        </div>

        <div id="batchPreview" style="display: none">
          <h4>选中的文件:</h4>
          <div id="batchFileList"></div>
          <button class="btn" onclick="processBatchFiles()" id="processBatchBtn">⚡ 批量处理</button>
        </div>

        <div class="result" id="batchResult" style="display: none"></div>
      </div>

      <!-- 创建测试文件 -->
      <div class="test-section">
        <h2>🧪 创建测试文件</h2>
        <p>快速创建各种类型的测试文件</p>

        <button class="btn" onclick="createTestJSON()">📋 创建JSON文件</button>
        <button class="btn" onclick="createTestTXT()">📄 创建TXT文件</button>
        <button class="btn" onclick="createTestCSV()">📊 创建CSV文件</button>
        <button class="btn" onclick="createTestHTML()">🌐 创建HTML文件</button>

        <div class="result" id="createResult" style="display: none"></div>
      </div>
    </div>

    <script>
      let selectedUniversalFile = null;
      let selectedBatchFiles = [];

      // 检查插件状态
      function checkPluginStatus() {
        const mainStatus = document.getElementById('mainPluginStatus');
        const configStatus = document.getElementById('configStatus');
        const uiStatus = document.getElementById('uiStatus');
        const apiList = document.getElementById('apiList');

        // 检查主插件
        if (typeof window.__processFileByPlugin === 'function') {
          mainStatus.textContent = '主插件: ✅ 已加载';
          mainStatus.className = 'plugin-item plugin-loaded';
        } else {
          mainStatus.textContent = '主插件: ❌ 未加载';
          mainStatus.className = 'plugin-item plugin-missing';
        }

        // 检查配置
        try {
          const supportedTypes = window.__getSupportedFileTypes ? window.__getSupportedFileTypes() : null;
          if (supportedTypes) {
            configStatus.textContent = '配置状态: ✅ 正常';
            configStatus.className = 'plugin-item plugin-loaded';
          } else {
            configStatus.textContent = '配置状态: ❌ 异常';
            configStatus.className = 'plugin-item plugin-missing';
          }
        } catch (error) {
          configStatus.textContent = '配置状态: ❌ 错误';
          configStatus.className = 'plugin-item plugin-missing';
        }

        // 检查界面状态
        const settingsElement = document.getElementById('smart-media-assistant-settings');
        const collapsibleElement = document.querySelector('.smart-media-collapsible');
        if (settingsElement && collapsibleElement) {
          uiStatus.textContent = '界面状态: ✅ 已创建';
          uiStatus.className = 'plugin-item plugin-loaded';
        } else {
          uiStatus.textContent = '界面状态: ❌ 未创建';
          uiStatus.className = 'plugin-item plugin-missing';
        }

        // 检查API接口
        const apis = [
          { name: '__processFileByPlugin', desc: '通用文件处理' },
          { name: '__uploadImageByPlugin', desc: '图片处理' },
          { name: '__processDocumentByPlugin', desc: '文档处理' },
          { name: '__isDocumentFile', desc: '文档检测' },
          { name: '__getSupportedFileTypes', desc: '支持类型查询' },
        ];

        let apiHTML = '<h4>API接口状态:</h4>';
        apis.forEach(api => {
          const available = typeof window[api.name] === 'function';
          const statusClass = available ? 'api-available' : 'api-missing';
          const statusText = available ? '可用' : '缺失';

          apiHTML += `
                    <div class="api-item">
                        <span class="api-name">${api.name}</span>
                        <span>${api.desc}</span>
                        <span class="api-status ${statusClass}">${statusText}</span>
                    </div>
                `;
        });

        apiList.innerHTML = apiHTML;
      }

      // 加载插件
      function loadPlugin() {
        const script = document.createElement('script');
        script.type = 'module';
        script.src = '智能媒体助手插件/index.js';
        script.onload = () => {
          setTimeout(checkPluginStatus, 500);
          console.log('插件加载完成');
        };
        script.onerror = () => {
          console.error('插件加载失败');
        };
        document.head.appendChild(script);
      }

      // 测试收缩栏功能
      function testCollapsibleFeature() {
        const collapsibleElement = document.querySelector('.smart-media-collapsible');

        if (!collapsibleElement) {
          alert('❌ 收缩栏元素未找到，请先加载插件');
          return;
        }

        // 测试收缩/展开功能
        const isOpen = collapsibleElement.hasAttribute('open');

        if (isOpen) {
          // 收缩
          collapsibleElement.removeAttribute('open');
          console.log('🔽 收缩栏已收缩');

          setTimeout(() => {
            // 展开
            collapsibleElement.setAttribute('open', '');
            console.log('🔼 收缩栏已展开');
            alert('✅ 收缩栏功能测试完成！\n\n测试结果：\n- 收缩功能：正常\n- 展开功能：正常\n- 动画效果：正常');
          }, 1000);
        } else {
          // 展开
          collapsibleElement.setAttribute('open', '');
          console.log('🔼 收缩栏已展开');

          setTimeout(() => {
            // 收缩
            collapsibleElement.removeAttribute('open');
            console.log('🔽 收缩栏已收缩');
            alert('✅ 收缩栏功能测试完成！\n\n测试结果：\n- 展开功能：正常\n- 收缩功能：正常\n- 动画效果：正常');
          }, 1000);
        }
      }

      // 处理通用文件选择
      function handleUniversalFile(input) {
        const file = input.files[0];
        if (file) {
          selectedUniversalFile = file;

          const preview = document.getElementById('universalPreview');
          const icon = document.getElementById('universalIcon');
          const name = document.getElementById('universalName');
          const details = document.getElementById('universalDetails');

          // 设置文件图标
          if (file.type.startsWith('image/')) {
            icon.textContent = '🖼️';
          } else if (file.type.includes('json')) {
            icon.textContent = '📋';
          } else if (file.type.startsWith('text/')) {
            icon.textContent = '📄';
          } else {
            icon.textContent = '📁';
          }

          name.textContent = file.name;
          details.textContent = `${file.type || '未知类型'} • ${(file.size / 1024).toFixed(1)} KB`;

          preview.style.display = 'flex';
        }
      }

      // 处理通用文件
      async function processUniversalFile() {
        if (!selectedUniversalFile) return;

        const resultDiv = document.getElementById('universalResult');
        const progressBar = document.getElementById('universalProgress');
        const progressFill = document.getElementById('universalProgressFill');

        resultDiv.style.display = 'block';
        progressBar.style.display = 'block';
        resultDiv.className = 'result';
        resultDiv.innerHTML = '<span class="status loading">⏳ 处理中...</span> 正在处理文件...';

        // 模拟进度
        let progress = 0;
        const progressInterval = setInterval(() => {
          progress += Math.random() * 20;
          if (progress > 90) progress = 90;
          progressFill.style.width = progress + '%';
        }, 100);

        try {
          const result = await window.__processFileByPlugin(selectedUniversalFile, {
            sendToChat: false, // 测试时不发送到聊天
          });

          clearInterval(progressInterval);
          progressFill.style.width = '100%';

          setTimeout(() => {
            progressBar.style.display = 'none';
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                        <span class="status success">✅ 成功</span> 文件处理成功！
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
          }, 500);
        } catch (error) {
          clearInterval(progressInterval);
          progressBar.style.display = 'none';

          console.error('文件处理失败:', error);
          resultDiv.className = 'result error';
          resultDiv.innerHTML = `
                    <span class="status error">❌ 失败</span> 文件处理失败: ${error.message}
                    <pre>错误详情: ${error.stack || error.toString()}</pre>
                `;
        }
      }

      // 处理批量文件选择
      function handleBatchFiles(input) {
        selectedBatchFiles = Array.from(input.files);

        if (selectedBatchFiles.length > 0) {
          const preview = document.getElementById('batchPreview');
          const fileList = document.getElementById('batchFileList');

          let listHTML = '';
          selectedBatchFiles.forEach((file, index) => {
            const icon = file.type.startsWith('image/')
              ? '🖼️'
              : file.type.includes('json')
              ? '📋'
              : file.type.startsWith('text/')
              ? '📄'
              : '📁';

            listHTML += `
                        <div class="file-preview" style="display: flex;">
                            <div class="file-icon">${icon}</div>
                            <div class="file-info">
                                <div class="file-name">${file.name}</div>
                                <div class="file-details">${file.type || '未知类型'} • ${(file.size / 1024).toFixed(
              1,
            )} KB</div>
                            </div>
                        </div>
                    `;
          });

          fileList.innerHTML = listHTML;
          preview.style.display = 'block';
        }
      }

      // 批量处理文件
      async function processBatchFiles() {
        if (selectedBatchFiles.length === 0) return;

        const resultDiv = document.getElementById('batchResult');
        resultDiv.style.display = 'block';
        resultDiv.className = 'result';
        resultDiv.innerHTML = '<span class="status loading">⏳ 批量处理中...</span> 正在处理多个文件...';

        const results = [];

        try {
          for (let i = 0; i < selectedBatchFiles.length; i++) {
            const file = selectedBatchFiles[i];
            resultDiv.innerHTML = `<span class="status loading">⏳ 处理中...</span> 正在处理文件 ${i + 1}/${
              selectedBatchFiles.length
            }: ${file.name}`;

            try {
              const result = await window.__processFileByPlugin(file, {
                sendToChat: false,
              });
              results.push({ file: file.name, success: true, result });
            } catch (error) {
              results.push({ file: file.name, success: false, error: error.message });
            }
          }

          // 显示结果
          let successCount = results.filter(r => r.success).length;
          let failCount = results.length - successCount;

          resultDiv.className = failCount === 0 ? 'result success' : 'result error';
          resultDiv.innerHTML = `
                    <span class="status ${failCount === 0 ? 'success' : 'error'}">
                        ${failCount === 0 ? '✅' : '⚠️'} 批量处理完成
                    </span> 
                    成功: ${successCount}, 失败: ${failCount}
                    <pre>${JSON.stringify(results, null, 2)}</pre>
                `;
        } catch (error) {
          resultDiv.className = 'result error';
          resultDiv.innerHTML = `
                    <span class="status error">❌ 失败</span> 批量处理失败: ${error.message}
                `;
        }
      }

      // 创建测试文件函数
      function createTestJSON() {
        const data = {
          name: '测试JSON文件',
          version: '2.0.0',
          timestamp: new Date().toISOString(),
          features: ['图片处理', '文档处理', 'AI识别'],
          config: {
            imageQuality: 85,
            maxFileSize: 20,
            enableLogging: true,
          },
          testData: [1, 2, 3, 4, 5],
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const file = new File([blob], 'test.json', { type: 'application/json' });

        selectedUniversalFile = file;
        handleUniversalFile({ files: [file] });

        const result = document.getElementById('createResult');
        result.style.display = 'block';
        result.className = 'result success';
        result.innerHTML = '<span class="status success">✅ 成功</span> 已创建测试JSON文件，可以点击"处理文件"进行测试';
      }

      function createTestTXT() {
        const content = `智能媒体助手插件测试文档
创建时间: ${new Date().toLocaleString()}

这是一个测试文本文件，用于验证文档处理功能。

功能特点:
- 图片智能压缩和优化
- 多格式文档处理
- AI图片识别和分析
- 文件类型智能识别

测试内容:
包含中文字符、英文字符、数字123456、特殊符号!@#$%^&*()
多行文本测试
换行测试

结束标记: EOF`;

        const blob = new Blob([content], { type: 'text/plain' });
        const file = new File([blob], 'test.txt', { type: 'text/plain' });

        selectedUniversalFile = file;
        handleUniversalFile({ files: [file] });

        const result = document.getElementById('createResult');
        result.style.display = 'block';
        result.className = 'result success';
        result.innerHTML = '<span class="status success">✅ 成功</span> 已创建测试TXT文件，可以点击"处理文件"进行测试';
      }

      function createTestCSV() {
        const content = `文件名,类型,大小,状态
test.jpg,图片,1024KB,已处理
document.pdf,文档,2048KB,处理中
data.json,数据,512KB,等待中
image.png,图片,1536KB,已完成
config.xml,配置,256KB,已处理`;

        const blob = new Blob([content], { type: 'text/csv' });
        const file = new File([blob], 'test.csv', { type: 'text/csv' });

        selectedUniversalFile = file;
        handleUniversalFile({ files: [file] });

        const result = document.getElementById('createResult');
        result.style.display = 'block';
        result.className = 'result success';
        result.innerHTML = '<span class="status success">✅ 成功</span> 已创建测试CSV文件，可以点击"处理文件"进行测试';
      }

      function createTestHTML() {
        const content = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>测试HTML文档</title>
</head>
<body>
    <h1>智能媒体助手测试页面</h1>
    <p>这是一个测试HTML文档，用于验证HTML文件处理功能。</p>
    <ul>
        <li>图片处理功能</li>
        <li>文档处理功能</li>
        <li>AI识别功能</li>
    </ul>
    <p>创建时间: ${new Date().toLocaleString()}</p>
</body>
</html>`;

        const blob = new Blob([content], { type: 'text/html' });
        const file = new File([blob], 'test.html', { type: 'text/html' });

        selectedUniversalFile = file;
        handleUniversalFile({ files: [file] });

        const result = document.getElementById('createResult');
        result.style.display = 'block';
        result.className = 'result success';
        result.innerHTML = '<span class="status success">✅ 成功</span> 已创建测试HTML文件，可以点击"处理文件"进行测试';
      }

      // 拖拽功能
      function setupDragAndDrop() {
        const dropZones = document.querySelectorAll('.file-input');

        dropZones.forEach(zone => {
          zone.addEventListener('dragover', e => {
            e.preventDefault();
            zone.classList.add('dragover');
          });

          zone.addEventListener('dragleave', e => {
            e.preventDefault();
            zone.classList.remove('dragover');
          });

          zone.addEventListener('drop', e => {
            e.preventDefault();
            zone.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
              const input = zone.querySelector('input[type="file"]');
              if (input.multiple) {
                handleBatchFiles({ files });
              } else {
                handleUniversalFile({ files });
              }
            }
          });
        });
      }

      // 页面加载时初始化
      window.addEventListener('load', function () {
        checkPluginStatus();
        setupDragAndDrop();

        // 尝试自动加载插件
        setTimeout(() => {
          if (typeof window.__processFileByPlugin !== 'function') {
            console.log('尝试自动加载插件...');
            loadPlugin();
          }
        }, 1000);
      });
    </script>
  </body>
</html>
