<details>
  <summary>◦ PSYCHOLOGICAL DISSECTION</summary>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Creepster&family=Nosifer&family=Butcherman&family=Eater&family=Griffy:wght@400&family=Cinzel:wght@400;500;600&family=Crimson+Text:ital,wght@0,400;0,600;1,400&display=swap');

    @keyframes subtle-pulse {
      0%,
      100% {
        border-color: rgba(220, 20, 60, 0.3);
      }
      50% {
        border-color: rgba(220, 20, 60, 0.6);
      }
    }

    @keyframes fade-in {
      0% {
        opacity: 0;
        transform: translateY(5px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes dark-whisper {
      0%,
      100% {
        text-shadow: 0 0 3px rgba(139, 0, 0, 0.3);
      }
      50% {
        text-shadow: 0 0 6px rgba(139, 0, 0, 0.6), 0 0 12px rgba(139, 0, 0, 0.2);
      }
    }

    @keyframes psychological-scan {
      0% {
        box-shadow: inset 0 0 0 0 rgba(139, 0, 0, 0);
      }
      50% {
        box-shadow: inset 0 0 8px 0 rgba(139, 0, 0, 0.1);
      }
      100% {
        box-shadow: inset 0 0 0 0 rgba(139, 0, 0, 0);
      }
    }

    @keyframes mind-probe {
      0%,
      90%,
      100% {
        opacity: 0;
      }
      95% {
        opacity: 0.15;
      }
    }

    @keyframes data-corruption {
      0%,
      100% {
        transform: translateX(0) skew(0deg);
        filter: hue-rotate(0deg);
      }
      25% {
        transform: translateX(-1px) skew(-0.5deg);
        filter: hue-rotate(5deg);
      }
      75% {
        transform: translateX(1px) skew(0.5deg);
        filter: hue-rotate(-5deg);
      }
    }

    @keyframes shadow-creep {
      0% {
        box-shadow: inset 0 0 0 0 rgba(139, 0, 0, 0);
      }
      100% {
        box-shadow: inset 0 0 20px 0 rgba(139, 0, 0, 0.2);
      }
    }

    @keyframes gothic-glow {
      0%,
      100% {
        text-shadow: 0 0 5px rgba(220, 20, 60, 0.3), 0 1px 2px rgba(0, 0, 0, 0.8);
      }
      50% {
        text-shadow: 0 0 10px rgba(220, 20, 60, 0.5), 0 0 15px rgba(220, 20, 60, 0.2), 0 1px 2px rgba(0, 0, 0, 0.8);
      }
    }

    @keyframes blood-drip {
      0% {
        transform: translateY(-2px);
        opacity: 0.8;
      }
      50% {
        transform: translateY(0px);
        opacity: 1;
      }
      100% {
        transform: translateY(1px);
        opacity: 0.8;
      }
    }

    @keyframes whisper-float {
      0%,
      100% {
        transform: translateY(0px) rotate(0deg);
      }
      33% {
        transform: translateY(-1px) rotate(0.5deg);
      }
      66% {
        transform: translateY(1px) rotate(-0.5deg);
      }
    }

    .status-card-container {
      background-image: url('https://files.catbox.moe/jdvsy1.png');
      background-size: cover;
      background-position: center;
      background-attachment: fixed;
      border: 1px solid rgba(220, 20, 60, 0.3);
      border-radius: 4px;
      padding: 16px;
      position: relative;
      color: #e8e8e8;
      font-family: 'Inter', sans-serif;
      font-size: 0.9em;
      animation: subtle-pulse 4s ease-in-out infinite, psychological-scan 8s ease-in-out infinite;
      backdrop-filter: blur(8px);
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.4);
      overflow: hidden;
    }

    .status-card-container::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(10, 10, 15, 0.9), rgba(20, 20, 30, 0.85));
      z-index: 1;
      transition: background 0.3s ease;
    }

    .status-card-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(139, 0, 0, 0.4), transparent);
      animation: mind-probe 12s linear infinite;
    }

    .status-card-container:hover {
      border-color: rgba(220, 20, 60, 0.5);
      box-shadow: 0 4px 20px rgba(220, 20, 60, 0.1);
      animation: subtle-pulse 2s ease-in-out infinite, psychological-scan 4s ease-in-out infinite;
    }

    .status-card-container:hover::after {
      background: linear-gradient(135deg, rgba(10, 10, 15, 0.8), rgba(20, 20, 30, 0.75));
    }
    .status-card-content-wrapper {
      position: relative;
      display: grid;
      grid-template-columns: 1fr;
      gap: 6px;
      z-index: 2;
    }

    .status-item {
      background: rgba(20, 20, 25, 0.6);
      border-left: 2px solid rgba(220, 20, 60, 0.4);
      padding: 8px 12px;
      margin: 2px 0;
      transition: all 0.2s ease;
      animation: fade-in 0.3s ease;
      animation-delay: calc(var(--index) * 0.05s);
      animation-fill-mode: both;
    }

    .status-item:nth-child(1) {
      --index: 1;
    }
    .status-item:nth-child(2) {
      --index: 2;
    }
    .status-item:nth-child(3) {
      --index: 3;
    }
    .status-item:nth-child(4) {
      --index: 4;
    }
    .status-item:nth-child(5) {
      --index: 5;
    }
    .status-item:nth-child(6) {
      --index: 6;
    }

    .status-item:hover {
      background: rgba(30, 30, 40, 0.8);
      border-left-color: rgba(220, 20, 60, 0.7);
      transform: translateX(2px);
      animation: fade-in 0.3s ease, dark-whisper 3s ease-in-out infinite, shadow-creep 0.5s ease forwards;
      cursor: pointer;
    }

    .status-item:nth-child(6) .status-content {
      animation: dark-whisper 4s ease-in-out infinite;
    }

    .status-item:active {
      animation: data-corruption 0.2s ease;
      transform: translateX(4px) scale(0.98);
    }

    .status-content:hover {
      color: #f0f0f0;
      transition: color 0.2s ease;
    }

    .status-label {
      font-family: 'Cinzel', serif;
      font-weight: 500;
      color: #ff4757;
      font-size: 0.8em;
      margin-bottom: 4px;
      display: block;
      text-transform: uppercase;
      letter-spacing: 1px;
      position: relative;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8), 0 0 5px rgba(255, 71, 87, 0.3);
      animation: gothic-glow 3s ease-in-out infinite;
    }

    .status-label::after {
      content: '●';
      position: absolute;
      right: -12px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 0.6em;
      color: rgba(139, 0, 0, 0.7);
      animation: blood-drip 2s ease-in-out infinite;
      transition: all 0.2s ease;
    }

    .status-item:hover .status-label::after {
      color: rgba(220, 20, 60, 1);
      font-size: 0.8em;
      text-shadow: 0 0 8px rgba(220, 20, 60, 0.6);
      animation: blood-drip 1s ease-in-out infinite;
    }

    .mind-whisper {
      border-left-color: rgba(139, 0, 0, 0.6) !important;
    }

    .mind-whisper:hover {
      border-left-color: rgba(139, 0, 0, 0.9) !important;
      background: rgba(40, 20, 25, 0.8) !important;
    }

    .mind-whisper .status-content {
      font-style: italic;
      color: #dda0dd;
      font-family: 'Griffy', cursive;
      animation: dark-whisper 3s ease-in-out infinite, whisper-float 5s ease-in-out infinite;
    }

    .status-content {
      font-family: 'Crimson Text', serif;
      font-size: 0.9em;
      font-weight: 400;
      color: #f1f2f6;
      line-height: 1.5;
      letter-spacing: 0.3px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.9);
      animation: whisper-float 4s ease-in-out infinite;
    }
  </style>
  <div class="status-card-container">
    <div class="status-card-content-wrapper">
      <div class="status-item">
        <span class="status-label">⚡ 身之位</span>
        <div class="status-content">$1</div>
      </div>
      <div class="status-item">
        <span class="status-label">🎭 我是谁？</span>
        <div class="status-content">$2</div>
      </div>
      <div class="status-item">
        <span class="status-label">👤 衣着</span>
        <div class="status-content">$3</div>
      </div>
      <div class="status-item">
        <span class="status-label">🔗 {{user}}和他是……？</span>
        <div class="status-content">$4</div>
      </div>
      <div class="status-item">
        <span class="status-label">📊 {{user}}这个人怎么样？</span>
        <div class="status-content">$5</div>
      </div>
      <div class="status-item mind-whisper">
        <span class="status-label">🧠心之声</span>
        <div class="status-content">$6</div>
      </div>
    </div>
  </div>
</details>
