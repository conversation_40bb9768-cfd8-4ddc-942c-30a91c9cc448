<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>同层私聊文件上传测试</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background: #f5f5f5;
      }

      .container {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }

      .test-info {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;
        color: #1976d2;
      }

      .feature-list {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 15px;
        margin: 15px 0;
      }

      .feature-item {
        display: flex;
        align-items: center;
        margin: 8px 0;
        font-size: 14px;
      }

      .feature-icon {
        margin-right: 10px;
        font-size: 16px;
      }

      .demo-section {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
        background: #fafafa;
      }

      .demo-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
        font-size: 16px;
      }

      .demo-desc {
        color: #666;
        font-size: 13px;
        margin-bottom: 15px;
        line-height: 1.4;
      }

      .demo-image {
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 10px;
        background: white;
        margin: 10px 0;
        text-align: center;
        color: #666;
        font-style: italic;
      }

      .code-block {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 12px;
        font-family: monospace;
        font-size: 12px;
        color: #495057;
        margin: 10px 0;
        white-space: pre-wrap;
        overflow-x: auto;
      }

      .highlight {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 10px;
        margin: 10px 0;
        color: #856404;
      }

      .success {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
      }

      .warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
      }

      .btn {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
        text-decoration: none;
        display: inline-block;
      }

      .btn:hover {
        background: #0056b3;
      }

      .btn-success {
        background: #28a745;
      }

      .btn-success:hover {
        background: #218838;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>📄 同层私聊文件上传功能测试</h1>

      <div class="test-info">
        <h3>🎯 功能概述</h3>
        <p>同层私聊喵喵喵2现在支持以下新功能：</p>
        <ul>
          <li><strong>📄 文件上传</strong>：支持文字描述和真实文件上传两种方式</li>
          <li><strong>🤖 文档AI分析</strong>：自动分析上传的文档内容</li>
          <li><strong>😄 表情包AI识图</strong>：发送表情包时自动识别图片内容</li>
          <li><strong>📱 移动端优化</strong>：底部弹出式界面，更适合手机使用</li>
        </ul>
      </div>
    </div>

    <div class="container">
      <h2>✨ 新增功能特性</h2>

      <div class="feature-list">
        <div class="feature-item">
          <span class="feature-icon">📁</span>
          <span>支持多种文档格式：.txt, .md, .json, .csv, .html, .xml, .rtf, .pdf, .doc, .docx</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">🤖</span>
          <span>集成AI分析功能，自动分析文档内容并提供摘要</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">🎨</span>
          <span>美观的文档消息渲染，显示文件信息、内容预览和AI分析结果</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">📱</span>
          <span>支持拖拽上传，移动端友好的界面设计</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">🔗</span>
          <span>完美集成ctrl的插件（bug大杂烩），利用SillyTavern的AI接口</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">😄</span>
          <span>表情包AI识图：自动识别表情包内容，为没有备注的表情包生成描述</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">⚙️</span>
          <span>可配置设置：在表情包面板的"设置"标签页中开启/关闭AI识图功能</span>
        </div>
      </div>
    </div>

    <div class="container">
      <h2>🔧 使用方法</h2>

      <div class="demo-section">
        <div class="demo-title">1. 点击文件按钮</div>
        <div class="demo-desc">在聊天界面点击文件按钮（📄），会弹出文件发送选择界面</div>
        <div class="demo-image">[文件按钮点击示意图]</div>
      </div>

      <div class="demo-section">
        <div class="demo-title">2. 选择发送方式</div>
        <div class="demo-desc">
          <strong>选项A：文字描述</strong><br />
          - 选择文件格式（txt、word、pdf等）<br />
          - 输入文件内容描述<br />
          - 点击发送<br /><br />

          <strong>选项B：上传真实文件</strong><br />
          - 点击上传区域或拖拽文件<br />
          - 选择是否启用AI分析<br />
          - 添加文件说明（可选）<br />
          - 点击发送
        </div>
      </div>

      <div class="demo-section">
        <div class="demo-title">3. 文档处理流程</div>
        <div class="demo-desc">真实文件上传后的处理流程：</div>
        <div class="code-block">文件选择 → 插件处理 → 文档解析 → AI分析（可选） → 消息发送 → 界面渲染</div>
      </div>
    </div>

    <div class="container">
      <h2>📋 消息格式示例</h2>

      <div class="demo-section">
        <div class="demo-title">文字描述消息</div>
        <div class="code-block">
          { "type": "file", "fileFormat": "txt", "fileContent": "这是一个文本文件的内容..." }
        </div>
      </div>

      <div class="demo-section">
        <div class="demo-title">真实文档消息</div>
        <div class="code-block">
          { "type": "document", "fileName": "报告.pdf", "fileSize": 1024000, "fileType": "application/pdf", "content":
          "文档的实际内容...", "description": "这是一份重要报告", "aiAnalysis": "AI分析结果：这份报告主要讨论了...",
          "enabledAI": true, "processedAt": "2024-01-01T12:00:00.000Z" }
        </div>
      </div>
    </div>

    <div class="container">
      <h2>🎨 界面效果预览</h2>

      <div class="demo-section">
        <div class="demo-title">文档消息渲染效果</div>
        <div class="demo-desc">文档消息会显示为一个美观的卡片，包含：</div>
        <ul>
          <li>📄 文件图标和基本信息（文件名、大小、格式）</li>
          <li>🤖 AI分析标识（如果启用）</li>
          <li>📝 文件说明（如果有）</li>
          <li>📄 内容预览（前200字符）</li>
          <li>🤖 AI分析结果（如果有）</li>
          <li>⏰ 处理时间戳</li>
        </ul>
      </div>
    </div>

    <div class="container">
      <h2>⚙️ 技术实现</h2>

      <div class="highlight success">
        <h4>🔗 插件集成</h4>
        <p>利用 <code>third-party-image-processor.js</code> 插件的文档处理接口：</p>
        <div class="code-block">
          // 调用插件处理文档 const result = await top.window.__processDocumentByPlugin(file, { enableAIReading: true,
          aiPrompt: '请分析这个文档的主要内容' });
        </div>
      </div>

      <div class="highlight warning">
        <h4>🤖 AI分析功能</h4>
        <p>通过插件调用SillyTavern的内置AI接口，实现文档内容的智能分析和摘要生成。</p>
      </div>

      <div class="highlight">
        <h4>📱 用户体验</h4>
        <p>保留原有的文字描述功能，同时添加真实文件上传，用户可以根据需要选择合适的方式。</p>
      </div>
    </div>

    <div class="container">
      <h2>🚀 开始测试</h2>
      <p>现在你可以打开同层私聊喵喵喵2.html文件，点击文件按钮来测试新的文件上传功能！</p>

      <a href="ctrl的前端2/同层私聊喵喵喵2.html" class="btn btn-success">打开同层私聊喵喵喵2</a>
      <a href="SillyTavern接口测试.html" class="btn">测试插件接口</a>
    </div>

    <div class="container">
      <h2>📝 注意事项</h2>
      <ul>
        <li>确保 <code>third-party-image-processor.js</code> 插件已正确加载</li>
        <li>AI分析功能需要SillyTavern的AI接口可用</li>
        <li>大文件可能需要较长的处理时间</li>
        <li>文档内容会被解析并存储在消息中</li>
        <li>支持的文件格式有限制，请查看上传提示</li>
      </ul>
    </div>
  </body>
</html>
