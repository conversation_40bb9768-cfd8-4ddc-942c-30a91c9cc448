# 同层群聊6.html 识图功能添加说明

## 需要添加的功能

基于聊x手机5.html的成功实现，为同层群聊6.html添加以下功能：

### 1. 识图API配置界面
- 三种识图模式：酒馆内置、Kimi、自定义API
- 配置界面集成到现有设置面板中
- 智能显示/隐藏不同模式的配置项

### 2. 图片上传修复
- 添加convertFileToBase64函数作为备用方案
- 修复手机端图片上传失败问题
- 保持与原有上传逻辑的兼容性

### 3. 识图功能实现
- requestVisionAnalysisWithTavern：使用酒馆内置API
- requestVisionAnalysisWithKimi：使用Kimi API
- requestVisionAnalysis：主识图函数，支持三种模式
- 智能回退机制

### 4. 用户体验优化
- 图片发送后不立即触发AI回复
- 用户手动点击AI回复按钮时才开始识图
- 清晰的状态指示器

## 实现步骤

### 步骤1：添加状态管理
已完成：在state对象中添加了识图相关状态

### 步骤2：修复AI_GENERATE函数获取
已完成：统一使用typeof generate === 'function'方式

### 步骤3：添加识图配置界面
需要在破限设置后添加识图API配置部分

### 步骤4：添加识图功能函数
- convertFileToBase64
- testKimiConnection
- showKimiTestResult
- testVisionConnection
- refreshVisionModels
- updateVisionModelSelect
- showVisionTestResult
- requestVisionAnalysisWithKimi
- requestVisionAnalysisWithTavern
- requestVisionAnalysis

### 步骤5：修改图片发送逻辑
- 修改sendMessage函数中的图片处理
- 添加needsVisionAnalysis标记
- 移除自动触发AI回复

### 步骤6：修改AI回复逻辑
- 在requestAiReply函数开头添加识图处理
- 批量处理待识图的图片

### 步骤7：添加事件监听器
- 识图方式选择
- Kimi配置相关
- 自定义API配置相关

### 步骤8：添加配置保存/加载
- 在loadSettings中添加识图配置加载
- 在事件监听器中添加实时保存

## 与聊x手机5.html的差异

### 相同点
- 识图API配置界面
- 三种识图模式
- 智能回退机制
- 状态指示器

### 不同点
- 群聊环境，可能有多个角色
- AI_GENERATE函数获取方式略有不同
- 消息格式可能有差异
- 需要适配群聊的特殊需求

## 注意事项

1. **保持兼容性**：不影响现有的群聊功能
2. **错误处理**：确保识图失败时有合适的回退
3. **性能考虑**：避免重复识图同一张图片
4. **用户体验**：清晰的状态反馈和操作指引

## 测试要点

1. **基础功能**：
   - 三种识图模式都能正常工作
   - 配置保存和恢复正常
   - 图片上传在手机端正常

2. **群聊特性**：
   - 多角色环境下识图正常
   - 群聊消息格式兼容
   - 不影响其他群聊功能

3. **错误处理**：
   - API连接失败时的处理
   - 识图失败时的回退
   - 网络异常时的表现

4. **用户体验**：
   - 状态指示器显示正确
   - 配置界面操作流畅
   - 手动触发识图工作正常

由于文件较大且功能复杂，建议分步骤实现，每完成一个步骤就进行测试，确保功能稳定后再继续下一步。
