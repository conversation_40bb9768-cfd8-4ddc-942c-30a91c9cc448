{"display_name": "智能媒体助手", "loading_order": 50, "requires": [], "optional": [], "js": "index.js", "css": "style.css", "author": "kencuo", "version": "2.0.0", "homePage": "https://github.com/kencuo/chajian", "description": "智能媒体处理插件，支持图片优化、文档处理、AI识图等功能。修复了txt、json等文档文件被误识别为图片的问题。", "tags": ["media", "image", "document", "ai", "vision", "file-processing"], "features": ["图片智能压缩和优化", "多格式文档处理 (txt, json, md, csv, html, xml等)", "AI图片识别和分析", "文件类型智能识别", "批量文件处理", "聊天集成支持"], "settings": [{"key": "enableImageProcessing", "type": "checkbox", "label": "启用图片处理", "default": true, "tooltip": "启用图片压缩、优化和AI识图功能"}, {"key": "enableDocumentProcessing", "type": "checkbox", "label": "启用文档处理", "default": true, "tooltip": "启用txt、json等文档文件的处理功能"}, {"key": "imageQuality", "type": "slider", "label": "图片质量", "min": 10, "max": 100, "step": 5, "default": 85, "tooltip": "图片压缩质量，数值越高质量越好但文件越大"}, {"key": "maxImageDimension", "type": "number", "label": "图片最大尺寸", "min": 512, "max": 4096, "default": 2048, "tooltip": "图片的最大宽度或高度（像素）"}, {"key": "maxFileSize", "type": "number", "label": "文件大小限制 (MB)", "min": 1, "max": 100, "default": 20, "tooltip": "允许处理的最大文件大小"}, {"key": "enableAIReading", "type": "checkbox", "label": "启用AI文档阅读", "default": true, "tooltip": "自动使用AI分析上传的文档内容"}, {"key": "showProcessingInfo", "type": "checkbox", "label": "显示处理信息", "default": false, "tooltip": "显示文件处理的详细信息和进度"}, {"key": "enableLogging", "type": "checkbox", "label": "启用调试日志", "default": false, "tooltip": "在控制台输出详细的调试信息"}], "supported_file_types": {"images": ["image/jpeg", "image/png", "image/gif", "image/webp", "image/bmp"], "documents": ["text/plain", "application/json", "text/markdown", "text/csv", "text/html", "text/xml", "application/xml", "text/javascript", "application/javascript", "text/css", "application/rtf"]}, "supported_extensions": {"images": ["jpg", "jpeg", "png", "gif", "webp", "bmp"], "documents": ["txt", "json", "md", "csv", "html", "xml", "js", "css", "rtf", "log", "conf", "config", "ini", "yaml", "yml"]}, "api": {"processFile": "window.__processFileByPlugin", "processImage": "window.__uploadImageByPlugin", "processDocument": "window.__processDocumentByPlugin", "isDocumentFile": "window.__isDocumentFile", "getSupportedTypes": "window.__getSupportedFileTypes"}, "changelog": [{"version": "2.0.0", "date": "2024-08-02", "changes": ["修复txt、json等文档文件被误识别为图片的问题", "重构文件类型识别逻辑，支持扩展名识别", "整合图片和文档处理功能到单一插件", "添加标准的SillyTavern插件结构", "改进错误处理和用户反馈", "添加详细的设置选项和配置界面"]}, {"version": "1.0.0", "date": "2024-07-01", "changes": ["初始版本", "基础图片处理功能", "简单文档处理支持"]}]}