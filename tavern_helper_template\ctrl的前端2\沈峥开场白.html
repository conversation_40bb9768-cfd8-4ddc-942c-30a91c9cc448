<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>沈峥 - 档案</title>
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400&family=Inter:wght@300;400;500;600&family=Dancing+Script:wght@400;500;600;700&display=swap');

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: linear-gradient(135deg, #baccd9 0%, #dadada 100%);
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        margin: 0;
        padding: 20px 15px;
        line-height: 1.6;
        color: #142332;
      }

      .archive-container {
        width: 100%;
        max-width: 700px;
        background: #ffffff;
        border: 1px solid #2b3a42;
        border-radius: 12px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 6px 24px rgba(20, 35, 50, 0.15);
        margin-top: 20px;
      }

      .archive-header {
        background: linear-gradient(135deg, #baccd9 0%, #dadada 100%);
        padding: 30px 25px;
        border-bottom: 1px solid #2b3a42;
        text-align: center;
        position: relative;
      }

      .archive-title {
        font-family: 'Crimson Text', serif;
        font-size: 1.5rem;
        font-weight: 600;
        color: #142332;
        letter-spacing: 1px;
        margin-bottom: 8px;
      }

      .archive-subtitle {
        font-size: 0.85rem;
        color: #2b3a42;
        font-weight: 300;
        letter-spacing: 0.5px;
      }

      .archive-main {
        padding: 25px;
        display: flex;
        flex-direction: column;
        gap: 25px;
      }

      .profile-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
      }

      .archive-img {
        width: 160px;
        height: 160px;
        border-radius: 50%;
        object-fit: cover;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        border: 4px solid #ffffff;
        margin-bottom: 20px;
        background: #f8f9fa;
        transition: all 0.3s ease;
      }

      .archive-img:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
      }

      .archive-name {
        font-family: 'Crimson Text', serif;
        font-size: 1.8rem;
        font-weight: 600;
        color: #142332;
        margin-bottom: 8px;
        letter-spacing: 0.5px;
      }

      .archive-name-en {
        font-size: 1rem;
        color: #2b3a42;
        font-weight: 300;
        margin-top: 5px;
        font-style: italic;
      }

      .archive-quote {
        font-family: 'Crimson Text', serif;
        font-size: 1.1rem;
        color: #142332;
        background: linear-gradient(135deg, #baccd9 0%, #dadada 100%);
        border-left: 4px solid #2b3a42;
        border-radius: 8px;
        padding: 20px 25px;
        margin: 25px 0;
        font-style: italic;
        text-align: center;
        box-shadow: 0 3px 12px rgba(20, 35, 50, 0.1);
        position: relative;
        line-height: 1.7;
      }

      .archive-quote::before {
        content: '"';
        font-size: 3rem;
        color: #2b3a42;
        position: absolute;
        top: -10px;
        left: 15px;
        font-family: 'Crimson Text', serif;
      }

      .archive-quote::after {
        content: '"';
        font-size: 3rem;
        color: #2b3a42;
        position: absolute;
        bottom: -25px;
        right: 15px;
        font-family: 'Crimson Text', serif;
      }

      .archive-quote-secondary {
        font-family: 'Dancing Script', cursive;
        font-size: 1.2rem;
        font-weight: 500;
        color: #142332;
        background: linear-gradient(135deg, #ffffff 0%, #baccd9 100%);
        border: 1px solid #2b3a42;
        border-radius: 8px;
        padding: 18px 22px;
        margin: 20px 0;
        text-align: center;
        box-shadow: 0 2px 10px rgba(20, 35, 50, 0.08);
        line-height: 1.6;
        letter-spacing: 0.5px;
      }

      .info-section {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .archive-info {
        background: #ffffff;
        border: 1px solid #2b3a42;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 6px rgba(20, 35, 50, 0.08);
      }

      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        font-size: 0.95rem;
        padding: 8px 0;
        border-bottom: 1px solid #dadada;
      }

      .info-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      .info-label {
        color: #2b3a42;
        font-weight: 500;
        letter-spacing: 0.3px;
      }

      .info-value {
        color: #142332;
        font-weight: 600;
      }

      .info-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 8px;
      }

      .tag {
        background: #baccd9;
        color: #142332;
        border: 1px solid #2b3a42;
        border-radius: 16px;
        padding: 4px 12px;
        font-size: 0.8rem;
        font-weight: 500;
        transition: all 0.2s ease;
      }

      .tag:hover {
        background: #dadada;
        border-color: #142332;
      }

      .scene-section {
        background: #ffffff;
        border: 1px solid #2b3a42;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 6px rgba(20, 35, 50, 0.08);
      }

      .scene-section-title {
        font-family: 'Crimson Text', serif;
        font-size: 1.3rem;
        font-weight: 600;
        color: #142332;
        margin-bottom: 15px;
        text-align: center;
        letter-spacing: 0.5px;
      }

      .scene-list-container {
        display: flex;
        flex-direction: column;
        gap: 10px;
        max-height: 320px;
        overflow-y: auto;
        padding-right: 8px;
        scrollbar-width: thin;
        scrollbar-color: #2b3a42 #dadada;
      }

      .scene-list-container::-webkit-scrollbar {
        width: 6px;
      }

      .scene-list-container::-webkit-scrollbar-track {
        background: #dadada;
        border-radius: 3px;
        margin: 2px 0;
      }

      .scene-list-container::-webkit-scrollbar-thumb {
        background: #2b3a42;
        border-radius: 3px;
        transition: background 0.2s ease;
      }

      .scene-list-container::-webkit-scrollbar-thumb:hover {
        background: #142332;
      }

      .scene-list-container::-webkit-scrollbar-corner {
        background: #dadada;
      }

      .scene-item-btn {
        background: #ffffff;
        border: 1px solid #2b3a42;
        border-radius: 6px;
        padding: 12px 15px;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: left;
        width: 100%;
        color: #142332;
        font-size: 0.95rem;
        display: block;
      }

      .scene-item-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(20, 35, 50, 0.12);
        border-color: #142332;
        background: #baccd9;
      }

      .scene-title {
        font-size: 1rem;
        font-weight: 600;
        color: #142332;
        margin-bottom: 4px;
      }

      .scene-description {
        font-size: 0.85rem;
        color: #2b3a42;
        line-height: 1.4;
      }

      /* SillyTavern适配 */
      @media (max-width: 768px) {
        body {
          padding: 10px 5px;
          align-items: flex-start;
        }

        .archive-container {
          margin-top: 10px;
          max-width: 100%;
        }

        .archive-main {
          padding: 20px 15px;
        }

        .archive-img {
          width: 140px;
          height: 140px;
        }

        .archive-title {
          font-size: 1.3rem;
        }

        .archive-name {
          font-size: 1.5rem;
        }

        .archive-quote {
          font-size: 1rem;
          padding: 15px 20px;
          margin: 20px 0;
        }

        .archive-quote-secondary {
          font-size: 1.1rem;
          padding: 15px 18px;
          margin: 15px 0;
        }

        .scene-list-container {
          max-height: 280px;
        }
      }

      @media (max-width: 480px) {
        body {
          padding: 5px;
        }

        .archive-container {
          margin-top: 5px;
          border-radius: 6px;
        }

        .archive-header {
          padding: 20px 15px;
        }

        .archive-main {
          padding: 15px;
        }

        .archive-img {
          width: 120px;
          height: 120px;
        }

        .archive-title {
          font-size: 1.2rem;
        }

        .archive-name {
          font-size: 1.3rem;
        }

        .archive-quote {
          font-size: 0.95rem;
          padding: 12px 15px;
          margin: 15px 0;
        }

        .archive-quote::before,
        .archive-quote::after {
          font-size: 2rem;
        }

        .archive-quote-secondary {
          font-size: 1rem;
          padding: 12px 15px;
          margin: 12px 0;
        }

        .scene-list-container {
          max-height: 220px;
        }

        .info-row {
          font-size: 0.9rem;
        }

        .scene-item-btn {
          padding: 10px 12px;
        }

        .scene-title {
          font-size: 0.95rem;
        }

        .scene-description {
          font-size: 0.8rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="archive-container">
      <div class="archive-header">
        <div class="archive-title">Personnel Archive</div>
        <div class="archive-subtitle">Confidential Document</div>
      </div>

      <div class="archive-main">
        <div class="profile-section">
          <div class="archive-quote" style="margin-bottom: 28px; margin-top: 0">
            In war, I have observed for a long time, and did not see any sacred, glorious things.
          </div>
          <img class="archive-img" src="https://files.catbox.moe/9vd6ni.jpeg" alt="沈峥" />
          <div class="archive-name">
            沈峥
            <div class="archive-name-en">Shen Zheng</div>
          </div>
          <div class="archive-quote-secondary" style="margin-top: 28px; margin-bottom: 0">
            <br>When you have love, you want to do something for the other person. You want to sacrifice yourself, you want to serve.
          </br>作者：ctrl。
          </div>
        </div>

        <div class="info-section">
          <div class="archive-info">
            <div class="info-row">
              <span class="info-label">性别</span>
              <span class="info-value">男</span>
            </div>
            <div class="info-row">
              <span class="info-label">年龄</span>
              <span class="info-value">28岁</span>
            </div>
            <div class="info-row">
              <span class="info-label">类别/标签</span>
              <div class="info-tags">
                <span class="tag">响尾蛇佣兵团团长</span>
                <span class="tag">代号：蝰蛇</span>
              </div>
            </div>
          </div>

          <div class="scene-section">
            <div class="scene-section-title">场景选择</div>
            <div class="scene-list-container">
              <button class="scene-item-btn" onclick="switchToOpening(2)">
                <div class="scene-title">卧底开场白</div>
                <div class="scene-description">【团长的试探】</div>
              </button>
              <button class="scene-item-btn" onclick="switchToOpening(3)">
                <div class="scene-title">卧底</div>
                <div class="scene-description">【任务成功，活捉沈峥】</div>
              </button>
              <button class="scene-item-btn" onclick="switchToOpening(4)">
                <div class="scene-title">团长的诱惑</div>
                <div class="scene-description">【危险的魅力】</div>
              </button>
              <button class="scene-item-btn" onclick="switchToOpening(5)">
                <div class="scene-title">团长舍命相救</div>
                <div class="scene-description">【生死关头的选择】</div>
              </button>
              <button class="scene-item-btn" onclick="switchToOpening(6)">
                <div class="scene-title">团长的报恩</div>
                <div class="scene-description">【恩情难忘】</div>
              </button>
              <button class="scene-item-btn" onclick="switchToOpening(7)">
                <div class="scene-title">真倒霉</div>
                <div class="scene-description">【怎么和上司"荒野求生"了】</div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      function showTip(message) {
        const tip = document.createElement('div');
        tip.style.cssText = `
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: rgba(255, 255, 255, 0.98);
          color: #142332;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          z-index: 2000;
          backdrop-filter: blur(10px);
          border: 1px solid #2b3a42;
          box-shadow: 0 6px 24px rgba(20, 35, 50, 0.15);
          font-family: 'Inter', sans-serif;
        `;
        tip.textContent = message;
        document.body.appendChild(tip);

        // 添加淡入动画
        tip.style.opacity = '0';
        tip.style.transform = 'translate(-50%, -50%) scale(0.8)';
        setTimeout(() => {
          tip.style.transition = 'all 0.3s ease';
          tip.style.opacity = '1';
          tip.style.transform = 'translate(-50%, -50%) scale(1)';
        }, 10);

        setTimeout(() => {
          tip.style.opacity = '0';
          tip.style.transform = 'translate(-50%, -50%) scale(0.8)';
          setTimeout(() => tip.remove(), 300);
        }, 2000);
      }

      async function switchToOpening(openingId) {
        try {
          // 在实际的SillyTavern环境中，使用以下API
          if (typeof getChatMessages === 'function' && typeof setChatMessage === 'function') {
            // 获取第一条消息（通常是角色的开场白）
            const messages = await getChatMessages(0, { include_swipe: true });

            if (messages && messages.length > 0 && messages[0].swipes && messages[0].swipes.length >= openingId) {
              // 如果找到了开场白，就切换到对应的开场白
              const content = messages[0].swipes[openingId - 1];

              // 使用setChatMessage API设置消息
              setChatMessage(content, 0, {
                swipe_id: openingId - 1,
                refresh: 'display_and_render_current',
              });

              console.log(`oi，成功了！ ${openingId - 1}`);

              // 显示成功消息
              const scenes = [
                null,
                null,
                { title: '卧底开场白', desc: '团长的试探' },
                { title: '卧底', desc: '任务成功，活捉沈峥' },
                { title: '团长的诱惑', desc: '危险的魅力' },
                { title: '团长舍命相救', desc: '生死关头的选择' },
                { title: '团长的报恩', desc: '恩情难忘' },
                { title: '真倒霉', desc: '怎么和上司"荒野求生"了' },
              ];
              showTip(`成功喵: ${scenes[openingId - 1].title}`);
            } else {
              console.error('什么？开场白在哪喵？！');
              showTip('错误：找不到……我找不到开场白啊');
            }
          } else {
            // 在非SillyTavern环境中，模拟成功消息
            console.log(`模拟切换到开场白 ${openingId - 1}`);
            const scenes = [
              null,
              null,
              { title: '卧底开场白', desc: '团长的试探' },
              { title: '卧底', desc: '任务成功，活捉沈峥' },
              { title: '团长的诱惑', desc: '危险的魅力' },
              { title: '团长舍命相救', desc: '生死关头的选择' },
              { title: '团长的报恩', desc: '恩情难忘' },
              { title: '真倒霉', desc: '怎么和上司"荒野求生"了' },
            ];
            showTip(`喵: ${scenes[openingId - 1].title}`);
          }
        } catch (error) {
          console.error('错误……可恶!!:', error);
          showTip(`可恶，竟然出错了喵: ${error.message}`);
        }
      }

      // 添加页面加载动画
      document.addEventListener('DOMContentLoaded', function () {
        const container = document.querySelector('.archive-container');
        container.style.opacity = '0';
        container.style.transform = 'translateY(30px)';

        setTimeout(() => {
          container.style.transition = 'all 0.8s ease';
          container.style.opacity = '1';
          container.style.transform = 'translateY(0)';
        }, 100);
      });
    </script>
  </body>
</html>
