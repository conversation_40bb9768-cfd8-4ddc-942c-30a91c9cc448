// 抖音API配置文件
// 这个文件包含了连接真实抖音数据的各种API配置

const DOUYIN_CONFIG = {
  // 是否启用真实API（设为false使用演示数据）
  USE_REAL_API: true,
  
  // API配置
  API_SOURCES: {
    // 方案1：抖音开放平台API（需要申请开发者账号）
    OFFICIAL: {
      baseURL: 'https://open.douyin.com',
      endpoints: {
        feed: '/api/apps/v1/videos/list',
        search: '/api/apps/v1/videos/search',
        user: '/api/apps/v1/user/info'
      },
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DouyinApp/1.0'
      },
      // 需要申请的access_token
      accessToken: 'YOUR_ACCESS_TOKEN_HERE'
    },
    
    // 方案2：第三方抖音数据API服务
    THIRD_PARTY: {
      baseURL: 'https://api.iesdouyin.com',
      endpoints: {
        feed: '/web/api/v2/aweme/feed',
        search: '/web/api/v2/aweme/search',
        detail: '/web/api/v2/aweme/iteminfo'
      },
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
        'Referer': 'https://www.douyin.com/',
        'Accept': 'application/json'
      }
    },
    
    // 方案3：网络爬虫代理服务
    CRAWLER_PROXY: {
      baseURL: 'https://douyin-crawler-api.herokuapp.com',
      endpoints: {
        feed: '/api/douyin/feed',
        search: '/api/douyin/search',
        video: '/api/douyin/video'
      },
      headers: {
        'Accept': 'application/json',
        'X-API-Key': 'your-api-key-here'
      }
    },
    
    // 方案4：本地代理服务器（需要自己搭建）
    LOCAL_PROXY: {
      baseURL: 'http://localhost:3000',
      endpoints: {
        feed: '/douyin/feed',
        search: '/douyin/search',
        video: '/douyin/video'
      },
      headers: {
        'Accept': 'application/json'
      }
    }
  },
  
  // 请求配置
  REQUEST_CONFIG: {
    timeout: 10000, // 10秒超时
    retryCount: 3,  // 重试次数
    retryDelay: 1000 // 重试延迟（毫秒）
  },
  
  // 数据缓存配置
  CACHE_CONFIG: {
    enabled: true,
    duration: 5 * 60 * 1000, // 5分钟缓存
    maxSize: 100 // 最大缓存条目数
  },
  
  // 视频播放配置
  VIDEO_CONFIG: {
    autoplay: true,
    controls: true,
    preload: 'metadata',
    // 支持的视频格式
    supportedFormats: ['mp4', 'webm', 'ogg'],
    // 视频质量优先级
    qualityPriority: ['720p', '480p', '360p']
  },
  
  // 用户界面配置
  UI_CONFIG: {
    // 每页显示的视频数量
    videosPerPage: 10,
    // 是否启用无限滚动
    infiniteScroll: true,
    // 主题配置
    theme: {
      primaryColor: '#ff0050',
      secondaryColor: '#ff4081',
      backgroundColor: '#000000'
    }
  }
};

// 实用工具函数
const DouyinUtils = {
  // 格式化数字显示
  formatNumber(num) {
    if (num >= 100000000) {
      return (num / 100000000).toFixed(1) + '亿';
    } else if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  },
  
  // 格式化时间显示
  formatTime(timestamp) {
    const now = Date.now();
    const diff = now - timestamp * 1000;
    
    if (diff < 60000) {
      return '刚刚';
    } else if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) {
      return Math.floor(diff / 3600000) + '小时前';
    } else if (diff < 2592000000) {
      return Math.floor(diff / 86400000) + '天前';
    } else {
      const date = new Date(timestamp * 1000);
      return `${date.getMonth() + 1}-${date.getDate()}`;
    }
  },
  
  // 提取视频ID
  extractVideoId(url) {
    const patterns = [
      /\/video\/(\d+)/,
      /aweme_id=(\d+)/,
      /item_ids=(\d+)/
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    
    return null;
  },
  
  // 验证视频URL
  isValidVideoUrl(url) {
    const validDomains = [
      'douyin.com',
      'iesdouyin.com',
      'amemv.com',
      'tiktok.com'
    ];
    
    try {
      const urlObj = new URL(url);
      return validDomains.some(domain => urlObj.hostname.includes(domain));
    } catch {
      return false;
    }
  },
  
  // 生成随机设备ID
  generateDeviceId() {
    return 'web_' + Math.random().toString(36).substr(2, 9);
  },
  
  // 处理跨域请求
  async fetchWithCORS(url, options = {}) {
    const corsProxies = [
      'https://cors-anywhere.herokuapp.com/',
      'https://api.allorigins.win/raw?url=',
      'https://cors-proxy.htmldriven.com/?url='
    ];
    
    // 首先尝试直接请求
    try {
      const response = await fetch(url, options);
      if (response.ok) return response;
    } catch (error) {
      console.warn('Direct request failed:', error);
    }
    
    // 尝试使用CORS代理
    for (const proxy of corsProxies) {
      try {
        const proxyUrl = proxy + encodeURIComponent(url);
        const response = await fetch(proxyUrl, options);
        if (response.ok) return response;
      } catch (error) {
        console.warn('CORS proxy failed:', proxy, error);
        continue;
      }
    }
    
    throw new Error('All request methods failed');
  }
};

// 数据缓存管理
class DouyinCache {
  constructor() {
    this.cache = new Map();
    this.config = DOUYIN_CONFIG.CACHE_CONFIG;
  }
  
  set(key, data) {
    if (!this.config.enabled) return;
    
    // 清理过期缓存
    this.cleanup();
    
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.config.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }
  
  get(key) {
    if (!this.config.enabled) return null;
    
    const item = this.cache.get(key);
    if (!item) return null;
    
    // 检查是否过期
    if (Date.now() - item.timestamp > this.config.duration) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  cleanup() {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > this.config.duration) {
        this.cache.delete(key);
      }
    }
  }
  
  clear() {
    this.cache.clear();
  }
}

// 导出配置和工具
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { DOUYIN_CONFIG, DouyinUtils, DouyinCache };
} else {
  window.DOUYIN_CONFIG = DOUYIN_CONFIG;
  window.DouyinUtils = DouyinUtils;
  window.DouyinCache = DouyinCache;
}
