/**
 * 同层手机界面 - 文档处理功能集成代码
 * 这个文件展示了如何在你的同层手机界面中集成文档处理功能
 */

// ==================== 文档处理功能集成 ====================

/**
 * 扩展现有的文件处理功能，支持文档
 */
function initDocumentProcessing() {
  // 检查插件是否可用
  if (typeof top.window.__processFileByPlugin !== 'function') {
    console.warn('文档处理插件未加载');
    return false;
  }
  
  console.log('✅ 文档处理功能已启用');
  return true;
}

/**
 * 处理文件上传（扩展版本，支持图片和文档）
 */
async function handleFileUploadExtended(file) {
  try {
    // 显示上传状态
    showUploadStatus(`正在处理文件: ${file.name}...`);
    
    // 使用插件处理文件（自动识别类型）
    const result = await top.window.__processFileByPlugin(file, {
      enableAIReading: true, // 启用AI阅读
      aiPrompt: '请分析这个文件的内容，并提供简要总结' // 自定义AI提示
    });
    
    if (result.success) {
      // 根据文件类型处理结果
      if (file.type.startsWith('image/')) {
        // 图片文件 - 使用现有的图片处理逻辑
        return await handleImageResult(result, file);
      } else {
        // 文档文件 - 新的文档处理逻辑
        return await handleDocumentResult(result, file);
      }
    } else {
      throw new Error('文件处理失败');
    }
  } catch (error) {
    console.error('文件处理失败:', error);
    showUploadStatus(`文件处理失败: ${error.message}`, 'error');
    throw error;
  }
}

/**
 * 处理文档结果
 */
async function handleDocumentResult(result, file) {
  const time = getTimeStr();
  const currentUser = getCurrentUser();
  
  // 构建文档消息
  const documentMessage = {
    type: 'document',
    user: currentUser,
    time: time,
    fileName: result.metadata.originalName,
    fileType: result.metadata.type,
    fileSize: result.metadata.size,
    content: result.content,
    contentLength: result.metadata.contentLength,
    // 添加特殊标记，用于AI识别
    aiReadable: true
  };
  
  // 发送文档消息
  sendMessage(documentMessage);
  
  // 如果内容较长，可以自动触发AI分析
  if (result.content.length > 1000) {
    setTimeout(() => {
      requestAIAnalysis(result.content, file.name);
    }, 1000);
  }
  
  showUploadStatus(`文档上传成功: ${file.name}`, 'success');
  return result;
}

/**
 * 处理图片结果（保持现有逻辑）
 */
async function handleImageResult(result, file) {
  // 使用现有的图片处理逻辑
  const time = getTimeStr();
  const currentUser = getCurrentUser();
  
  const imageMessage = {
    type: 'image',
    user: currentUser,
    time: time,
    imageUrl: result.url,
    fileName: file.name,
    fileSize: file.size
  };
  
  sendMessage(imageMessage);
  showUploadStatus(`图片上传成功: ${file.name}`, 'success');
  return result;
}

/**
 * 请求AI分析文档内容
 */
async function requestAIAnalysis(content, fileName) {
  try {
    const analysisPrompt = `我刚刚上传了一个文档"${fileName}"，内容如下：

${content}

请帮我分析这个文档的主要内容，并提供以下信息：
1. 文档主题和类型
2. 关键信息摘要
3. 重要观点或结论
4. 如果有问题或需要进一步讨论的地方

请用简洁明了的方式回复。`;

    // 发送分析请求给AI
    const analysisMessage = {
      type: 'text',
      user: getCurrentUser(),
      time: getTimeStr(),
      message: analysisPrompt,
      isAIRequest: true // 标记为AI请求
    };
    
    sendMessage(analysisMessage);
    
    // 触发AI回复
    setTimeout(() => {
      requestAiReply();
    }, 500);
    
  } catch (error) {
    console.error('AI分析请求失败:', error);
  }
}

/**
 * 扩展消息渲染，支持文档类型
 */
function renderDocumentMessage(message) {
  const messageDiv = document.createElement('div');
  messageDiv.className = 'message document-message';
  
  const avatar = getUserAvatar(message.user);
  const fileIcon = getFileIcon(message.fileType);
  
  messageDiv.innerHTML = `
    <div class="message-header">
      <img src="${avatar}" alt="${message.user}" class="avatar">
      <span class="username">${message.user}</span>
      <span class="time">${message.time}</span>
    </div>
    <div class="message-content">
      <div class="document-info">
        <div class="document-header">
          ${fileIcon} <strong>${message.fileName}</strong>
          <span class="file-meta">(${message.fileType}, ${formatFileSize(message.fileSize)})</span>
        </div>
        <div class="document-preview">
          <div class="content-preview">
            ${formatDocumentContent(message.content)}
          </div>
          <div class="document-actions">
            <button onclick="showFullContent('${message.fileName}', \`${escapeHtml(message.content)}\`)">
              查看完整内容
            </button>
            <button onclick="requestAIAnalysis(\`${escapeHtml(message.content)}\`, '${message.fileName}')">
              AI分析
            </button>
          </div>
        </div>
      </div>
    </div>
  `;
  
  return messageDiv;
}

/**
 * 获取文件图标
 */
function getFileIcon(fileType) {
  const iconMap = {
    'text/plain': '📄',
    'application/pdf': '📕',
    'application/msword': '📘',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '📘',
    'application/json': '📋',
    'text/markdown': '📝'
  };
  
  return iconMap[fileType] || '📄';
}

/**
 * 格式化文档内容预览
 */
function formatDocumentContent(content) {
  if (!content) return '无内容';
  
  // 限制预览长度
  const maxLength = 200;
  const preview = content.length > maxLength 
    ? content.substring(0, maxLength) + '...' 
    : content;
  
  // 简单的HTML转义和格式化
  return escapeHtml(preview)
    .replace(/\n/g, '<br>')
    .replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;');
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 显示完整文档内容
 */
function showFullContent(fileName, content) {
  // 创建模态框显示完整内容
  const modal = document.createElement('div');
  modal.className = 'document-modal';
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h3>📄 ${fileName}</h3>
        <button class="close-btn" onclick="this.closest('.document-modal').remove()">×</button>
      </div>
      <div class="modal-body">
        <pre class="document-content">${escapeHtml(content)}</pre>
      </div>
      <div class="modal-footer">
        <button onclick="copyToClipboard(\`${escapeHtml(content)}\`)">复制内容</button>
        <button onclick="requestAIAnalysis(\`${escapeHtml(content)}\`, '${fileName}')">AI分析</button>
      </div>
    </div>
  `;
  
  document.body.appendChild(modal);
}

/**
 * 复制到剪贴板
 */
function copyToClipboard(text) {
  navigator.clipboard.writeText(text).then(() => {
    showUploadStatus('内容已复制到剪贴板', 'success');
  }).catch(err => {
    console.error('复制失败:', err);
    showUploadStatus('复制失败', 'error');
  });
}

/**
 * HTML转义
 */
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * 显示上传状态
 */
function showUploadStatus(message, type = 'info') {
  // 这里可以使用你现有的状态显示逻辑
  console.log(`[${type.toUpperCase()}] ${message}`);
  
  // 如果有toastr或其他通知系统，可以在这里调用
  if (typeof toastr !== 'undefined') {
    toastr[type](message);
  }
}

// ==================== 集成到现有系统 ====================

/**
 * 扩展现有的文件处理函数
 * 在你的现有代码中，将原来的图片处理函数替换为这个扩展版本
 */
async function handleFiles(files) {
  const validFiles = Array.from(files).filter(file => {
    // 检查是否为支持的文件类型
    const supportedTypes = top.window.__getSupportedFileTypes?.() || { all: [] };
    
    if (!supportedTypes.all.includes(file.type)) {
      alert(`${file.name} 不是支持的文件类型`);
      return false;
    }
    
    if (file.size > 50 * 1024 * 1024) { // 50MB限制
      alert(`${file.name} 文件过大，最大支持50MB`);
      return false;
    }
    
    return true;
  });

  if (validFiles.length === 0) return;

  // 处理每个文件
  for (const file of validFiles) {
    try {
      await handleFileUploadExtended(file);
    } catch (error) {
      console.error(`处理文件 ${file.name} 失败:`, error);
    }
  }
}

/**
 * 扩展消息发送函数，支持文档类型
 */
function sendMessage(messageData) {
  // 根据消息类型选择不同的渲染方式
  let messageElement;
  
  switch (messageData.type) {
    case 'document':
      messageElement = renderDocumentMessage(messageData);
      break;
    case 'image':
      messageElement = renderImageMessage(messageData); // 使用现有的图片渲染
      break;
    default:
      messageElement = renderTextMessage(messageData); // 使用现有的文本渲染
      break;
  }
  
  // 添加到消息容器
  const messagesContainer = document.getElementById('messages') || document.querySelector('.messages');
  if (messagesContainer && messageElement) {
    messagesContainer.appendChild(messageElement);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }
  
  // 保存到历史记录
  saveMessageToHistory(messageData);
}

// ==================== 初始化 ====================

/**
 * 在页面加载时初始化文档处理功能
 */
document.addEventListener('DOMContentLoaded', function() {
  // 初始化文档处理
  const documentProcessingEnabled = initDocumentProcessing();
  
  if (documentProcessingEnabled) {
    console.log('✅ 文档处理功能已集成到同层手机界面');
  } else {
    console.warn('⚠️ 文档处理功能未启用，请确保插件已加载');
  }
});

// ==================== CSS样式（需要添加到你的CSS文件中） ====================
const documentStyles = `
.document-message {
  border-left: 4px solid #007bff;
}

.document-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin: 10px 0;
}

.document-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  font-weight: bold;
}

.file-meta {
  color: #666;
  font-size: 12px;
  font-weight: normal;
}

.content-preview {
  background: white;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  max-height: 150px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
}

.document-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.document-actions button {
  padding: 5px 10px;
  border: 1px solid #007bff;
  background: white;
  color: #007bff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.document-actions button:hover {
  background: #007bff;
  color: white;
}

.document-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90%;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 15px;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.document-content {
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin: 0;
}

.modal-footer {
  padding: 15px;
  border-top: 1px solid #ddd;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.close-btn:hover {
  color: #000;
}
`;

// 动态添加样式
const styleSheet = document.createElement('style');
styleSheet.textContent = documentStyles;
document.head.appendChild(styleSheet);
