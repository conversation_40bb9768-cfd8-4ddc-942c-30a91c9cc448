# 抖音界面使用说明

## 概述

这是一个真正能联网的抖音界面，可以连接到真实的抖音数据源，为SillyTavern提供真实的抖音内容体验。

## 功能特性

### 🌟 核心功能
- **真实API连接**: 支持多种抖音数据源
- **视频播放**: 支持真实视频播放
- **搜索功能**: 实时搜索抖音内容
- **多标签浏览**: 推荐、热门、关注
- **数据缓存**: 提高加载速度
- **设置面板**: 灵活配置API源

### 📱 界面特性
- **移动端设计**: 仿真抖音界面
- **响应式布局**: 适配不同屏幕
- **流畅动画**: 现代化交互体验
- **暗色主题**: 符合抖音风格

## 使用方法

### 1. 基本使用
1. 在主界面点击抖音图标
2. 新窗口打开抖音界面
3. 浏览推荐、热门、关注内容
4. 点击视频播放或搜索内容

### 2. 设置配置
1. 点击右上角设置按钮（⚙️）
2. 配置API连接选项：
   - **启用真实API**: 开启/关闭真实数据连接
   - **API源选择**: 选择不同的数据源
   - **数据缓存**: 启用缓存提高速度
3. 测试连接确保API正常工作

### 3. API源说明

#### 第三方API (推荐)
- **地址**: `https://api.iesdouyin.com`
- **特点**: 稳定性较好，数据丰富
- **限制**: 可能有访问频率限制

#### 爬虫代理
- **地址**: `https://douyin-crawler.vercel.app`
- **特点**: 实时数据，更新快
- **限制**: 依赖代理服务稳定性

#### 本地代理
- **地址**: `http://localhost:3000`
- **特点**: 需要自己搭建，完全可控
- **限制**: 需要技术能力搭建

#### 官方API
- **地址**: `https://open.douyin.com`
- **特点**: 最稳定，数据最准确
- **限制**: 需要申请开发者账号和access_token

## 技术实现

### API连接策略
1. **多源备份**: 支持多个API源，自动切换
2. **错误处理**: API失败时自动降级到演示数据
3. **跨域处理**: 使用多种方法解决跨域问题
4. **缓存机制**: 智能缓存减少API调用

### 数据解析
- 支持多种API响应格式
- 统一数据结构处理
- 智能数字格式化显示
- 时间格式化处理

### 视频播放
- 支持多种视频格式
- 自动选择最佳画质
- 响应式视频播放器
- 播放错误处理

## 配置文件

### 抖音配置.js
包含所有API配置和工具函数：

```javascript
// 启用/禁用真实API
USE_REAL_API: true

// API源配置
API_SOURCES: {
  OFFICIAL: { /* 官方API配置 */ },
  THIRD_PARTY: { /* 第三方API配置 */ },
  CRAWLER_PROXY: { /* 爬虫代理配置 */ },
  LOCAL_PROXY: { /* 本地代理配置 */ }
}

// 缓存配置
CACHE_CONFIG: {
  enabled: true,
  duration: 5 * 60 * 1000, // 5分钟
  maxSize: 100
}
```

## 故障排除

### 常见问题

#### 1. 无法加载内容
**原因**: API连接失败
**解决**: 
- 检查网络连接
- 尝试切换API源
- 查看浏览器控制台错误信息

#### 2. 视频无法播放
**原因**: 视频URL无效或格式不支持
**解决**:
- 检查视频URL是否有效
- 尝试不同的API源
- 确认浏览器支持视频格式

#### 3. 搜索无结果
**原因**: 搜索API不可用
**解决**:
- 检查搜索关键词
- 尝试切换API源
- 确认API支持搜索功能

#### 4. 跨域错误
**原因**: 浏览器CORS限制
**解决**:
- 使用CORS代理
- 配置本地代理服务器
- 使用支持CORS的API源

### 调试方法
1. 打开浏览器开发者工具
2. 查看Console标签页的错误信息
3. 检查Network标签页的请求状态
4. 使用设置面板的"测试连接"功能

## 自定义开发

### 添加新的API源
1. 在`抖音配置.js`中添加新的API配置
2. 更新`parseAPIResponse`函数支持新格式
3. 在设置面板添加新选项

### 修改界面样式
1. 编辑CSS样式部分
2. 调整颜色、字体、布局
3. 添加新的动画效果

### 扩展功能
1. 添加用户登录功能
2. 实现视频上传功能
3. 添加评论和互动功能

## 注意事项

### 法律合规
- 遵守抖音平台使用条款
- 不要进行恶意爬取
- 尊重内容版权

### 技术限制
- API可能有访问频率限制
- 某些功能需要用户授权
- 跨域访问可能受限

### 隐私保护
- 不存储用户敏感信息
- 缓存数据仅在本地
- 遵守数据保护法规

## 更新日志

### v1.0.0 (当前版本)
- ✅ 基础界面实现
- ✅ 多API源支持
- ✅ 视频播放功能
- ✅ 搜索功能
- ✅ 缓存机制
- ✅ 设置面板

### 计划功能
- 🔄 用户登录
- 🔄 视频上传
- 🔄 评论系统
- 🔄 直播功能
- 🔄 更多API源

## 技术支持

如果遇到问题或需要帮助，请：
1. 查看本说明文档
2. 检查浏览器控制台错误
3. 尝试不同的配置选项
4. 联系开发者获取支持

---

**注意**: 这是一个演示项目，实际使用时请确保遵守相关平台的使用条款和法律法规。
