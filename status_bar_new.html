<link
  href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;600;700&display=swap"
  rel="stylesheet"
/>
<details>
  <summary>状态栏</summary>
  <style>
    body {
      margin: 0;
      padding: 10px;
      background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1f1f1f 100%);
      background-image: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 48, 0.15) 0%, transparent 50%);
      background-size: cover;
      background-attachment: fixed;
      font-family: '<PERSON><PERSON><PERSON>', 'Microsoft YaHei', sans-serif;
      min-height: 100vh;
      color: #e0e0e0;
    }

    .status-container {
      max-width: 100%;
      margin: 0 auto;
      background: rgba(30, 30, 30, 0.95);
      border-radius: 15px;
      box-shadow: 0 0 30px rgba(120, 119, 198, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
      overflow: hidden;
      backdrop-filter: blur(15px);
      border: 1px solid rgba(120, 119, 198, 0.4);
    }

    .status-summary {
      background: linear-gradient(135deg, #3a3a3a 0%, #4a4a4a 50%, #2a2a2a 100%);
      color: #00ff88;
      padding: 12px 20px;
      cursor: pointer;
      font-size: 18px;
      font-weight: 700;
      text-align: center;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      font-family: 'Orbitron', monospace;
      text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
      letter-spacing: 2px;
    }

    .status-summary::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg, transparent, rgba(0, 255, 136, 0.3), transparent);
      transform: rotate(45deg);
      transition: all 0.6s ease;
      opacity: 0;
    }

    .status-summary:hover::before {
      animation: shimmer 1.5s ease-in-out infinite;
    }

    @keyframes shimmer {
      0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
        opacity: 0;
      }
      50% {
        opacity: 1;
      }
      100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
        opacity: 0;
      }
    }

    .status-summary:hover {
      background: linear-gradient(135deg, #4a4a4a 0%, #5a5a5a 50%, #3a3a3a 100%);
      transform: translateY(-2px);
      box-shadow: 0 0 20px rgba(0, 255, 136, 0.4);
      color: #00ffaa;
    }

    .status-content {
      display: none;
      padding: 0;
      background: linear-gradient(180deg, #2a2a2a 0%, #1f1f1f 100%);
      background-image: url('https://files.catbox.moe/73ykh2.jpeg');
      background-size: cover;
      background-position: center;
      background-attachment: local;
      position: relative;
    }

    .status-content::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(180deg, rgba(42, 42, 42, 0.85) 0%, rgba(31, 31, 31, 0.9) 100%);
      z-index: 1;
    }

    .status-main {
      display: flex;
      min-height: 350px;
      position: relative;
      z-index: 2;
    }

    .character-section {
      flex: 1;
      background: linear-gradient(135deg, #333333 0%, #2a2a2a 100%);
      padding: 15px;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      border-right: 2px solid rgba(120, 119, 198, 0.4);
    }

    .character-image {
      width: 180px;
      height: 240px;
      border-radius: 12px;
      object-fit: cover;
      border: 3px solid #ff7730;
      box-shadow: 0 0 20px rgba(255, 119, 48, 0.4), inset 0 0 10px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
      animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
      0%,
      100% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-8px);
      }
    }

    .character-image:hover {
      transform: scale(1.05) translateY(-5px);
      box-shadow: 0 0 30px rgba(255, 119, 48, 0.6), inset 0 0 15px rgba(0, 0, 0, 0.4);
      border-color: #ffaa44;
    }

    .character-name {
      margin-top: 15px;
      font-size: 22px;
      font-weight: 700;
      color: #ff7730;
      text-shadow: 0 0 10px rgba(255, 119, 48, 0.5);
      font-family: 'Orbitron', monospace;
      letter-spacing: 1px;
      text-transform: uppercase;
    }

    .info-section {
      flex: 2;
      padding: 15px;
      background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
      min-width: 0;
      width: 100%;
      box-sizing: border-box;
    }

    .info-item {
      margin-bottom: 12px;
      padding: 12px;
      background: linear-gradient(135deg, rgba(120, 119, 198, 0.1) 0%, rgba(255, 119, 48, 0.05) 100%);
      border-radius: 8px;
      border-left: 3px solid #7877c6;
      transition: all 0.3s ease;
      position: relative;
      overflow: visible;
      border: 1px solid rgba(120, 119, 198, 0.2);
      width: 100%;
      box-sizing: border-box;
    }

    .info-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.2), transparent);
      transition: left 0.5s ease;
    }

    .info-item:hover::before {
      left: 100%;
    }

    .info-item:hover {
      transform: translateX(3px);
      box-shadow: 0 0 15px rgba(120, 119, 198, 0.3);
      border-left-color: #00ff88;
      border-color: rgba(120, 119, 198, 0.4);
    }

    .info-label {
      font-weight: 600;
      color: #00ff88;
      font-size: 14px;
      margin-bottom: 6px;
      display: block;
      font-family: 'Orbitron', monospace;
      text-shadow: 0 0 5px rgba(0, 255, 136, 0.3);
      letter-spacing: 0.5px;
    }

    .info-content {
      color: #cccccc;
      line-height: 1.6;
      max-height: 120px;
      min-height: 40px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px;
      padding-right: 12px;
      font-size: 13px;
      word-wrap: break-word;
      word-break: break-all;
      white-space: normal;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      border: 1px solid rgba(120, 119, 198, 0.1);
      box-sizing: border-box;
      width: 100%;
    }

    .info-content::-webkit-scrollbar {
      width: 8px;
    }

    .info-content::-webkit-scrollbar-track {
      background: rgba(120, 119, 198, 0.1);
      border-radius: 4px;
      margin: 2px;
    }

    .info-content::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #7877c6, #ff7730);
      border-radius: 4px;
      border: 1px solid rgba(0, 0, 0, 0.3);
      box-shadow: 0 0 3px rgba(120, 119, 198, 0.3);
    }

    .info-content::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(135deg, #00ff88, #ff7730);
      box-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
      border-color: rgba(0, 255, 136, 0.3);
    }

    .info-content::-webkit-scrollbar-corner {
      background: transparent;
    }

    details[open] .status-content {
      display: block;
      animation: slideDown 0.5s ease-out;
    }

    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateY(-20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .status-summary::after {
      content: '▼';
      float: right;
      transition: transform 0.3s ease;
    }

    details[open] .status-summary::after {
      transform: rotate(180deg);
    }

    @media (max-width: 768px) {
      body {
        padding: 5px;
      }

      .status-container {
        border-radius: 10px;
      }

      .status-summary {
        padding: 10px 15px;
        font-size: 16px;
      }

      .status-main {
        flex-direction: column;
        min-height: auto;
      }

      .character-section {
        border-right: none;
        border-bottom: 2px solid rgba(120, 119, 198, 0.4);
        padding: 12px;
      }

      .character-image {
        width: 100px;
        height: 130px;
      }

      .character-name {
        font-size: 18px;
        margin-top: 10px;
      }

      .info-section {
        padding: 12px;
      }

      .info-item {
        margin-bottom: 10px;
        padding: 10px;
      }

      .info-label {
        font-size: 13px;
      }

      .info-content {
        font-size: 12px;
        max-height: 100px;
      }
    }

    @media (max-width: 480px) {
      .status-summary {
        font-size: 14px;
        padding: 8px 12px;
      }

      .character-image {
        width: 80px;
        height: 110px;
      }

      .character-name {
        font-size: 16px;
      }

      .info-section {
        padding: 10px;
      }

      .info-item {
        padding: 8px;
        margin-bottom: 8px;
      }

      .info-label {
        font-size: 12px;
      }

      .info-content {
        font-size: 11px;
        max-height: 80px;
      }
    }
  </style>
  <div class="status-container">
    <div class="status-main">
      <div class="character-section">
        <img src="https://files.catbox.moe/988bb3.jpeg" alt="沈峥" class="character-image" />
        <div class="character-name">沈峥</div>
      </div>
      <div class="info-section">
        <div class="info-item">
          <span class="info-label">🧠 内心想法</span>
          <div class="info-content">
          $1
          </div>
        </div>
        <div class="info-item">
          <span class="info-label">💰 信用点</span>
          <div class="info-content">
          $2
          </div>
        </div>
        <div class="info-item">
          <span class="info-label">⚡ 好感度</span>
          <div class="info-content">$3</div>
        </div>
        <div class="info-item">
          <span class="info-label">🛡️ 身体状态</span>
          <div class="info-content">$4</div>
        </div>
        <div class="info-item">
          <span class="info-label">📝 备忘录</span>
          <div class="info-content">$5</div>
        </div>
        <div class="info-item">
          <span class="info-label">🎯 计划</span>
          <div class="info-content">$6</div>
        </div>
        <div class="info-item">
          <span class="info-label">⚔️ 任务</span>
          <div class="info-content">$7</div>
        </div>
      </div>
    </div>
  </div>
</details>
