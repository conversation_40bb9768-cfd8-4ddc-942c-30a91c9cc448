<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色昵称显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .demo-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .demo-title {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
        }
        
        /* 复制同层群聊6的样式 */
        .message {
            display: flex;
            margin-bottom: 12px;
            align-items: flex-start;
        }
        
        .message.sent {
            flex-direction: row-reverse;
        }
        
        .avatar-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            flex-shrink: 0;
        }
        
        .char-nickname {
            font-size: 10px;
            color: #666;
            text-align: center;
            max-width: 50px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
            font-weight: 500;
        }
        
        .message.sent .avatar-container {
            align-items: flex-end;
        }
        
        .message.received .avatar-container {
            align-items: flex-start;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            flex-shrink: 0;
            object-fit: cover;
            background: #ddd;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .message-wrapper {
            position: relative;
            max-width: 70%;
            margin: 0 8px;
        }
        
        .message-content {
            background: #fff;
            padding: 10px 12px;
            border-radius: 12px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            word-wrap: break-word;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .message.sent .message-content {
            background: #95ec69;
            color: #000;
        }
        
        .message-meta {
            font-size: 11px;
            color: #999;
            margin-top: 4px;
            text-align: right;
        }
        
        .message.received .message-meta {
            text-align: left;
        }
        
        .test-controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }
        
        .test-btn:hover {
            background: #0056b3;
        }
        
        .format-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 13px;
            color: #1976d2;
        }
        
        .format-example {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-title">🎭 角色昵称显示测试</div>
        
        <div class="format-info">
            <h4>📋 支持的消息格式：</h4>
            <div class="format-example">[角色昵称|头像文件名|消息内容|时间]</div>
            <p>现在角色昵称会显示在头像上方，让群聊更清晰！</p>
        </div>
        
        <div class="test-controls">
            <button class="test-btn" onclick="addTestMessage('小雪', 'e1xk9k.jpeg', '大家好呀～')">小雪说话</button>
            <button class="test-btn" onclick="addTestMessage('阿明', 'g299b6.jpeg', '今天天气真不错')">阿明说话</button>
            <button class="test-btn" onclick="addTestMessage('莉莉', 'cmegcm.jpeg', '我们一起去玩吧！')">莉莉说话</button>
            <button class="test-btn" onclick="addUserMessage('好的，我们走！')">我回复</button>
            <button class="test-btn" onclick="clearMessages()">清空消息</button>
        </div>
        
        <div id="chatContainer">
            <!-- 消息会在这里显示 -->
        </div>
    </div>
    
    <div class="demo-container">
        <h3>🔍 效果说明</h3>
        <ul style="font-size: 13px; color: #555; line-height: 1.6;">
            <li><strong>角色昵称</strong>：显示在头像上方，字体小巧不占空间</li>
            <li><strong>自动省略</strong>：昵称过长时自动省略显示</li>
            <li><strong>对齐方式</strong>：用户消息右对齐，角色消息左对齐</li>
            <li><strong>群聊识别</strong>：多个角色时可以清楚区分谁在说话</li>
            <li><strong>兼容性</strong>：完全兼容现有的消息格式</li>
        </ul>
    </div>

    <script>
        let messageCount = 0;
        
        function addTestMessage(charName, avatar, content) {
            const container = document.getElementById('chatContainer');
            const time = new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
            
            const message = document.createElement('div');
            message.className = 'message received';
            
            // 创建头像容器
            const avatarContainer = document.createElement('div');
            avatarContainer.className = 'avatar-container';
            
            // 创建角色昵称标签
            const nicknameEl = document.createElement('div');
            nicknameEl.className = 'char-nickname';
            nicknameEl.textContent = charName;
            avatarContainer.appendChild(nicknameEl);
            
            // 创建头像
            const avatarEl = document.createElement('img');
            avatarEl.className = 'avatar';
            avatarEl.src = `https://files.catbox.moe/${avatar}`;
            avatarContainer.appendChild(avatarEl);
            
            // 创建消息内容
            const wrapper = document.createElement('div');
            wrapper.className = 'message-wrapper';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            const timeSpan = document.createElement('div');
            timeSpan.className = 'message-meta';
            timeSpan.textContent = time;
            
            wrapper.appendChild(contentDiv);
            wrapper.appendChild(timeSpan);
            
            // 组装消息
            message.appendChild(avatarContainer);
            message.appendChild(wrapper);
            
            container.appendChild(message);
            container.scrollTop = container.scrollHeight;
            
            messageCount++;
        }
        
        function addUserMessage(content) {
            const container = document.getElementById('chatContainer');
            const time = new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
            
            const message = document.createElement('div');
            message.className = 'message sent';
            
            // 创建头像容器（用户不显示昵称）
            const avatarContainer = document.createElement('div');
            avatarContainer.className = 'avatar-container';
            
            const avatarDiv = document.createElement('div');
            avatarDiv.className = 'avatar user_avatar';
            avatarDiv.style.backgroundImage = 'url(https://files.catbox.moe/cmegcm.jpeg)';
            avatarDiv.style.backgroundSize = 'cover';
            avatarDiv.style.backgroundPosition = 'center';
            avatarContainer.appendChild(avatarDiv);
            
            // 创建消息内容
            const wrapper = document.createElement('div');
            wrapper.className = 'message-wrapper';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            const timeSpan = document.createElement('div');
            timeSpan.className = 'message-meta';
            timeSpan.textContent = time;
            
            wrapper.appendChild(contentDiv);
            wrapper.appendChild(timeSpan);
            
            // 组装消息
            message.appendChild(avatarContainer);
            message.appendChild(wrapper);
            
            container.appendChild(message);
            container.scrollTop = container.scrollHeight;
            
            messageCount++;
        }
        
        function clearMessages() {
            document.getElementById('chatContainer').innerHTML = '';
            messageCount = 0;
        }
        
        // 初始化一些示例消息
        window.onload = function() {
            setTimeout(() => {
                addTestMessage('小雪', 'e1xk9k.jpeg', '大家好！我是小雪～');
                addTestMessage('阿明', 'g299b6.jpeg', '嗨，小雪！');
                addUserMessage('你们好呀！');
                addTestMessage('莉莉', 'cmegcm.jpeg', '哇，好热闹的群聊！');
            }, 500);
        };
    </script>
</body>
</html>
