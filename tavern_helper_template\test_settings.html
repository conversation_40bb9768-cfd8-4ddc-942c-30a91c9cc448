<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .test-button {
            background: #07c160;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #06ad56;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f8f8;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .avatar-test {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            background: #ddd;
            margin: 10px 0;
            background-size: cover;
            background-position: center;
        }
        .wallpaper-test {
            width: 200px;
            height: 100px;
            border-radius: 8px;
            background: #ddd;
            margin: 10px 0;
            background-size: cover;
            background-position: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>聊天界面设置功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">头像设置测试</div>
            <div class="avatar-test" id="avatarTest"></div>
            <button class="test-button" onclick="testAvatarUpload()">测试头像上传</button>
            <button class="test-button" onclick="testAvatarReset()">重置头像</button>
            <div class="test-result" id="avatarResult">等待测试...</div>
        </div>

        <div class="test-section">
            <div class="test-title">壁纸设置测试</div>
            <div class="wallpaper-test" id="wallpaperTest"></div>
            <button class="test-button" onclick="testWallpaperUpload()">测试壁纸上传</button>
            <button class="test-button" onclick="testWallpaperReset()">重置壁纸</button>
            <div class="test-result" id="wallpaperResult">等待测试...</div>
        </div>

        <div class="test-section">
            <div class="test-title">本地存储测试</div>
            <button class="test-button" onclick="testLocalStorage()">测试本地存储</button>
            <button class="test-button" onclick="clearLocalStorage()">清空本地存储</button>
            <div class="test-result" id="storageResult">等待测试...</div>
        </div>

        <div class="test-section">
            <div class="test-title">打开聊天界面</div>
            <button class="test-button" onclick="openChatInterface()">打开聊天界面</button>
            <div class="test-result">点击按钮打开聊天界面，测试设置功能</div>
        </div>
    </div>

    <input type="file" id="avatarFileInput" accept="image/*" style="display: none;">
    <input type="file" id="wallpaperFileInput" accept="image/*" style="display: none;">

    <script>
        // 模拟设置功能
        const settingsState = {
            userAvatar: null,
            wallpaper: null
        };

        // 从localStorage加载设置
        function loadSettings() {
            const savedAvatar = localStorage.getItem('chatUserAvatar');
            const savedWallpaper = localStorage.getItem('chatWallpaper');
            
            if (savedAvatar) {
                settingsState.userAvatar = savedAvatar;
                updateAvatarTest();
            }
            
            if (savedWallpaper) {
                settingsState.wallpaper = savedWallpaper;
                updateWallpaperTest();
            }
        }

        // 保存设置到localStorage
        function saveSettings() {
            if (settingsState.userAvatar) {
                localStorage.setItem('chatUserAvatar', settingsState.userAvatar);
            }
            if (settingsState.wallpaper) {
                localStorage.setItem('chatWallpaper', settingsState.wallpaper);
            }
        }

        // 更新头像测试显示
        function updateAvatarTest() {
            const avatarTest = document.getElementById('avatarTest');
            if (settingsState.userAvatar) {
                avatarTest.style.backgroundImage = `url(${settingsState.userAvatar})`;
            } else {
                avatarTest.style.backgroundImage = '';
                avatarTest.style.background = '#ddd';
            }
        }

        // 更新壁纸测试显示
        function updateWallpaperTest() {
            const wallpaperTest = document.getElementById('wallpaperTest');
            if (settingsState.wallpaper) {
                wallpaperTest.style.backgroundImage = `url(${settingsState.wallpaper})`;
            } else {
                wallpaperTest.style.backgroundImage = '';
                wallpaperTest.style.background = '#ddd';
            }
        }

        // 处理文件上传
        function handleFileUpload(file, type) {
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const dataUrl = e.target.result;
                
                if (type === 'avatar') {
                    settingsState.userAvatar = dataUrl;
                    updateAvatarTest();
                    document.getElementById('avatarResult').textContent = '头像上传成功！';
                } else if (type === 'wallpaper') {
                    settingsState.wallpaper = dataUrl;
                    updateWallpaperTest();
                    document.getElementById('wallpaperResult').textContent = '壁纸上传成功！';
                }
                
                saveSettings();
            };
            reader.readAsDataURL(file);
        }

        // 测试头像上传
        function testAvatarUpload() {
            document.getElementById('avatarFileInput').click();
        }

        // 测试头像重置
        function testAvatarReset() {
            settingsState.userAvatar = null;
            localStorage.removeItem('chatUserAvatar');
            updateAvatarTest();
            document.getElementById('avatarResult').textContent = '头像已重置！';
        }

        // 测试壁纸上传
        function testWallpaperUpload() {
            document.getElementById('wallpaperFileInput').click();
        }

        // 测试壁纸重置
        function testWallpaperReset() {
            settingsState.wallpaper = null;
            localStorage.removeItem('chatWallpaper');
            updateWallpaperTest();
            document.getElementById('wallpaperResult').textContent = '壁纸已重置！';
        }

        // 测试本地存储
        function testLocalStorage() {
            const avatar = localStorage.getItem('chatUserAvatar');
            const wallpaper = localStorage.getItem('chatWallpaper');
            
            let result = '本地存储状态:\n';
            result += `头像: ${avatar ? '已保存' : '未保存'}\n`;
            result += `壁纸: ${wallpaper ? '已保存' : '未保存'}`;
            
            document.getElementById('storageResult').textContent = result;
        }

        // 清空本地存储
        function clearLocalStorage() {
            localStorage.removeItem('chatUserAvatar');
            localStorage.removeItem('chatWallpaper');
            settingsState.userAvatar = null;
            settingsState.wallpaper = null;
            updateAvatarTest();
            updateWallpaperTest();
            document.getElementById('storageResult').textContent = '本地存储已清空！';
        }

        // 打开聊天界面
        function openChatInterface() {
            window.open('./同层私聊1.html', '_blank');
        }

        // 绑定文件输入事件
        document.getElementById('avatarFileInput').addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                handleFileUpload(file, 'avatar');
            }
        });

        document.getElementById('wallpaperFileInput').addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                handleFileUpload(file, 'wallpaper');
            }
        });

        // 页面加载时加载设置
        loadSettings();
    </script>
</body>
</html>
