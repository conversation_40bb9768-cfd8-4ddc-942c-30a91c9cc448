# 🚀 智能媒体处理助手 - 完整使用流程

## 📋 功能概述

这个插件实现了从同层手机界面上传文件到SillyTavern，自动解析分析文件内容，并让AI模型根据分析结果进行回复的完整流程。

## 🔄 完整工作流程

### 1. 文件上传阶段
```javascript
// 在同层手机界面中调用 - 简化版本，类似图片处理
const result = await top.window.__processFileByPlugin(file, {
  enableAIReading: true,     // 启用AI分析
  sendToChat: true,          // 自动发送到聊天
  aiPrompt: '请分析这个文档', // 自定义提示
  saveToLocal: true          // 保存到本地
});
```

### 2. 插件处理阶段
- ✅ 验证文件格式和大小
- ✅ 读取文档内容（支持txt、md、json、doc、docx等）
- ✅ 使用SillyTavern标准工具保存文件
- ✅ 直接调用generate函数发送内容到聊天

### 3. AI回复阶段
- ✅ 文档内容直接发送到SillyTavern聊天
- ✅ 连接的模型（如Claude、GPT等）立即分析并回复
- ✅ 用户在聊天界面看到AI的分析回复

## 🎯 支持的文件类型

### 📄 文档文件
- **文本文件**: `.txt`, `.md`, `.csv`
- **JSON文件**: `.json`
- **Word文档**: `.doc`, `.docx`
- **HTML/XML**: `.html`, `.xml`

### 🖼️ 图像文件
- **常见格式**: `.jpg`, `.png`, `.gif`, `.webp`
- **自动压缩优化**
- **智能尺寸调整**

## ⚙️ 配置选项

### 基础配置
```javascript
const options = {
  enableAIReading: true,    // 是否启用AI分析
  sendToChat: true,         // 是否自动发送到聊天
  sendRawContent: false,    // 是否发送原始内容
  aiPrompt: '自定义提示'     // AI分析提示词
};
```

### 高级配置
- `maxFileSize`: 最大文件大小限制
- `compressionMode`: 图像压缩模式
- `enableLogging`: 启用详细日志
- `showProcessingInfo`: 显示处理信息

## 🔧 集成示例

### 同层手机界面集成
```html
<input type="file" id="fileInput" accept=".txt,.md,.json,.doc,.docx,.jpg,.png">
<div id="uploadStatus"></div>

<script>
document.getElementById('fileInput').addEventListener('change', async (e) => {
  const file = e.target.files[0];
  if (!file) return;
  
  try {
    const result = await top.window.__processFileByPlugin(file, {
      enableAIReading: true,
      sendToChat: true,
      aiPrompt: '请详细分析这个文档的内容，提供总结和关键见解'
    });
    
    if (result.success) {
      showStatus('✅ 文档已上传并分析完成，AI正在生成回复...');
    }
  } catch (error) {
    showStatus('❌ 处理失败: ' + error.message);
  }
});

function showStatus(message) {
  document.getElementById('uploadStatus').textContent = message;
}
</script>
```

## 🚀 使用步骤

### 步骤1: 安装插件
1. 将插件文件放入SillyTavern的扩展目录
2. 在SillyTavern中启用插件
3. 配置插件设置

### 步骤2: 在同层手机界面中集成
1. 添加文件上传控件
2. 调用插件的处理函数
3. 处理返回结果

### 步骤3: 上传和分析
1. 用户在手机界面选择文件
2. 插件自动解析文件内容
3. AI分析文档内容
4. 分析结果发送到聊天

### 步骤4: AI回复
1. SillyTavern接收分析结果
2. 连接的AI模型处理内容
3. 生成相应的回复
4. 用户看到AI的分析和回复

## 🎨 界面特性

### 插件设置界面
- 🎯 简洁的折叠式设计
- 📱 完美适配移动端
- ⚙️ 丰富的配置选项
- 💾 自动保存设置状态

### 聊天集成
- 📄 美观的消息格式
- 🤖 自动AI分析标识
- 📊 详细的处理信息
- 🔄 实时状态更新

## 🛠️ 技术特性

### 核心功能
- **多格式支持**: 自动识别文件类型
- **智能压缩**: 图像自动优化
- **AI集成**: 调用SillyTavern内置函数
- **聊天集成**: 自动发送分析结果

### 性能优化
- **异步处理**: 不阻塞界面操作
- **错误处理**: 完善的异常捕获
- **内存管理**: 自动清理临时数据
- **缓存机制**: 提高处理效率

## 📝 注意事项

1. **文件大小限制**: 默认最大20MB
2. **格式支持**: 确保文件格式在支持列表中
3. **AI功能**: 需要SillyTavern连接AI模型
4. **网络连接**: 需要稳定的网络环境

## 🔍 故障排除

### 常见问题
- **插件未加载**: 检查扩展目录和权限
- **文件处理失败**: 确认文件格式和大小
- **AI分析失败**: 检查SillyTavern AI连接
- **聊天发送失败**: 确认聊天界面状态

### 调试方法
1. 打开浏览器开发者工具
2. 查看控制台日志信息
3. 检查网络请求状态
4. 验证插件配置设置

## 🎉 总结

这个插件实现了完整的文档处理和AI分析流程：

**同层手机上传** → **插件解析** → **AI分析** → **聊天发送** → **模型回复**

用户只需要在手机界面上传文件，就能自动获得AI的详细分析和回复，大大提升了使用体验！
