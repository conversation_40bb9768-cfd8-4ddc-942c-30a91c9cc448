.tavern-task-container {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 320px;
    max-height: 80vh;
    overflow-y: auto;
    z-index: 1000;
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    // 响应式设计
    @media (max-width: 768px) {
        position: relative;
        top: auto;
        right: auto;
        width: 100%;
        max-width: 400px;
        margin: 20px auto;
        max-height: none;
    }

    // 滚动条样式
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3px;

        &:hover {
            background: rgba(255, 255, 255, 0.5);
        }
    }

    .task-section {
        margin-bottom: 15px;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 10px;
        padding: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:last-child {
            margin-bottom: 0;
        }

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 6px;
            border-bottom: 2px solid;
            text-align: center;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                left: 50%;
                bottom: -2px;
                transform: translateX(-50%);
                width: 30px;
                height: 2px;
                background: currentColor;
                border-radius: 1px;
            }
        }

        &.accepted-tasks .section-title {
            color: #e74c3c;
            border-color: #e74c3c;
        }

        &.available-tasks .section-title {
            color: #f39c12;
            border-color: #f39c12;
        }

        &.story-tasks .section-title {
            color: #9b59b6;
            border-color: #9b59b6;
        }
    }

    .task-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 8px;
        border-left: 4px solid;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;

        &:last-child {
            margin-bottom: 0;
        }

        &:hover {
            transform: translateX(5px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);

            &::before {
                opacity: 1;
            }
        }

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        &.accepted-task {
            border-left-color: #e74c3c;
            background: linear-gradient(90deg, #ffe6e6 0%, #f8f9fa 20%);

            &:hover {
                background: linear-gradient(90deg, #ffcccc 0%, #f0f0f0 20%);
            }
        }

        &.available-task {
            border-left-color: #f39c12;
            background: linear-gradient(90deg, #fff3e0 0%, #f8f9fa 20%);

            &:hover {
                background: linear-gradient(90deg, #ffe0b3 0%, #f0f0f0 20%);
            }
        }

        &.story-task {
            border-left-color: #9b59b6;
            background: linear-gradient(90deg, #f3e5f5 0%, #f8f9fa 20%);

            &:hover {
                background: linear-gradient(90deg, #e6cceb 0%, #f0f0f0 20%);
            }
        }
    }

    .task-content {
        font-size: 13px;
        line-height: 1.4;
        color: #2c3e50;
        word-wrap: break-word;
        margin-top: 5px;
    }

    .empty-section {
        text-align: center;
        color: #7f8c8d;
        font-style: italic;
        padding: 15px;
        font-size: 12px;
    }

    .task-status {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 10px;
        font-weight: bold;
        margin-bottom: 5px;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.status-accepted {
            background: #e74c3c;
            color: white;
            box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
        }

        &.status-available {
            background: #f39c12;
            color: white;
            box-shadow: 0 2px 4px rgba(243, 156, 18, 0.3);
        }

        &.status-story {
            background: #9b59b6;
            color: white;
            box-shadow: 0 2px 4px rgba(155, 89, 182, 0.3);
        }
    }

    // 动画效果
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: scale(0.9);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    // 初始动画
    animation: slideInRight 0.5s ease-out;

    .task-item {
        animation: fadeIn 0.3s ease-out;
    }

    // 暗色主题适配
    @media (prefers-color-scheme: dark) {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        
        .task-section {
            background: rgba(52, 73, 94, 0.95);
            color: #ecf0f1;
        }

        .task-item {
            background: #34495e;
            color: #ecf0f1;

            &.accepted-task {
                background: linear-gradient(90deg, #4a2c2a 0%, #34495e 20%);
            }

            &.available-task {
                background: linear-gradient(90deg, #4a3c2a 0%, #34495e 20%);
            }

            &.story-task {
                background: linear-gradient(90deg, #3e2a4a 0%, #34495e 20%);
            }
        }

        .task-content {
            color: #bdc3c7;
        }

        .empty-section {
            color: #95a5a6;
        }
    }

    // 高对比度模式
    @media (prefers-contrast: high) {
        border: 2px solid #000;
        
        .task-item {
            border: 1px solid #000;
        }

        .task-status {
            border: 1px solid #000;
        }
    }

    // 减少动画模式
    @media (prefers-reduced-motion: reduce) {
        animation: none;
        
        .task-item {
            animation: none;
            transition: none;

            &:hover {
                transform: none;
            }
        }

        .task-section {
            transition: none;

            &:hover {
                transform: none;
            }
        }
    }
}
