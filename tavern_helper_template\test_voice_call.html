<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音通话测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-bottom: 10px;
            font-family: monospace;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .format-example {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>语音通话消息格式测试</h1>
        
        <div class="test-section">
            <div class="test-title">📱 语音通话消息格式说明</div>
            <div class="format-example">
                <strong>用户消息格式：</strong><br>
                [我方消息|语音通话|{{消息内容}}|{{消息时间}}]
                <br><br>
                <strong>角色消息格式（带头像）：</strong><br>
                [{{角色昵称}}|{{对方头像}}|语音通话|{{消息内容}}|{{消息时间}}]
                <br><br>
                <strong>角色消息格式（不带头像）：</strong><br>
                [{{角色昵称}}|语音通话|{{消息内容}}|{{消息时间}}]
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 测试消息解析</div>
            <textarea class="test-input" id="testMessage" rows="3" placeholder="输入要测试的消息格式...">
[小美|https://example.com/avatar.jpg|语音通话|你好，我是小美！|14:30]</textarea>
            <button class="test-button" onclick="testMessageParsing()">解析消息</button>
            <button class="test-button" onclick="clearOutput()">清空输出</button>
            <div class="test-output" id="parseOutput"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📋 预设测试用例</div>
            <button class="test-button" onclick="testCase1()">用户消息</button>
            <button class="test-button" onclick="testCase2()">角色消息（带头像）</button>
            <button class="test-button" onclick="testCase3()">角色消息（不带头像）</button>
            <button class="test-button" onclick="testCase4()">通话结束消息</button>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 实际效果预览</div>
            <p>在实际的聊天界面中，语音通话消息应该显示：</p>
            <ul>
                <li>✅ 角色头像（32x32像素，圆角）</li>
                <li>✅ 角色昵称（小字体，半透明）</li>
                <li>✅ 消息内容（在气泡中）</li>
                <li>✅ 正确的左右对齐（用户右对齐，角色左对齐）</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟消息解析器
        const messagePatterns = [
            {
                name: '用户语音通话消息',
                regex: /^\[我方消息\|语音通话\|(.*?)\|(\d{2}:\d{2})\]$/,
                handler: ([, content, time]) => ({
                    sender: 'user',
                    content,
                    time,
                    callContext: true,
                    type: 'text'
                })
            },
            {
                name: '角色语音通话消息（带头像）',
                regex: /^\[(.+?)\|(.+?)\|语音通话\|(.*?)\|(\d{2}:\d{2})\]$/,
                handler: ([, charName, avatar, content, time]) => ({
                    sender: 'char',
                    charName,
                    avatar,
                    content,
                    time,
                    callContext: true,
                    type: 'text'
                })
            },
            {
                name: '角色语音通话消息（不带头像）',
                regex: /^\[(.+?)\|语音通话\|(.*?)\|(\d{2}:\d{2})\]$/,
                handler: ([, charName, content, time]) => ({
                    sender: 'char',
                    charName,
                    content,
                    time,
                    callContext: true,
                    type: 'text'
                })
            },
            {
                name: '语音通话结束消息（带头像）',
                regex: /^\[(.+?)\|(.+?)\|语音通话已挂断\|(.*?)\|(.*?)\|(\d{2}:\d{2})\]$/,
                handler: ([, charName, avatar, duration, transcriptJson, time]) => {
                    let transcript = [];
                    try {
                        transcript = JSON.parse(transcriptJson);
                    } catch (e) {
                        // ignore parse error
                    }
                    return {
                        sender: 'char',
                        type: 'voicecall-end',
                        charName,
                        avatar,
                        duration,
                        transcript,
                        time
                    };
                }
            }
        ];

        function testMessageParsing() {
            const input = document.getElementById('testMessage').value.trim();
            const output = document.getElementById('parseOutput');
            
            if (!input) {
                output.textContent = '请输入要测试的消息';
                return;
            }

            let matched = false;
            let result = '';

            for (const pattern of messagePatterns) {
                const match = input.match(pattern.regex);
                if (match) {
                    matched = true;
                    const parsed = pattern.handler(match);
                    result += `✅ 匹配模式: ${pattern.name}\n`;
                    result += `📝 解析结果:\n${JSON.stringify(parsed, null, 2)}\n\n`;
                    
                    // 显示在语音通话界面中的效果
                    result += `🎯 语音通话界面显示效果:\n`;
                    if (parsed.sender === 'user') {
                        result += `[用户消息] ${parsed.content}\n`;
                    } else {
                        result += `[${parsed.charName || '角色'}] ${parsed.content}\n`;
                        if (parsed.avatar) {
                            result += `头像: ${parsed.avatar}\n`;
                        }
                    }
                    break;
                }
            }

            if (!matched) {
                result = '❌ 未匹配任何已知格式\n\n';
                result += '请检查消息格式是否正确。支持的格式:\n';
                result += '1. [我方消息|语音通话|内容|时间]\n';
                result += '2. [角色名|头像|语音通话|内容|时间]\n';
                result += '3. [角色名|语音通话|内容|时间]\n';
            }

            output.textContent = result;
        }

        function clearOutput() {
            document.getElementById('parseOutput').textContent = '';
        }

        function testCase1() {
            document.getElementById('testMessage').value = '[我方消息|语音通话|你好，我想问个问题|14:25]';
            testMessageParsing();
        }

        function testCase2() {
            document.getElementById('testMessage').value = '[小美|https://example.com/avatar.jpg|语音通话|你好！有什么可以帮助你的吗？|14:26]';
            testMessageParsing();
        }

        function testCase3() {
            document.getElementById('testMessage').value = '[小美|语音通话|好的，我明白了|14:27]';
            testMessageParsing();
        }

        function testCase4() {
            document.getElementById('testMessage').value = '[小美|https://example.com/avatar.jpg|语音通话已挂断|02:35|[]|14:30]';
            testMessageParsing();
        }
    </script>
</body>
</html>
