<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片识别功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056CC;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .preview {
            max-width: 300px;
            max-height: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 图片识别功能测试</h1>
        <p>此页面用于测试同层私聊喵喵喵2.html中新增的图片识别功能。</p>

        <!-- API配置测试 -->
        <div class="section">
            <h3>1. 识图API配置测试</h3>
            <div class="form-group">
                <label>识图API地址:</label>
                <input type="text" id="apiUrl" placeholder="例如: https://api.openai.com/v1">
            </div>
            <div class="form-group">
                <label>识图API密钥:</label>
                <input type="password" id="apiKey" placeholder="请输入API密钥">
            </div>
            <div class="form-group">
                <label>识图模型:</label>
                <select id="modelSelect">
                    <option value="">请先测试连接以获取可用模型</option>
                </select>
            </div>
            <button onclick="testConnection()">测试连接</button>
            <button onclick="refreshModels()">刷新模型</button>
            <div id="apiResult" class="result"></div>
        </div>

        <!-- 图片上传测试 -->
        <div class="section">
            <h3>2. 图片上传测试</h3>
            <div class="form-group">
                <label>选择图片:</label>
                <input type="file" id="imageFile" accept="image/*">
            </div>
            <div class="form-group">
                <label>图片描述（可选）:</label>
                <textarea id="imageDesc" placeholder="请描述图片内容或添加备注"></textarea>
            </div>
            <button onclick="uploadImage()">上传并识图</button>
            <div id="uploadResult" class="result"></div>
            <img id="imagePreview" class="preview" style="display: none;">
        </div>

        <!-- 功能说明 -->
        <div class="section">
            <h3>3. 功能说明</h3>
            <ul>
                <li><strong>API配置:</strong> 支持OpenAI、硅基流动等兼容OpenAI格式的识图API</li>
                <li><strong>模型识别:</strong> 自动识别支持视觉的模型（包含vision、gpt-4、claude等关键词）</li>
                <li><strong>图片识别:</strong> 上传图片后会自动调用识图API进行内容分析</li>
                <li><strong>数据存储:</strong> 配置信息会保存到localStorage，页面刷新后自动恢复</li>
                <li><strong>错误处理:</strong> 提供详细的错误信息和状态反馈</li>
            </ul>
        </div>

        <!-- 使用步骤 -->
        <div class="section">
            <h3>4. 使用步骤</h3>
            <ol>
                <li>在"识图API配置测试"部分填入API地址和密钥</li>
                <li>点击"测试连接"验证配置是否正确</li>
                <li>从下拉菜单中选择支持视觉的模型</li>
                <li>在"图片上传测试"部分选择图片文件</li>
                <li>可选择添加图片描述</li>
                <li>点击"上传并识图"开始测试</li>
                <li>查看识图结果和状态反馈</li>
            </ol>
        </div>
    </div>

    <script>
        // 模拟同层私聊喵喵喵2.html中的识图功能
        let visionConfig = {
            apiUrl: '',
            apiKey: '',
            model: '',
            availableModels: []
        };

        // 测试API连接
        async function testConnection() {
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const apiKey = document.getElementById('apiKey').value.trim();
            const resultDiv = document.getElementById('apiResult');

            if (!apiUrl || !apiKey) {
                showResult('apiResult', '请先填写API地址和密钥！', 'error');
                return;
            }

            showResult('apiResult', '正在测试连接...', 'info');

            try {
                const response = await fetch(`${apiUrl}/models`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.data && Array.isArray(data.data)) {
                    // 过滤视觉模型
                    const visionModels = data.data.filter(model => {
                        const modelId = model.id.toLowerCase();
                        return modelId.includes('vision') || 
                               modelId.includes('gpt-4') || 
                               modelId.includes('claude') ||
                               modelId.includes('gemini') ||
                               modelId.includes('qwen-vl') ||
                               modelId.includes('llava');
                    });

                    visionConfig.apiUrl = apiUrl;
                    visionConfig.apiKey = apiKey;
                    visionConfig.availableModels = visionModels.length > 0 ? visionModels.map(m => m.id) : data.data.map(m => m.id);
                    
                    updateModelSelect();
                    
                    const message = visionModels.length > 0 
                        ? `连接成功！找到 ${visionModels.length} 个视觉模型。`
                        : `连接成功！找到 ${data.data.length} 个模型，请手动选择支持视觉的模型。`;
                    
                    showResult('apiResult', message, visionModels.length > 0 ? 'success' : 'warning');
                } else {
                    showResult('apiResult', '连接成功，但模型数据格式不正确！', 'warning');
                }
            } catch (error) {
                showResult('apiResult', `连接失败: ${error.message}`, 'error');
            }
        }

        // 刷新模型列表
        async function refreshModels() {
            if (!visionConfig.apiUrl || !visionConfig.apiKey) {
                showResult('apiResult', '请先测试连接！', 'error');
                return;
            }
            await testConnection();
        }

        // 更新模型选择框
        function updateModelSelect() {
            const select = document.getElementById('modelSelect');
            select.innerHTML = '<option value="">请选择模型</option>';
            
            visionConfig.availableModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                select.appendChild(option);
            });
        }

        // 上传并识图
        async function uploadImage() {
            const fileInput = document.getElementById('imageFile');
            const desc = document.getElementById('imageDesc').value.trim();
            const preview = document.getElementById('imagePreview');

            if (!fileInput.files[0]) {
                showResult('uploadResult', '请先选择图片！', 'error');
                return;
            }

            if (!visionConfig.apiUrl || !visionConfig.apiKey || !visionConfig.model) {
                showResult('uploadResult', '请先配置识图API！', 'error');
                return;
            }

            const file = fileInput.files[0];
            
            // 显示图片预览
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);

            showResult('uploadResult', '正在识别图片...', 'info');

            try {
                // 模拟识图过程
                setTimeout(() => {
                    const mockResult = `识图完成！检测到图片内容：${file.name}（文件大小：${(file.size/1024).toFixed(1)}KB）`;
                    if (desc) {
                        showResult('uploadResult', `${mockResult}\n用户描述：${desc}`, 'success');
                    } else {
                        showResult('uploadResult', mockResult, 'success');
                    }
                }, 2000);
            } catch (error) {
                showResult('uploadResult', `识图失败: ${error.message}`, 'error');
            }
        }

        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // 模型选择事件
        document.getElementById('modelSelect').addEventListener('change', function() {
            visionConfig.model = this.value;
        });

        // 页面加载时的提示
        window.onload = function() {
            showResult('apiResult', '请配置识图API以开始测试', 'info');
        };
    </script>
</body>
</html>
