<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>伊莱恩日记 - 羊皮纸卷</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      /* 楷书字体设置 */
      @font-face {
        font-family: 'KaiShu';
        src: local('楷体'), local('KaiTi'), local('STKaiti'), local('DFKai-SB');
        font-weight: normal;
        font-style: normal;
      }

      body {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        /* 深沉的科技背景色，暗示未来世界的冷漠 */
        background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 50%, #0f0f0f 100%);
        padding: 20px 10px;
        font-family: 'KaiShu', '楷体', 'STKaiti', 'DFKai-SB', serif;
        position: relative;
        perspective: 1000px;
      }

      /* 添加微妙的科技感光效 */
      body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 20% 80%, rgba(0, 100, 200, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(100, 0, 200, 0.1) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
      }

      /* 日记本主容器 */
      .diary-book {
        position: relative;
        width: 400px;
        height: 500px;
        transform-style: preserve-3d;
        cursor: pointer;
        transition: transform 0.6s ease-in-out;
      }

      /* 日记本封面 */
      .book-cover {
        position: absolute;
        width: 100%;
        height: 100%;
        background:
          /* 皮质纹理 */ radial-gradient(
            circle at 30% 30%,
            rgba(101, 67, 33, 0.3) 0%,
            transparent 50%
          ),
          radial-gradient(circle at 70% 70%, rgba(139, 101, 67, 0.2) 0%, transparent 40%),
          /* 主要皮革色调 */ linear-gradient(135deg, #8b5a3c 0%, #6d4c41 30%, #5d4037 70%, #4e342e 100%);

        border: 3px solid #3e2723;
        border-radius: 8px;
        box-shadow: inset 0 0 30px rgba(62, 39, 35, 0.4), 0 10px 30px rgba(0, 0, 0, 0.6);

        transform-origin: left center;
        transition: transform 0.8s ease-in-out;
        z-index: 2;

        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        padding: 40px;
      }

      /* 封面标题 */
      .cover-title {
        font-size: 28px;
        color: #d7ccc8;
        letter-spacing: 3px;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        font-weight: bold;
      }

      .cover-subtitle {
        font-size: 16px;
        color: #bcaaa4;
        letter-spacing: 2px;
        font-style: italic;
        margin-bottom: 30px;
      }

      .cover-ornament {
        width: 80px;
        height: 80px;
        border: 2px solid #d7ccc8;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 20px auto;
        background: radial-gradient(circle, rgba(215, 204, 200, 0.1) 0%, transparent 70%);
      }

      .cover-ornament::before {
        content: '✦';
        font-size: 24px;
        color: #d7ccc8;
      }

      /* 翻页状态 */
      .diary-book.opened .book-cover {
        transform: rotateY(-180deg);
      }

      /* 日记内页 - 羊皮纸效果 */
      .diary-pages {
        position: absolute;
        width: 100%;
        height: 100%;
        background:
          /* 微妙的纹理点缀 */ radial-gradient(
            circle at 25% 25%,
            rgba(218, 204, 186, 0.15) 0%,
            transparent 40%
          ),
          radial-gradient(circle at 75% 75%, rgba(230, 220, 200, 0.12) 0%, transparent 35%),
          /* 主要背景色 - 温暖的米色调 */ linear-gradient(135deg, #faf8f3 0%, #f5f1ea 30%, #f0ebe0 70%, #ebe4d6 100%);

        border: 2px solid #d4c5a0;
        border-radius: 6px;
        box-shadow: inset 0 0 15px rgba(218, 204, 186, 0.2), 0 3px 10px rgba(0, 0, 0, 0.15);

        /* 纸张纹理 */
        background-size: 300px 300px, 200px 200px, 100% 100%;

        /* 初始状态隐藏 */
        opacity: 0;
        transform: scale(0.9);
        transition: opacity 0.5s ease-in-out 0.3s, transform 0.5s ease-in-out 0.3s;
        z-index: 1;
      }

      /* 打开状态显示内页 */
      .diary-book.opened .diary-pages {
        opacity: 1;
        transform: scale(1);
      }

      .parchment-container {
        width: 100%;
        height: 100%;
        padding: 30px;
        position: relative;
        background: transparent;
        border: none;
        box-shadow: none;
        overflow-y: auto;
      }

      /* 清爽的纸张纹理效果 */
      .parchment-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        /* 淡雅的横线纹理，模拟优质纸张 */
        background-image: linear-gradient(transparent 29px, rgba(218, 204, 186, 0.25) 30px),
          linear-gradient(90deg, transparent 49px, rgba(218, 204, 186, 0.15) 50px);
        background-size: 50px 30px;
        opacity: 0.5;
        pointer-events: none;
        border-radius: 6px;
      }

      /* 移除磨损效果，保持干净整洁 */

      .parchment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 2px solid rgba(139, 119, 101, 0.4);
        position: relative;
      }

      .diary-date {
        font-size: 18px;
        color: #6b5d4a;
        letter-spacing: 2px;
        font-weight: bold;
        text-shadow: 0.5px 0.5px 1px rgba(218, 204, 186, 0.4);
      }

      .diary-weather {
        font-size: 16px;
        color: #7a6c58;
        letter-spacing: 1px;
        font-style: italic;
      }

      .parchment-content {
        font-size: 17px;
        line-height: 30px; /* 与横线高度对应 */
        color: #4a3d2a; /* 温和的墨色，保持隐忍但不过于沉重 */
        margin: 20px 0;
        text-align: justify;
        letter-spacing: 1.5px;
        position: relative;
        text-shadow: 0.5px 0.5px 1px rgba(218, 204, 186, 0.3);
        /* 楷书特有的字间距和行间距 */
        word-spacing: 2px;
        text-indent: 2em; /* 首行缩进 */
      }

      .parchment-footer {
        text-align: right;
        font-size: 15px;
        color: #6b5d4a;
        margin-top: 20px;
        padding-top: 12px;
        border-top: 1px solid rgba(218, 204, 186, 0.5);
        letter-spacing: 2px;
        position: relative;
        font-style: italic;
        /* 体现沉闷性格的签名 */
      }

      /* 清雅的装饰元素 */
      .parchment-ornament {
        position: absolute;
        width: 50px;
        height: 50px;
        opacity: 0.2;
        background: radial-gradient(circle, rgba(218, 204, 186, 0.4) 40%, transparent 70%);
        border-radius: 50%;
      }

      .ornament-top-left {
        top: -10px;
        left: -10px;
        background: radial-gradient(circle at 60% 60%, rgba(218, 204, 186, 0.3) 0%, transparent 60%);
      }

      .ornament-bottom-right {
        bottom: -10px;
        right: -10px;
        background: radial-gradient(circle at 40% 40%, rgba(218, 204, 186, 0.3) 0%, transparent 60%);
      }

      /* 优雅的装订线 */
      .binding-line {
        position: absolute;
        left: 50px;
        top: 0;
        bottom: 0;
        width: 1px;
        background: linear-gradient(
          to bottom,
          rgba(218, 204, 186, 0.4) 0%,
          rgba(230, 220, 200, 0.3) 50%,
          rgba(218, 204, 186, 0.4) 100%
        );
        opacity: 0.6;
        border-radius: 0.5px;
      }

      @media screen and (max-width: 480px) {
        .parchment-container {
          width: 95%;
          max-width: 350px;
          padding: 25px;
        }

        .parchment-frame {
          padding: 30px;
        }

        .parchment-content {
          font-size: 16px;
          line-height: 30px;
          letter-spacing: 1px;
        }

        .diary-date {
          font-size: 16px;
        }

        .diary-weather {
          font-size: 14px;
        }

        .parchment-footer {
          font-size: 14px;
        }

        .binding-line {
          left: 40px;
        }
      }
    </style>
  </head>
  <body>
    <div class="diary-book" id="diaryBook">
      <!-- 封面 -->
      <div class="book-cover">
        <div class="cover-title">伊莱恩</div>
        <div class="cover-subtitle">私人日记</div>
        <div class="cover-ornament"></div>
        <div class="cover-subtitle" style="margin-top: 20px; font-size: 14px; opacity: 0.8">点击翻开</div>
      </div>

      <!-- 内页 -->
      <div class="diary-pages">
        <div class="parchment-container">
          <div class="binding-line"></div>
          <div class="parchment-header">
            <div class="diary-date">$1</div>
            <div class="diary-weather">$2</div>
          </div>
          <div class="parchment-content">$3</div>
          <div class="parchment-footer">——写点东西吧</div>
        </div>
      </div>
    </div>

    <script>
      // 日记本翻页交互
      const diaryBook = document.getElementById('diaryBook');

      diaryBook.addEventListener('click', function () {
        this.classList.toggle('opened');
      });

      // 添加键盘交互
      document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
          diaryBook.classList.remove('opened');
        } else if (e.key === 'Enter' || e.key === ' ') {
          diaryBook.classList.toggle('opened');
        }
      });
    </script>
  </body>
</html>
