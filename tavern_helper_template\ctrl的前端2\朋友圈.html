<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>朋友圈</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dompurify/3.0.6/purify.min.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .phone-container {
        width: min(100%, 360px);
        border: 20px solid #fdf5e6;
        border-radius: 36px;
        background-color: #1c1c1c;
        padding: 3px;
        position: relative;
        box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
      }

      .status-bar {
        height: 32px;
        color: #ffffff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
        font-size: 14px;
        position: absolute;
        top: 3px;
        left: 3px;
        right: 3px;
        z-index: 998;
        font-weight: 500;
      }

      .status-bar-left {
        display: flex;
        align-items: center;
        gap: 5px;
      }
      .status-bar-right {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      .status-icon {
        height: 16px;
        width: 16px;
        filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.4));
      }
      .signal-text {
        font-weight: 600;
        font-size: 13px;
        letter-spacing: -0.5px;
      }
      .battery-icon {
        width: 24px;
        height: 11px;
        border: 1.5px solid #fff;
        border-radius: 3px;
        padding: 1px;
        position: relative;
        filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.4));
      }
      .battery-icon::after {
        content: '';
        position: absolute;
        right: -3px;
        top: 50%;
        transform: translateY(-50%);
        width: 1.5px;
        height: 4px;
        background: #fff;
        border-radius: 0 1px 1px 0;
      }
      .battery-level {
        height: 100%;
        width: 80%;
        background: #fff;
        border-radius: 1px;
      }

      .back-btn {
        position: absolute;
        left: 16px;
        top: 25px;
        width: 24px;
        height: 24px;
        color: #fff;
        cursor: pointer;
        transition: transform 0.2s;
        z-index: 100;
      }

      .back-btn:hover {
        transform: scale(1.05);
      }

      .back-btn:active {
        transform: scale(0.95);
      }

      .back-btn svg {
        width: 100%;
        height: 100%;
        fill: currentColor;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
      }

      .content-container {
        width: 100%;
        height: 560px;
        background: #f5f5f5;
        border-radius: 24px;
        overflow-x: hidden;
        overflow-y: auto;
        font-family: 'Microsoft JhengHei', 'Noto Sans KR', sans-serif;
        position: relative; /* 修正：确保加载动画在容器内定位 */
      }

      .content-container::-webkit-scrollbar {
        width: 0;
        background: transparent;
      }

      .content-container::-webkit-scrollbar-track {
        background: transparent;
      }

      .content-container::-webkit-scrollbar-thumb {
        background: transparent;
      }

      .profile-header {
        height: 240px;
        min-height: 240px;
        width: 100%;
        background-image: url('https://files.catbox.moe/6d0pez.jpeg');
        background-size: cover;
        background-position: center;
        position: relative;
        flex-shrink: 0;
      }

      .profile-info {
        position: absolute;
        bottom: -6px;
        right: 15px;
        display: flex;
        align-items: flex-end;
        gap: 10px;
      }

      .profile-name {
        color: #fff;
        font-size: 16px;
        font-weight: 500;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        margin-bottom: 5px;
        position: relative;
        bottom: 17px;
      }

      .profile-avatar {
        width: 50px;
        height: 50px;
        border-radius: 5px;
        border: 2px solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .user_avatar.avatar {
        width: 50px;
        height: 50px;
        border-radius: 5px;
        border: 2px solid rgba(255, 255, 255, 0.8);
      }

      .profile-signature {
        position: absolute;
        top: 250px;
        right: -28px;
        color: #333;
        font-size: 12px;
        max-width: 200px;
        text-align: right;
        padding-right: 75px;
      }

      .moments-list {
        height: auto;
        min-height: calc(100% - 240px);
        width: 100%;
        padding: 45px 0 0;
        background: #fff;
        overflow-x: hidden;
      }

      .moments-list::-webkit-scrollbar {
        width: 0;
        background: transparent;
      }

      .moments-list::-webkit-scrollbar-track {
        background: transparent;
      }

      .moments-list::-webkit-scrollbar-thumb {
        background: transparent;
      }

      .moment-item {
        display: flex;
        padding: 15px 15px 10px;
        border-bottom: 1px solid #eee;
        width: 100%;
        gap: 10px;
        position: relative;
      }

      .moment-avatar {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        flex-shrink: 0;
        background-color: #eee;
      }

      .user_avatar.avatar {
        width: 50px;
        height: 50px;
        border-radius: 5px;
        border: 2px solid rgba(255, 255, 255, 0.8);
      }

      /* 添加朋友圈头像样式 */
      .moment-item .user_avatar.avatar {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        border: none;
        flex-shrink: 0;
      }

      .moment-details {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        width: calc(100% - 55px);
      }

      .moment-username {
        font-size: 15px;
        font-weight: 500;
        color: #576b95;
        margin-bottom: 5px;
      }

      .moment-content {
        font-size: 15px;
        color: #333;
        line-height: 1.5;
        margin-bottom: 8px;
        width: 100%;
        word-wrap: break-word;
        overflow-wrap: break-word;
        white-space: pre-wrap;
      }

      .moment-time {
        font-size: 12px;
        color: #999;
      }

      .moment-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;
        margin-bottom: 5px;
      }

      .moment-comments {
        background-color: #f7f7f7;
        border-radius: 4px;
        padding: 8px 10px;
        margin-top: 5px;
        width: 100%;
      }

      .comment-item {
        font-size: 14px;
        line-height: 1.4;
        margin-bottom: 3px;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      .comment-item:last-child {
        margin-bottom: 0;
      }

      .comment-username {
        font-weight: 500;
        color: #576b95;
        margin-right: 4px;
      }

      .comment-text {
        color: #333;
      }

      .moments-end {
        display: none;
      }

      /* 添加发表朋友圈界面的样式 */
      .post-moment-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: white;
        z-index: 1000;
        display: none;
        flex-direction: column;
        border-radius: 24px;
        overflow: hidden;
      }

      .post-moment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background-color: #f8f8f8;
        border-bottom: 1px solid #eaeaea;
      }

      .post-moment-back {
        width: 24px;
        height: 24px;
        cursor: pointer;
      }

      .post-moment-back svg {
        width: 100%;
        height: 100%;
        fill: #333;
      }

      .post-moment-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .post-moment-publish {
        background-color: #07c160;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 5px 12px;
        font-size: 14px;
        cursor: pointer;
      }

      .post-moment-publish:disabled {
        background-color: #a5d6a7;
        cursor: not-allowed;
      }

      .post-moment-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 15px;
        overflow-y: auto;
      }

      .post-moment-textarea {
        width: 100%;
        height: 150px;
        border: none;
        resize: none;
        font-size: 16px;
        line-height: 1.5;
        margin-bottom: 20px;
        outline: none;
        font-family: inherit;
      }

      .post-moment-textarea::placeholder {
        color: #999;
      }

      .post-moment-options {
        margin-top: auto;
      }

      .post-moment-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f1f1f1;
        color: #333;
        font-size: 14px;
      }

      .post-moment-option:last-child {
        border-bottom: none;
      }

      .post-moment-option-right {
        color: #999;
        display: flex;
        align-items: center;
      }

      .post-moment-option-right svg {
        width: 16px;
        height: 16px;
        fill: #999;
        margin-left: 5px;
      }

      /* 统一圆形头像样式 */
      .avatar,
      .profile-avatar,
      .moment-avatar,
      .moment-item .user_avatar.avatar {
        border-radius: 50% !important;
        overflow: hidden;
        box-sizing: border-box;
      }

      .moment-likes {
        font-size: 13px;
        color: #576b95;
        margin-bottom: 4px;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      /* 点赞 & 评论操作按钮 */
      .moment-actions {
        position: relative; /* for popup positioning */
      }
      .action-btn {
        width: 25px;
        height: 15px;
        background: #f7f7f7;
        border: 1px solid #eee;
        border-radius: 3px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 3px;
      }
      .action-btn:hover {
        background: #e9e9e9;
      }
      .action-btn::before,
      .action-btn::after {
        content: '';
        display: block;
        width: 3px;
        height: 3px;
        background: #576b95;
        border-radius: 50%;
        margin: 0;
      }
      .like-comment-popup {
        position: absolute;
        right: 100%;
        top: 50%;
        transform: translate(-10px, -50%);
        display: none; /* Hidden by default */
        align-items: center;
        background: #fff;
        border-radius: 5px;
        font-size: 14px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        white-space: nowrap;
        z-index: 10;
      }
      .like-comment-popup.show {
        display: flex; /* Shown with JS */
      }
      .like-comment-popup button {
        background: none;
        border: none;
        color: #555;
        padding: 8px 12px;
        cursor: pointer;
        font-size: 13px;
      }
      .like-comment-popup button:hover {
        background: #f5f5f5;
      }
      .like-comment-popup .like-btn {
        border-right: 1px solid #eee;
      }

      /* 评论输入栏 */
      .comment-input-bar {
        position: absolute;
        left: 3px;
        right: 3px;
        bottom: 3px;
        background: #f7f7f7;
        border-top: 1px solid #d7d7d7;
        padding: 8px;
        display: none;
        gap: 5px;
        align-items: center;
        z-index: 1001;
        border-bottom-left-radius: 21px;
        border-bottom-right-radius: 21px;
        width: auto;
      }
      .comment-input-bar input {
        flex: 1;
        min-width: 0; /* 新增：防止flex项目在收缩时溢出容器 */
        border: none;
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 14px;
        outline: none;
      }
      .comment-input-bar button {
        border: none;
        border-radius: 6px;
        padding: 6px 14px;
        font-size: 14px;
        cursor: pointer;
      }
      .comment-input-bar .send-btn {
        background: #07c160;
        color: #fff;
      }
      .comment-input-bar .cancel-btn {
        background: #fff;
        color: #333;
        border: 1px solid #ccc;
      }

      /* === 朋友圈背景图库 & URL 弹窗 === */
      .bg-gallery {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: none;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 1002;
      }
      .bg-gallery .bg-list {
        max-height: 70%;
        width: 70%;
        overflow-y: auto;
        background: #fff;
        border-radius: 12px;
        padding: 15px;
        display: flex;
        flex-direction: column;
        gap: 12px;
      }
      .bg-thumb {
        width: 100%;
        height: 120px;
        background-size: cover;
        background-position: center;
        border-radius: 8px;
        cursor: pointer;
      }
      .bg-thumb.plus {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 40px;
        color: #888;
        background: #f1f1f1;
      }
      .bg-thumb.plus:hover {
        background: #e2e2e2;
      }
      .bg-url-modal {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background: #fff;
        padding: 18px;
        border-radius: 10px;
        display: none;
        z-index: 1003;
        width: 260px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
      }
      .bg-url-modal input {
        width: 100%;
        padding: 6px 8px;
        margin-bottom: 12px;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 14px;
      }
      .bg-url-modal .modal-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
      }
      .bg-url-modal button {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
      }
      .bg-url-modal .cancel-btn {
        background: #ddd;
        color: #333;
      }
      .bg-url-modal .confirm-btn {
        background: #07c160;
        color: #fff;
      }

      /* === 个签编辑弹窗 === */
      .sig-modal {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background: #fff;
        padding: 18px;
        border-radius: 10px;
        display: none;
        z-index: 1003;
        width: 260px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
      }
      .sig-modal input {
        width: 100%;
        padding: 6px 8px;
        margin-bottom: 12px;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 14px;
      }
      .sig-modal .modal-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
      }
      .sig-modal button {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
      }
      .sig-modal .cancel-btn {
        background: #ddd;
        color: #333;
      }
      .sig-modal .confirm-btn {
        background: #07c160;
        color: #fff;
      }

      /* 下拉刷新动画 */
      .refresh-loader {
        position: absolute;
        top: -50px; /* Initially hidden above */
        left: 0;
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
        background: #fff;
        z-index: 10;
        transition: top 0.3s ease;
        font-size: 14px;
        color: #555;
        border-bottom: 1px solid #eee;
      }
      .refresh-loader.show {
        top: 0;
      }
      .refresh-loader .spinner {
        width: 18px;
        height: 18px;
        border: 2px solid #576b95;
        border-top-color: transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }

      /* 新增：媒体内容容器 */
      .moment-media {
        max-height: 200px;
        overflow-y: auto;
        background-color: #f0f0f0;
        border-radius: 8px;
        padding: 10px;
        margin: 8px 0;
        font-size: 14px;
        line-height: 1.4;
        white-space: pre-wrap;
        word-break: break-word;
      }
      .moment-media::-webkit-scrollbar {
        width: 4px;
      }
      .moment-media::-webkit-scrollbar-track {
        background: transparent;
      }
      .moment-media::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 2px;
      }
    </style>
  </head>
  <body>
    <div class="phone-container">
      <div class="back-btn" onclick="goBack()">
        <svg viewBox="0 0 24 24">
          <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z" />
        </svg>
      </div>
      <div class="status-bar">
        <div class="status-bar-left">
          <span class="time"></span>
        </div>
        <div class="status-bar-right">
          <span class="signal-text">5G</span>
          <svg class="status-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 4C7.31 4 3.07 5.9 0 8.98L12 21l12-12.02C20.93 5.9 16.69 4 12 4z" />
          </svg>
          <div class="battery-icon">
            <div class="battery-level"></div>
          </div>
        </div>
      </div>
      <div class="content-container">
        <div id="refreshLoader" class="refresh-loader">
          <div class="spinner"></div>
          <span class="loader-text"></span>
        </div>
        <div class="profile-header">
          <div class="profile-info">
            <div class="profile-text">
              <div class="profile-name">{{user}}</div>
            </div>
            <div
              class="user_avatar avatar profile-avatar"
              style="background-size: cover; background-position: center"
              onclick="showPostMomentScreen()"
            ></div>
          </div>
          <div class="profile-signature">别管了，玩一下智能手机。</div>
        </div>
        <div class="moments-list">
          <div class="moment-item">
            <img src="https://files.catbox.moe/$1" class="moment-avatar" />
            <div class="moment-details">
              <div class="moment-username"></div>
              <div class="moment-content"></div>
              <div class="moment-time"></div>
              <div class="moment-comments">
                <div class="comment-item">
                  <span class="comment-username"></span>
                  <span class="comment-text"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加发表朋友圈的界面 -->
      <div class="post-moment-container" id="postMomentScreen">
        <div class="post-moment-header">
          <div class="post-moment-back" onclick="hidePostMomentScreen()">
            <svg viewBox="0 0 24 24">
              <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z" />
            </svg>
          </div>
          <div class="post-moment-title">发表文字</div>
          <button class="post-moment-publish" id="publishBtn" disabled>发表</button>
        </div>
        <div class="post-moment-content">
          <textarea class="post-moment-textarea" id="momentTextarea" placeholder="这一刻的想法..."></textarea>
          <div class="post-moment-options">
            <div class="post-moment-option">
              <span>所在位置</span>
              <div class="post-moment-option-right">
                不显示
                <svg viewBox="0 0 24 24">
                  <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
                </svg>
              </div>
            </div>
            <div class="post-moment-option">
              <span>提醒谁看</span>
              <div class="post-moment-option-right">
                <svg viewBox="0 0 24 24">
                  <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
                </svg>
              </div>
            </div>
            <div class="post-moment-option">
              <span>谁可以看</span>
              <div class="post-moment-option-right">
                公开
                <svg viewBox="0 0 24 24">
                  <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部评论栏 -->
      <div id="commentBar" class="comment-input-bar">
        <input id="commentInput" placeholder="发表评论" />
        <button id="commentCancel" class="cancel-btn">取消</button>
        <button id="commentSend" class="send-btn">发送</button>
      </div>

      <!-- 背景图库 -->
      <div id="bgGallery" class="bg-gallery">
        <div id="bgList" class="bg-list"></div>
      </div>
      <!-- URL 输入弹窗 -->
      <div id="bgUrlModal" class="bg-url-modal">
        <input id="bgUrlInput" placeholder="输入图片 URL" />
        <div class="modal-actions">
          <button id="bgUrlCancel" class="cancel-btn">取消</button>
          <button id="bgUrlConfirm" class="confirm-btn">确定</button>
        </div>
      </div>

      <!-- 个签编辑弹窗 -->
      <div id="sigModal" class="sig-modal">
        <input id="sigInput" placeholder="输入想更改的个签" />
        <div class="modal-actions">
          <button id="sigCancel" class="cancel-btn">取消</button>
          <button id="sigConfirm" class="confirm-btn">确定</button>
        </div>
      </div>
    </div>

    <script>
      // === 刷新计数器，持久化到 localStorage ===
      let refreshCount = parseInt(localStorage.getItem('pyqRefreshCount') || '0', 10);

      /* === 调试日志函数 === */
      function logQS(...args) {
        console.log('[QUANSU]', ...args);
      }
      function logDog(...args) {
        console.log('[DOGGIE]', ...args);
      }

      document.addEventListener('DOMContentLoaded', function () {
        // --- TOQUS: Get user info from SillyTavern ---
        try {
          if (window.user && window.user.name) {
            const profileNameEl = document.querySelector('.profile-name');
            if (profileNameEl) profileNameEl.textContent = window.user.name;
          }
          if (typeof getUserAvatar === 'function') {
            const avatarUrl = getUserAvatar();
            if (avatarUrl) {
              const avatarEls = document.querySelectorAll('.user_avatar.avatar');
              avatarEls.forEach(el => {
                el.style.backgroundImage = `url('${avatarUrl}')`;
              });
            }
          }
        } catch (e) {
          console.warn('Could not set user info from ST context', e);
        }
        // --- END TOQUS ---

        // 更新时间的函数
        function updateTime() {
          const timeElement = document.querySelector('.time');
          if (timeElement) {
            // 检查元素是否存在
            const now = new Date();
            timeElement.textContent = now.toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false,
            });
          }
        }

        // 立即更新一次时间
        updateTime();

        // 每秒更新一次时间
        setInterval(updateTime, 1000);

        // 设置发表按钮的状态
        const textarea = document.getElementById('momentTextarea');
        const publishBtn = document.getElementById('publishBtn');

        if (textarea && publishBtn) {
          textarea.addEventListener('input', function () {
            publishBtn.disabled = textarea.value.trim() === '';
          });

          publishBtn.addEventListener('click', async function () {
            const content = textarea.value.trim();
            if (!content) return;

            const userName =
              window.user && window.user.name && window.user.name !== '{{user}}'
                ? window.user.name
                : document.querySelector('.profile-name')?.textContent.trim() || '你';

            // === 1. 写入楼层：<upyq> ===
            await appendUserMomentToFloor(userName, content);

            // === 2. 本地 UI 立即插入一条（乐观刷新） ===
            insertUserMomentAtTop(content, userName);

            // === 3. 告诉 AI 用户发朋友圈了 ===
            const prompt = buildUpyqPrompt(userName, content);
            sendLineToAI(prompt);

            // === 4. 清理并关闭发布界面 ===
            textarea.value = '';
            hidePostMomentScreen();
          });
        }

        // 修复嵌套朋友圈结构
        fixNestedMoments();

        /* === 背景图库逻辑 === */
        const defaultBgs = [
          'https://files.catbox.moe/k411du.jpeg',
          'https://files.catbox.moe/7vqt1n.jpeg',
          'https://files.catbox.moe/51mj90.jpeg',
          'https://files.catbox.moe/brdpc4.jpeg',
          'https://files.catbox.moe/rjcjzz.jpeg',
          'https://files.catbox.moe/dxawzr.jpeg',
        ];
        const profileHeader = document.querySelector('.profile-header');
        const bgGallery = document.getElementById('bgGallery');
        const bgList = document.getElementById('bgList');
        const bgUrlModal = document.getElementById('bgUrlModal');
        const bgUrlInput = document.getElementById('bgUrlInput');

        function setProfileBackground(url) {
          if (profileHeader) {
            profileHeader.style.backgroundImage = `url('${url}')`;
          }
          localStorage.setItem('pyqBg', url);
        }
        // 恢复已保存的背景
        const savedBg = localStorage.getItem('pyqBg');
        if (savedBg) {
          setProfileBackground(savedBg);
        }

        function buildBgList() {
          if (!bgList) return;
          bgList.innerHTML = '';
          [...defaultBgs].forEach(u => {
            const div = document.createElement('div');
            div.className = 'bg-thumb';
            div.style.backgroundImage = `url('${u}')`;
            div.dataset.url = u;
            bgList.appendChild(div);
          });
          // plus
          const plus = document.createElement('div');
          plus.className = 'bg-thumb plus';
          plus.innerHTML = '+';
          bgList.appendChild(plus);
        }

        function showBgGallery() {
          buildBgList();
          bgGallery.style.display = 'flex';
        }
        function hideBgGallery() {
          bgGallery.style.display = 'none';
        }

        profileHeader.addEventListener('click', e => {
          if (e.target.closest('.profile-avatar') || e.target.closest('.profile-signature')) {
            return; // ignore clicks on avatar or signature
          }
          showBgGallery();
        });
        bgGallery.addEventListener('click', e => {
          if (e.target === bgGallery) hideBgGallery();
        });
        bgList.addEventListener('click', e => {
          const thumb = e.target.closest('.bg-thumb');
          if (!thumb) return;
          if (thumb.classList.contains('plus')) {
            hideBgGallery();
            bgUrlInput.value = '';
            bgUrlModal.style.display = 'block';
            bgUrlInput.focus();
          } else {
            const url = thumb.dataset.url;
            setProfileBackground(url);
            hideBgGallery();
          }
        });
        const bgUrlCancel = document.getElementById('bgUrlCancel');
        const bgUrlConfirm = document.getElementById('bgUrlConfirm');

        bgUrlCancel.addEventListener('click', () => {
          bgUrlModal.style.display = 'none';
        });
        bgUrlConfirm.addEventListener('click', () => {
          const url = bgUrlInput.value.trim();
          if (url) {
            setProfileBackground(url);
          }
          bgUrlModal.style.display = 'none';
        });

        /* === 个签编辑逻辑 === */
        const sigModal = document.getElementById('sigModal');
        const sigInput = document.getElementById('sigInput');
        const sigCancel = document.getElementById('sigCancel');
        const sigConfirm = document.getElementById('sigConfirm');
        const signatureEl = document.querySelector('.profile-signature');

        function setSignature(text) {
          if (signatureEl) signatureEl.textContent = text;
          localStorage.setItem('pyqSignature', text);
        }
        // 恢复签名
        const savedSig = localStorage.getItem('pyqSignature');
        if (savedSig) {
          setSignature(savedSig);
        }

        signatureEl.addEventListener('click', e => {
          e.stopPropagation();
          sigInput.value = signatureEl.textContent || '';
          sigModal.style.display = 'block';
          sigInput.focus();
        });
        sigCancel.addEventListener('click', () => {
          sigModal.style.display = 'none';
        });
        sigConfirm.addEventListener('click', () => {
          const txt = sigInput.value.trim();
          if (txt) {
            setSignature(txt);
          }
          sigModal.style.display = 'none';
        });
      });

      // 显示发表朋友圈界面
      function showPostMomentScreen() {
        const postMomentScreen = document.getElementById('postMomentScreen');
        if (postMomentScreen) {
          postMomentScreen.style.display = 'flex';
          document.getElementById('momentTextarea').focus();
        }
      }

      // 隐藏发表朋友圈界面
      function hidePostMomentScreen() {
        const postMomentScreen = document.getElementById('postMomentScreen');
        if (postMomentScreen) {
          postMomentScreen.style.display = 'none';
          document.getElementById('momentTextarea').value = '';
          document.getElementById('publishBtn').disabled = true;
        }
      }

      // 函数用于修复可能的嵌套朋友圈结构问题
      function fixNestedMoments() {
        // 延迟执行以确保DOM已完全加载
        setTimeout(() => {
          console.log('开始修复嵌套朋友圈结构...');

          // 查找所有评论区
          const commentSections = document.querySelectorAll('.moment-comments');

          commentSections.forEach(commentSection => {
            // 检查评论区内是否有嵌套的朋友圈项
            const nestedMoments = commentSection.querySelectorAll('.moment-item');

            if (nestedMoments.length > 0) {
              console.log('发现嵌套的朋友圈项，正在修复...');

              // 获取朋友圈列表容器
              const momentsList = document.querySelector('.moments-list');

              // 将嵌套的朋友圈移动到正确位置
              nestedMoments.forEach(nestedMoment => {
                // 从错误的位置移除
                nestedMoment.parentNode.removeChild(nestedMoment);

                // 添加到朋友圈列表中
                momentsList.appendChild(nestedMoment);

                console.log('已修复一个嵌套朋友圈项');
              });
            }
          });

          console.log('朋友圈结构修复完成');
        }, 500); // 延迟500毫秒执行
      }

      function insertUserMomentAtTop(content, userName) {
        const avatarUrl = getUserAvatarUrl();
        // 创建新的朋友圈元素，使用替换后的内容直接插入DOM
        const html = `
<div class="moment-item">
<div class="user_avatar avatar" style="width: 40px; height: 40px; border-radius: 4px; background-image:url('${avatarUrl}'); background-size: cover; background-position: center;"></div>
<div class="moment-details">
<div class="moment-username">${userName}</div>
<div class="moment-content">${content}</div>
<div class="moment-time">刚刚</div>
<div class="moment-comments">
<!-- 评论区内容 -->
</div>
</div>
</div>
<!-- ==== USER_MOMENT_END ==== -->
<hr style="border:none; height:10px; background-color:transparent; margin:0; padding:0;">`;

        // 创建临时元素
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html.trim();

        // 获取朋友圈列表并插入新朋友圈
        const momentsList = document.querySelector('.moments-list');
        if (momentsList) {
          // 插入到朋友圈列表最前面
          momentsList.insertAdjacentHTML('afterbegin', html);

          // 执行修复程序以防万一
          fixNestedMoments();
        }
      }

      // === 同层写回逻辑：解析并渲染 <朋友圈>/<pyq> 标签 ===

      /**
       * 解析包含 <pyq>/<upyq> 块的字符串，返回 [{nickname, avatarUrl, text, likes, comments}]
       */
      function parseMomentsFromTaggedContent(taggedString) {
        // 如有外层 <朋友圈> 标签，先取内部内容
        const wrapperMatch = taggedString.match(/<朋友圈>([\s\S]*?)<\/朋友圈>/);
        if (wrapperMatch) {
          logDog('已检测到标签：1');
          taggedString = wrapperMatch[1];
          logDog('已剥离标签', taggedString);
        }

        const moments = [];
        const momentRegex = /<(pyq|upyq)>([\s\S]*?)<\/\1>/g;
        let match;
        while ((match = momentRegex.exec(taggedString)) !== null) {
          const tagType = match[1];
          const block = match[2];

          const nameMatch = block.match(/<昵称>([\s\S]*?)<\/昵称>/);
          let nickname = nameMatch ? nameMatch[1].trim() : '';

          let avatarUrl = '';
          var bhIndex = '';

          if (tagType === 'pyq') {
            // 聊天侧朋友圈，使用头像后缀
            const avatarMatch = block.match(/<头像>([\s\S]*?)<\/头像>/);
            const avatarSuffix = avatarMatch ? avatarMatch[1].trim() : '';
            avatarUrl = `https://files.catbox.moe/${avatarSuffix}`;
            const bhMatch = block.match(/<bh>([\s\S]*?)<\/bh>/);
            bhIndex = bhMatch ? bhMatch[1].trim() : '';
          } else {
            // 用户自身朋友圈，使用个人头像
            avatarUrl = getUserAvatarUrl();
            if (!nickname) {
              nickname = document.querySelector('.profile-name')?.textContent.trim() || '你';
            }
            bhIndex = '';
          }

          // 剩余部分即为正文部分，可能包含 内容/点赞/评论 信息
          const rawContent = block
            .replace(/<bh>[\s\S]*?<\/bh>/, '')
            .replace(/<昵称>[\s\S]*?<\/昵称>/, '')
            .replace(/<头像>[\s\S]*?<\/头像>/, '')
            .trim();

          // 解析 meta
          const { text, media, likes, comments } = parseContentDetails(rawContent);

          const timeMatch = block.match(/<time>(\d+)<\/time>/);
          const time = timeMatch ? parseInt(timeMatch[1], 10) : 0;

          // 如果没有昵称且 text 为空，但包含点赞/评论，视为 meta block → 合并到上一条
          if (!nickname && !text && moments.length) {
            const last = moments[moments.length - 1];
            // 覆盖点赞 (AI 侧已经合并)
            if (likes && likes.length) {
              last.likes = likes;
            }
            // 追加评论
            if (comments && comments.length) {
              last.comments = last.comments.concat(comments);
            }
            continue; // 不新增 moment
          }

          moments.push({ nickname, bhIndex, avatarUrl, text, media, likes, comments, time });
        }
        return moments;
      }

      /**
       * 解析朋友圈正文，提取文字、点赞、评论
       */
      function parseContentDetails(raw) {
        // 移除每行开头多余空白，兼容含缩进的楼层文本
        raw = raw.replace(/^[\t ]+/gm, '').trim();

        let text = '';
        let likes = [];
        const comments = [];
        let media = null; // 新增

        // 查找各段开头位置
        const likeIdx = raw.indexOf('点赞--');
        const commentIdx = raw.indexOf('评论--');
        let metaStart = raw.length;
        if (likeIdx !== -1) metaStart = Math.min(metaStart, likeIdx);
        if (commentIdx !== -1) metaStart = Math.min(metaStart, commentIdx);

        // 提取正文
        const contentMatch = raw.match(/内容--([\s\S]*?)(?:点赞--|评论--|$)/);
        if (contentMatch) {
          text = contentMatch[1].trim();
        } else {
          text = raw.substring(0, metaStart).trim();
        }

        // 从正文中解析媒体
        const mediaRegex = /\[图或视频：([\s\S]+?)\]/g;
        text = text
          .replace(mediaRegex, (match, mediaContent) => {
            if (media === null) {
              // 只取第一个
              media = mediaContent.trim();
            }
            return ''; // 从文本中移除
          })
          .trim();

        // 提取点赞
        const likeMatch = raw.match(/点赞--([\s\S]*?)(?:评论--|$)/);
        if (likeMatch) {
          likes = likeMatch[1]
            .split(/[，,、]/)
            .map(n => n.trim())
            .filter(Boolean);
        }

        // 提取评论，支持多条
        const commentRegex = /评论--([^\s-]+?)(?:回复([^\s-]+?))?--([\s\S]*?)(?=评论--|$)/g;
        let cm;
        while ((cm = commentRegex.exec(raw)) !== null) {
          const author = cm[1].trim();
          const replyTo = cm[2] ? cm[2].trim() : null;
          const content = cm[3].trim();
          comments.push({ author, replyTo, content });
        }

        return { text, media, likes, comments };
      }

      /**
       * 将解析后的朋友圈渲染到界面
       */
      function renderMoments(momentsArray) {
        const list = document.querySelector('.moments-list');
        if (!list) return;

        // 清空旧内容
        list.innerHTML = '';

        momentsArray.forEach(m => {
          let likesHtml = '';
          if (m.likes && m.likes.length) {
            likesHtml = `<div class="moment-likes"><svg style='width:14px;height:14px;vertical-align:-2px;fill:#576B95;' viewBox='0 0 24 24'><path d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 6.42 3.42 5 5.5 5c1.54 0 3.04.99 3.57 2.36h1.87C13.46 5.99 14.96 5 16.5 5 18.58 5 20 6.42 20 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/></svg> ${m.likes.join(
              '、',
            )}</div>`;
          }

          let commentsHtml = '';
          if (m.comments && m.comments.length) {
            m.comments.forEach(c => {
              const commentLine = c.replyTo
                ? `<span class="comment-username">${c.author}</span> 回复 <span class="comment-username">${c.replyTo}</span>: <span class="comment-text">${c.content}</span>`
                : `<span class="comment-username">${c.author}</span>: <span class="comment-text">${c.content}</span>`;
              commentsHtml += `<div class="comment-item">${commentLine}</div>`;
            });
          }

          let mediaHtml = '';
          if (m.media) {
            mediaHtml = `<div class="moment-media">${DOMPurify.sanitize(m.media)}</div>`;
          }

          const minutesAgo = refreshCount - m.time;
          const timeText = minutesAgo <= 0 ? '刚刚' : `${minutesAgo}分钟前`;

          const html = `\n<div class="moment-item" data-nickname="${m.nickname}" data-bh="${m.bhIndex}">\n    <div class="user_avatar avatar" style="width: 40px; height: 40px; background-image: url('${m.avatarUrl}'); background-size: cover; background-position: center;"></div>\n    <div class="moment-details">\n        <div class="moment-username">${m.nickname}</div>\n        <div class="moment-content">${m.text}</div>\n        ${mediaHtml}\n        <div class="moment-meta">\n            <div class="moment-time">${timeText}</div>\n            <div class="moment-actions">\n                <div class="action-btn"></div>\n                <div class="like-comment-popup">\n                    <button class='like-btn'>点赞</button><button class='comment-btn'>评论</button>\n                </div>\n            </div>\n        </div>\n        ${likesHtml}\n        <div class="moment-comments">${commentsHtml}</div>\n    </div>\n</div>`;
          list.insertAdjacentHTML('beforeend', html);
        });

        // 防止嵌套问题
        fixNestedMoments();
      }

      /**
       * 对外暴露的入口函数：传入包含 <朋友圈> 或 <pyq> 标签的字符串
       */
      window.loadMomentsFromTag = function (taggedString) {
        const moments = parseMomentsFromTaggedContent(taggedString);
        if (moments.length) {
          renderMoments(moments);
        }
      };

      /** 获取用户头像 URL */
      function getUserAvatarUrl() {
        const avatarEl = document.querySelector('.profile-avatar');
        if (!avatarEl) return '';
        const bg = window.getComputedStyle(avatarEl).getPropertyValue('background-image');
        const match = bg.match(/url\(["']?(.*?)["']?\)/);
        return match ? match[1] : '';
      }

      /* === 处理 AI 返回的 <pyq>/<upyq> 并写回楼层 === */
      async function processAIPyqResponse(raw) {
        logDog('PYQ小狗开始巡逻中');
        logDog('已巡回', raw);
        if (!raw) return;

        // 先尝试解压 SillyTavern 的压缩格式 ["base64"]
        try {
          if (/^\s*\["[A-Za-z0-9+/]+=*"\]\s*$/.test(raw)) {
            const [b64] = JSON.parse(raw);
            const bin = Uint8Array.from(atob(b64), c => c.charCodeAt(0));
            raw = pako.inflate(bin, { to: 'string' });
          }
        } catch (e) {
          console.warn('[朋友圈] 解压 AI 回复失败', e);
        }

        // 新增功能：捕获并转发 <手机界面> 标签（仅第一个）
        // 已移除 <手机界面> 标签的特殊处理，不再转发或剥离。

        // 工具：清理 pyq/upyq 块内部杂项
        function sanitizeMomentBlock(block) {
          const tagTypeMatch = block.match(/^<(pyq|upyq)>/);
          if (!tagTypeMatch) return '';
          const tag = tagTypeMatch[1];
          const inner = block.replace(/^<(pyq|upyq)>/, '').replace(/<\/(pyq|upyq)>$/, '');
          const lines = inner.split(/\n/).map(l => l.trim());
          const kept = [];
          lines.forEach(line => {
            if (!line) return;
            if (tag === 'pyq') {
              if (/^(<bh>|<昵称>|<头像>)/.test(line) || /^(内容--|点赞--|评论--)/.test(line)) kept.push(line);
            } else {
              // upyq
              if (/^(<昵称>)/.test(line) || /^(内容--|点赞--|评论--)/.test(line)) kept.push(line);
            }
          });
          if (!kept.length) return '';
          return `<${tag}>\n${kept.join('\n')}\n</${tag}>`;
        }

        // === 新逻辑：仅剥离 <朋友圈> 标签，保留内部内容 ===
        raw = raw.replace(/<朋友圈>/g, '').replace(/<\/朋友圈>/g, '');

        const blockRe = /<(pyq|upyq)>[\s\S]*?<\/\1>/g;
        const blocks = raw.match(blockRe);
        if (!blocks || !blocks.length) return;
        for (const block of blocks) {
          const cleanBlock = sanitizeMomentBlock(block);
          const tagTypeMatch = cleanBlock.match(/^<(pyq|upyq)>/);
          const tagType = tagTypeMatch ? tagTypeMatch[1] : '';
          const nickMatch = cleanBlock.match(/<昵称>([\s\S]*?)<\/昵称>/);
          const nick = nickMatch ? nickMatch[1].trim() : '';

          if (tagType === 'pyq') {
            const bhMatch = cleanBlock.match(/<bh>([\s\S]*?)<\/bh>/);
            const bh = bhMatch ? bhMatch[1].trim() : '';
            logDog(`已检测到标签：<pyq> ${nick}#${bh}`);
            if (nick && /^\d+$/.test(bh)) {
              await upsertMomentInFloor(nick, bh, cleanBlock);
              logDog('已插入/更新 <pyq>');
            } else {
              console.warn('[朋友圈] 忽略格式异常的 <pyq>：nick=', nick, ' bh=', bh);
            }
          } else if (tagType === 'upyq') {
            // 若缺少 <昵称>，默认合并到最新的用户朋友圈
            const effectiveNick = nick || '';
            logDog('已检测到标签：<upyq> (meta 更新)', cleanBlock);
            const contentMatch = cleanBlock.match(/内容--([\s\S]*?)(?:\n|$)/);
            const contentText = contentMatch ? contentMatch[1].trim() : '';
            await updateUserUpyq(effectiveNick, contentText, cleanBlock);
            logDog('已剥离标签并写回楼层 (meta)');
          }
        }
        setTimeout(refreshMomentsFromFloor, 200);
      }

      /* === 直接向 AI 发送文本（无暂存） === */
      function sendLineToAI(line) {
        logQS('sendLineToAI prompt', line);
        (async () => {
          try {
            if (typeof generate === 'function') {
              const response = await generate({ user_input: line, should_stream: false });
              logQS('AI generate response', response);
              // 有些版本 generate 会直接返回完整回复
              if (response) {
                await processAIPyqResponse(response);
              }
            } else if (typeof triggerSlash === 'function') {
              // /send 会开新楼层，这里仍然使用原逻辑
              await triggerSlash(`/send ${line.replace(/\n/g, ' ')} |/trigger`);
            } else {
              console.log('[QUANSU] AI prompt (fallback):', line);
            }
            // 兜底：稍后再刷新一次，防止漏掉
            setTimeout(refreshMomentsFromFloor, 600);
          } catch (e) {
            console.error('sendLineToAI failed', e);
          }
        })();
      }

      /* === 构造朋友圈评论 Prompt  === */
      function buildPyqPrompt(nick, bh, contentLine) {
        const header = `{{user}}评论了${nick}第${bh}条朋友圈：\n1. ${contentLine}`;
        const noRepeat = '不要重复用户消息，不要输出任何以 {{user}} 开头的行，只输出你（AI）的新消息。';
        const instr = `请将回复放入如下格式并仅返回标签内容：\n<pyq><bh>${bh}</bh><昵称>${nick}</昵称><头像>头像链接</头像>...你的回复...</pyq>`;
        const prompt = `${header}\n\n${noRepeat}\n\n${instr}`;
        console.log('已将打包消息写入图层并发送给AI:\n', prompt);
        return prompt;
      }

      /* === 构造用户发朋友圈 Prompt (<upyq>) === */
      function buildUpyqPrompt(userName, contentText) {
        const header = `{{user}}发表了朋友圈：${contentText}`;
        const noRepeat = '不要重复用户消息，不要输出任何以 {{user}} 开头的行，只输出你（AI）的新消息。';
        const instr = `请将回复放入如下格式并仅返回标签内容：\n<upyq><昵称>${userName}</昵称>内容--${contentText}……你的回复……</upyq>`;
        const prompt = `${header}\n\n${noRepeat}\n\n${instr}`;
        console.log('已将打包用户朋友圈写入图层并发送给AI:\n', prompt);
        return prompt;
      }

      /* === 向楼层追加一条用户朋友圈 <upyq> === */
      async function appendUserMomentToFloor(userName, contentText) {
        if (
          typeof getCurrentMessageId !== 'function' ||
          typeof getChatMessages !== 'function' ||
          typeof setChatMessage !== 'function'
        ) {
          console.warn('appendUserMomentToFloor: SillyTavern API 不可用');
          return;
        }
        try {
          const fid = getCurrentMessageId();
          if (fid === null) {
            console.warn('appendUserMomentToFloor: 未选中楼层');
            return;
          }
          const arr = await getChatMessages(fid);
          if (!arr || !arr.length) {
            console.warn('appendUserMomentToFloor: 读楼层失败');
            return;
          }
          let floorText = arr[0].message || '';

          // 生成块
          const upyqBlock = `<upyq>\n<昵称>${userName}</昵称>\n<time>${refreshCount}</time>\n内容--${contentText}\n</upyq>`;

          // 若已有 <朋友圈> 包裹，则插入其内部顶部；否则创建全新包装
          const wrapperRe = /<朋友圈>[\s\S]*?<\/朋友圈>/;
          const match = floorText.match(wrapperRe);
          if (match) {
            // 在开头插入，先取得内部
            const original = match[0];
            const inner = original
              .replace(/<朋友圈>/, '')
              .replace(/<\/朋友圈>/, '')
              .trim();
            const newInner = `${upyqBlock}\n${inner}`.trim();
            const rebuilt = `<朋友圈>\n${newInner}\n</朋友圈>`;
            floorText = floorText.replace(wrapperRe, rebuilt);
          } else {
            if (floorText && !/\n$/.test(floorText)) floorText += '\n';
            floorText += `<朋友圈>\n${upyqBlock}\n</朋友圈>\n`;
          }

          await setChatMessage({ message: floorText }, fid, { refresh: 'none' });
          console.log('[朋友圈] 已写入用户朋友圈到楼层');
        } catch (e) {
          console.error('appendUserMomentToFloor 错误', e);
        }
      }

      // === 事件委托：点赞/评论菜单 ===
      document.querySelector('.moments-list').addEventListener('click', function (e) {
        // --- 新增：点击已存在评论，触发"回复某人" ---
        const commentDiv = e.target.closest('.comment-item');
        if (commentDiv) {
          const momentEl = commentDiv.closest('.moment-item');
          if (momentEl) {
            const userNick =
              window.user && window.user.name && window.user.name !== '{{user}}'
                ? window.user.name
                : document.querySelector('.profile-name')?.textContent.trim() || '你';
            // 仅当该朋友圈属于用户本人时才允许快速回复
            if (momentEl.dataset.nickname === userNick) {
              const authorEl = commentDiv.querySelector('.comment-username');
              if (authorEl) {
                const targetNick = authorEl.textContent.trim();
                // 准备评论栏
                currentCommentTarget = momentEl;
                currentReplyNickname = targetNick;
                const bar = document.getElementById('commentBar');
                const input = document.getElementById('commentInput');
                if (bar && input) {
                  bar.style.display = 'flex';
                  input.value = '';
                  input.placeholder = `回复 ${targetNick}`;
                  input.focus();
                }
                // 若点击评论，则无需继续处理其他点击逻辑
                return;
              }
            }
          }
        }
        const actionBtn = e.target.closest('.action-btn');
        const likeBtn = e.target.closest('.like-btn');
        const commentBtn = e.target.closest('.comment-btn');

        if (actionBtn) {
          const popup = actionBtn.nextElementSibling;
          const currentlyOpen = document.querySelector('.like-comment-popup.show');
          if (currentlyOpen && currentlyOpen !== popup) {
            currentlyOpen.classList.remove('show');
          }
          popup.classList.toggle('show');
        } else if (likeBtn) {
          const momentEl = e.target.closest('.moment-item');
          if (!momentEl) return;

          const popup = likeBtn.closest('.like-comment-popup');
          if (popup) popup.classList.remove('show');

          const nick = momentEl.dataset.nickname;
          const bh = momentEl.dataset.bh;
          // Get user name robustly, avoid showing "{{user}}"
          const user =
            window.user && window.user.name && window.user.name !== '{{user}}'
              ? window.user.name
              : document.querySelector('.profile-name')?.textContent.trim() || '你';

          // 1. Update DOM
          const likesDiv = momentEl.querySelector('.moment-likes');
          const alreadyLiked = likesDiv && likesDiv.textContent.includes(user);

          if (alreadyLiked) return; // Already liked, do nothing.

          if (likesDiv) {
            likesDiv.innerHTML =
              likesDiv.innerHTML.replace(/\s*$/, '') + (likesDiv.textContent.trim() ? '、' : '') + user;
          } else {
            const newLike = document.createElement('div');
            newLike.className = 'moment-likes';
            newLike.innerHTML = `<svg style='width:14px;height:14px;vertical-align:-2px;fill:#576B95;' viewBox='0 0 24 24'><path d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 6.42 3.42 5 5.5 5c1.54 0 3.04.99 3.57 2.36h1.87C13.46 5.99 14.96 5 16.5 5 18.58 5 20 6.42 20 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/></svg> ${user}`;
            const detailsDiv = momentEl.querySelector('.moment-details');
            const commentsDiv = detailsDiv.querySelector('.moment-comments');
            detailsDiv.insertBefore(newLike, commentsDiv);
          }

          // 2. Write to floor
          updateMomentInFloor(nick, bh, block => {
            if (/点赞--/.test(block)) {
              return block.replace(/点赞--([^\n]*)/, (m, p1) => {
                let likers = p1
                  .split(/[，,、]/)
                  .map(s => s.trim())
                  .filter(Boolean);
                if (!likers.includes(user)) likers.push(user);
                return '点赞--' + likers.join('、');
              });
            } else {
              // Add '点赞--' before any '评论--' or at the end.
              return block.replace(/(\n?评论--|\n?$)/, `\n点赞--${user}$1`);
            }
          });
        } else if (commentBtn) {
          const momentEl = e.target.closest('.moment-item');
          const popup = commentBtn.closest('.like-comment-popup');
          if (popup) popup.classList.remove('show');
          currentCommentTarget = momentEl; // 设置全局
          currentReplyNickname = null;
          document.getElementById('commentBar').style.display = 'flex';
          document.getElementById('commentInput').value = '';
          document.getElementById('commentInput').placeholder = '发表评论';
          document.getElementById('commentInput').focus();
        } else if (!e.target.closest('.moment-actions')) {
          // 如果点击的地方不是操作按钮区域，则关闭所有弹窗
          document.querySelectorAll('.like-comment-popup.show').forEach(p => p.classList.remove('show'));
        }
      });

      // === 评论栏逻辑 ===
      let currentCommentTarget = null;
      let currentReplyNickname = null;
      document.getElementById('commentCancel').addEventListener('click', () => {
        document.getElementById('commentBar').style.display = 'none';
        currentCommentTarget = null;
        currentReplyNickname = null;
        const inputEl = document.getElementById('commentInput');
        if (inputEl) {
          inputEl.placeholder = '发表评论';
        }
      });
      document.getElementById('commentSend').addEventListener('click', () => {
        const txt = document.getElementById('commentInput').value.trim();
        if (!txt || !currentCommentTarget) return;

        // 判断是否是回复自己朋友圈中的评论
        if (currentReplyNickname) {
          const userSelf =
            window.user && window.user.name && window.user.name !== '{{user}}'
              ? window.user.name
              : document.querySelector('.profile-name')?.textContent.trim() || '你';

          const targetNick = currentReplyNickname;
          const momentContent = currentCommentTarget.querySelector('.moment-content')?.textContent.trim() || '';

          // 1. Update UI
          const cmts = currentCommentTarget.querySelector('.moment-comments');
          const div = document.createElement('div');
          div.className = 'comment-item';
          div.innerHTML = `<span class='comment-username'>${userSelf}</span> 回复 <span class='comment-username'>${targetNick}</span>: <span class='comment-text'>${txt}</span>`;
          cmts.appendChild(div);

          // 2. 写回楼层 (<upyq>)
          updateUpyqInFloor(userSelf, null, fullBlock => {
            return fullBlock.replace(/<\/upyq>\s*$/, `\n评论--${userSelf}回复${targetNick}--${txt}\n</upyq>`);
          });

          // 3. 发送 Prompt 给 AI
          const prompt = buildUpyqReplyPrompt(userSelf, targetNick, txt, momentContent);
          sendLineToAI(prompt);

          // 4. 清理
          document.getElementById('commentBar').style.display = 'none';
          currentCommentTarget = null;
          currentReplyNickname = null;
          document.getElementById('commentInput').placeholder = '发表评论';
          return;
        }

        const nick = currentCommentTarget.dataset.nickname;
        const bh = currentCommentTarget.dataset.bh;
        const user =
          window.user && window.user.name && window.user.name !== '{{user}}'
            ? window.user.name
            : document.querySelector('.profile-name')?.textContent.trim() || '你';

        // 1. Update UI
        const cmts = currentCommentTarget.querySelector('.moment-comments');
        const div = document.createElement('div');
        div.className = 'comment-item';
        div.innerHTML = `<span class='comment-username'>${user}</span>: <span class='comment-text'>${txt}</span>`;
        cmts.appendChild(div);

        // 2. Write to floor using the new modifier logic
        updateMomentInFloor(nick, bh, fullBlock => {
          // 将新评论插入到关闭标签之前，兼容标签后可能存在的空白符
          return fullBlock.replace(/<\/(pyq|upyq)>\s*$/, (m, tag) => `\n评论--${user}--${txt}\n</${tag}>`);
        });

        // 3. Send prompt to AI
        const prompt = buildPyqPrompt(nick, bh, txt);
        sendLineToAI(prompt);

        // 4. Clean up
        document.getElementById('commentBar').style.display = 'none';
        currentCommentTarget = null;
      });

      // === 修改楼层中指定 <pyq> 的工具函数 (更稳健的版本) ===
      async function updateMomentInFloor(nick, bh, modifier) {
        logQS('updateMomentInFloor called', nick, bh);
        if (
          typeof getCurrentMessageId !== 'function' ||
          typeof getChatMessages !== 'function' ||
          typeof setChatMessage !== 'function'
        )
          return;
        try {
          const fid = getCurrentMessageId();
          if (fid === null) return;
          const arr = await getChatMessages(fid);
          if (!arr || !arr.length) return;
          let floorText = arr[0].message || '';

          // Correct regex literal for finding all moment blocks
          const allMomentsRe = /<(pyq|upyq)>[\s\S]*?<\/\1>/g;
          let originalFullTag = null;

          // Find the specific pyq/upyq block that matches both bh and nick
          // Use a temporary array to work around the statefulness of exec on global regex
          const matches = [...floorText.matchAll(allMomentsRe)];
          for (const match of matches) {
            const fullTag = match[0];
            // Use correct strings for .includes() - no escaping forward slashes
            const hasBh = fullTag.includes(`<bh>${bh}</bh>`);
            const hasNick = fullTag.includes(`<昵称>${nick}</昵称>`);
            if (hasBh && hasNick) {
              originalFullTag = fullTag;
              break; // Found it, stop searching
            }
          }

          if (!originalFullTag) {
            console.error(`[朋友圈] updateMomentInFloor:找不到昵称为'${nick}'且编号为'${bh}'的朋友圈。`);
            return;
          }

          // The modifier function now receives the entire <pyq>...</pyq> block
          const modifiedFullTag = modifier(originalFullTag);

          if (modifiedFullTag === originalFullTag) {
            console.log('[朋友圈] updateMomentInFloor: Modifier made no changes.');
            return; // No changes made
          }

          // Replace the old block with the new one in the full floor text
          const newFloorText = floorText.replace(originalFullTag, modifiedFullTag);

          await setChatMessage({ message: newFloorText }, fid, { refresh: 'none' });
          console.log(`[朋友圈] 楼层已为 ${nick}#${bh} 更新。`);
        } catch (e) {
          console.warn('updateMomentInFloor 错误', e);
        }
      }

      /* === 自动捕获楼层中的 <朋友圈> 标签并渲染 === */
      async function refreshMomentsFromFloor() {
        // 处理刷新加载器状态
        const loader = document.getElementById('refreshLoader');
        if (loader && loader.classList.contains('show')) {
          loader.querySelector('.spinner').style.display = 'none';
          loader.querySelector('.loader-text').textContent = '已刷新';
          setTimeout(() => {
            loader.classList.remove('show');
            refreshLock = false; // 动画结束后释放锁
          }, 1000); // 显示"已刷新"1秒钟
        }

        let raw = '';
        try {
          if (typeof getCurrentMessageId === 'function' && typeof getChatMessages === 'function') {
            const fid = getCurrentMessageId();
            if (fid !== null) {
              const msgs = await getChatMessages(fid);
              if (msgs && msgs.length) raw = msgs[0].message || '';
            }
          }
        } catch (e) {
          console.warn('读取楼层失败', e);
        }

        if (!raw) {
          raw = localStorage.getItem('phoneData') || '';
        }

        let blocks = raw.match(/<朋友圈>[\s\S]*?<\/朋友圈>/g) || [];
        const direct = raw.match(/<(pyq|upyq)>[\s\S]*?<\/\1>/g) || [];
        blocks = blocks.concat(direct);
        if (blocks.length) {
          loadMomentsFromTag(blocks.join('\n'));
        } else {
          const list = document.querySelector('.moments-list');
          if (list) list.innerHTML = '';
        }
      }

      // 首次加载
      refreshMomentsFromFloor();

      // 监听 SillyTavern 楼层更新事件
      document.addEventListener('messageUpdated', refreshMomentsFromFloor);
      document.addEventListener('messageSwiped', refreshMomentsFromFloor);

      /* === 替换/追加 用户 <upyq> 的工具函数 === */
      async function updateUserUpyq(nick, contentText, newBlock) {
        logQS('updateUserUpyq called', nick, contentText);
        if (
          typeof getCurrentMessageId !== 'function' ||
          typeof getChatMessages !== 'function' ||
          typeof setChatMessage !== 'function'
        )
          return;
        try {
          const fid = getCurrentMessageId();
          if (fid === null) return;
          const arr = await getChatMessages(fid);
          if (!arr || !arr.length) return;
          let floorText = arr[0].message || '';
          const upyqRe = /<upyq>[\s\S]*?<\/upyq>/g;
          let found = false;
          const matches = [...floorText.matchAll(upyqRe)];
          for (const m of matches) {
            const blk = m[0];
            let hit = false;
            if (nick) {
              const hasNick = blk.includes(`<昵称>${nick}</昵称>`);
              let hasContent = true;
              if (contentText) {
                hasContent = blk.includes(`内容--${contentText}`);
              }
              hit = hasNick && hasContent;
            } else {
              // 没有昵称信息时，默认匹配第一条 upyq（最新发布）
              hit = true;
            }
            if (hit) {
              // Replace old with new
              floorText = floorText.replace(blk, newBlock);
              found = true;
              break;
            }
          }
          if (!found) {
            if (floorText && !/\n$/.test(floorText)) floorText += '\n';
            floorText += newBlock + '\n';
          }
          await setChatMessage({ message: floorText }, fid, { refresh: 'none' });
        } catch (e) {
          console.warn('updateUserUpyq error', e);
        }
      }

      /* === 修改用户 <upyq> 的工具函数（支持回调修改） === */
      async function updateUpyqInFloor(userNick, contentText, modifier) {
        logQS('updateUpyqInFloor called', userNick, contentText);
        if (
          typeof getCurrentMessageId !== 'function' ||
          typeof getChatMessages !== 'function' ||
          typeof setChatMessage !== 'function'
        )
          return;
        try {
          const fid = getCurrentMessageId();
          if (fid === null) return;
          const arr = await getChatMessages(fid);
          if (!arr || !arr.length) return;
          let floorText = arr[0].message || '';

          const upyqRe = /<upyq>[\s\S]*?<\/upyq>/g;
          let originalBlock = null;
          for (const m of floorText.matchAll(upyqRe)) {
            const blk = m[0];
            const hasNick = blk.includes(`<昵称>${userNick}</昵称>`);
            let hit = false;
            if (hasNick) {
              hit = contentText ? blk.includes(`内容--${contentText}`) : true;
            }
            if (hit) {
              originalBlock = blk;
              break;
            }
          }

          if (!originalBlock) {
            console.warn('[朋友圈] updateUpyqInFloor: 未找到匹配的 <upyq>');
            return;
          }

          let newBlock = modifier(originalBlock);

          // === Merge likes & comments instead of overwriting ===
          function mergeUpyq(oldBlk, updBlk) {
            const getLikeLine = blk => (blk.match(/点赞--.*?(?=\n|$)/) || [])[0] || '';
            const getCommentLines = blk => blk.match(/评论--.*?(?=\n|$)/g) || [];

            let merged = oldBlk;

            // Likes
            const updLike = getLikeLine(updBlk);
            if (updLike) {
              const updNames = updLike
                .replace('点赞--', '')
                .split(/[，,、]/)
                .map(s => s.trim())
                .filter(Boolean);
              const oldLike = getLikeLine(merged);
              let nameSet = new Set(updNames);
              if (oldLike) {
                oldLike
                  .replace('点赞--', '')
                  .split(/[，,、]/)
                  .forEach(n => nameSet.add(n.trim()));
                merged = merged.replace(oldLike, `点赞--${[...nameSet].join('、')}`);
              } else {
                merged = merged.replace(
                  /<\/upyq>\s*$/,
                  `点赞--${[...nameSet].join('、')}
</upyq>`,
                );
              }
            }

            // Comments
            const updComments = getCommentLines(updBlk);
            if (updComments.length) {
              const oldComments = getCommentLines(merged);
              const setOld = new Set(oldComments);
              const toAdd = updComments.filter(c => !setOld.has(c));
              if (toAdd.length) {
                const insertPos = merged.lastIndexOf('</upyq>');
                merged =
                  merged.slice(0, insertPos) + toAdd.map(c => '\n' + c).join('') + '\n' + merged.slice(insertPos);
              }
            }
            return merged;
          }

          const mergedBlock = mergeUpyq(originalBlock, newBlock);
          if (mergedBlock === originalBlock) {
            console.log('[朋友圈] updateUserUpyq: 无变化');
            return;
          }

          floorText = floorText.replace(originalBlock, mergedBlock);
          await setChatMessage({ message: floorText }, fid, { refresh: 'none' });
        } catch (e) {
          console.warn('updateUpyqInFloor error', e);
        }
      }

      /* === 构造用户回复自己朋友圈评论的 Prompt (<upyq>) === */
      function buildUpyqReplyPrompt(userNick, targetNick, replyText, momentContent) {
        const header = `{{user}}回复了${targetNick}的评论：\n1. ${replyText}`;
        const noRepeat = '不要重复用户消息，不要输出任何以 {{user}} 开头的行，只输出你（AI）的新消息。';
        const instr = `请将回复放入如下格式并仅返回标签内容：\n<upyq><昵称>${userNick}</昵称>内容--${momentContent}……你的回复……</upyq>`;
        const prompt = `${header}\n\n${noRepeat}\n\n${instr}`;
        console.log('已将用户评论回复打包并发送至 AI:\n', prompt);
        return prompt;
      }

      /* === 下拉刷新朋友圈 === */
      function buildRefreshPrompt() {
        const header = `{{user}}刷新了朋友圈`;
        const noRepeat = '不要重复用户消息，不要输出任何以 {{user}} 开头的行，只输出你（AI）的新消息。';
        const instr =
          '请将新的朋友圈内容放入如下格式并仅返回标签内容：\n<朋友圈>\n…你的回复（若干 <pyq>）…\n{{之前的内容}}\n</朋友圈>';
        return `${header}\n\n${noRepeat}\n\n${instr}`;
      }

      let refreshLock = false;
      async function triggerMomentsRefresh() {
        if (refreshLock) return;
        refreshLock = true;
        refreshCount++; // 递增计数器
        localStorage.setItem('pyqRefreshCount', String(refreshCount));

        const loader = document.getElementById('refreshLoader');
        if (loader) {
          loader.style.transition = 'top 0.3s ease';
          loader.querySelector('.spinner').style.display = 'block';
          loader.querySelector('.loader-text').textContent = '正在刷新...';
          loader.classList.add('show');
        }

        try {
          const prompt = buildRefreshPrompt();
          sendLineToAI(prompt);
        } finally {
          // 锁的释放交给 refreshMomentsFromFloor
        }
      }

      (function initPullToRefresh() {
        const container = document.querySelector('.content-container');
        const loader = document.getElementById('refreshLoader');
        if (!container || !loader) return;

        let startY = 0,
          pulling = false,
          deltaY = 0;

        container.addEventListener(
          'touchstart',
          e => {
            if (container.scrollTop === 0 && !refreshLock) {
              loader.style.transition = 'none';
              startY = e.touches[0].clientY;
              pulling = true;
            }
          },
          { passive: true },
        );

        container.addEventListener(
          'touchmove',
          e => {
            if (!pulling) return;
            deltaY = e.touches[0].clientY - startY;
            if (deltaY > 0) {
              e.preventDefault();
              // 跟随手指向下移动，但限制最大距离
              loader.style.top = `${-50 + Math.min(deltaY, 100)}px`;
            }
          },
          { passive: false },
        );

        container.addEventListener('touchend', () => {
          if (!pulling) return;
          pulling = false;
          loader.style.transition = 'top 0.3s ease';

          if (deltaY > 80) {
            // 下拉超过80px则触发
            triggerMomentsRefresh();
          } else {
            // 否则弹回
            loader.style.top = '-50px';
          }
          deltaY = 0;
        });

        // 鼠标滚轮上滑也触发 (桌面调试方便)
        container.addEventListener('wheel', e => {
          if (e.deltaY < -80 && container.scrollTop === 0) {
            triggerMomentsRefresh();
          }
        });
      })();

      async function replaceMomentsWrapper(newWrapper) {
        if (
          typeof getCurrentMessageId !== 'function' ||
          typeof getChatMessages !== 'function' ||
          typeof setChatMessage !== 'function'
        )
          return;
        try {
          const fid = getCurrentMessageId();
          if (fid === null) return;
          const arr = await getChatMessages(fid);
          if (!arr || !arr.length) return;
          let floorText = arr[0].message || '';
          const wrapperRe = /<朋友圈>[\s\S]*?<\/朋友圈>/;
          if (wrapperRe.test(floorText)) {
            floorText = floorText.replace(wrapperRe, newWrapper);
          } else {
            if (floorText && !/\n$/.test(floorText)) floorText += '\n';
            floorText += newWrapper + '\n';
          }
          await setChatMessage({ message: floorText }, fid, { refresh: 'none' });
        } catch (e) {
          console.warn('replaceMomentsWrapper error', e);
        }
      }

      /* === 新增：插入或更新 <pyq> 的工具函数 === */
      async function upsertMomentInFloor(nick, bh, newBlock) {
        if (
          typeof getCurrentMessageId !== 'function' ||
          typeof getChatMessages !== 'function' ||
          typeof setChatMessage !== 'function'
        )
          return;
        try {
          const fid = getCurrentMessageId();
          if (fid === null) return;
          const arr = await getChatMessages(fid);
          if (!arr || !arr.length) return;
          let floorText = arr[0].message || '';

          const allMomentsRe = /<(pyq|upyq)>[\s\S]*?<\/\1>/g;
          let found = false;
          for (const m of floorText.matchAll(allMomentsRe)) {
            const blk = m[0];
            if (blk.includes(`<bh>${bh}</bh>`) && blk.includes(`<昵称>${nick}</昵称>`)) {
              floorText = floorText.replace(blk, newBlock);
              found = true;
              break;
            }
          }

          if (!found) {
            const timeInjectedBlock = newBlock.replace(/<(pyq)>/, `$&<time>${refreshCount}</time>`);
            // 确认有没有 <朋友圈> 包裹
            const wrapperRe = /<朋友圈>[\s\S]*?<\/朋友圈>/;
            if (wrapperRe.test(floorText)) {
              floorText = floorText.replace(wrapperRe, m => {
                const inner = m
                  .replace(/^<朋友圈>/, '')
                  .replace(/<\/朋友圈>$/, '')
                  .trim();
                return `<朋友圈>\n${timeInjectedBlock}\n${inner}\n</朋友圈>`;
              });
            } else {
              if (floorText && !/\n$/.test(floorText)) floorText += '\n';
              floorText += `<朋友圈>\n${timeInjectedBlock}\n</朋友圈>\n`;
            }
          }

          await setChatMessage({ message: floorText }, fid, { refresh: 'none' });
        } catch (e) {
          console.warn('upsertMomentInFloor error', e);
        }
      }
    </script>
  </body>
</html>
