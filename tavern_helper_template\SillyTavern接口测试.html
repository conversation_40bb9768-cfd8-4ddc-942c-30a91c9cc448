<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SillyTavern接口测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-section {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f9f9f9;
        }
        
        .test-section.success {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .test-section.warning {
            border-color: #ffc107;
            background: #fff3cd;
        }
        
        .test-section.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .api-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            color: #1976d2;
        }
        
        .file-input {
            margin: 10px 0;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #28a745;
        }
        
        .status-offline {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 SillyTavern接口测试</h1>
        
        <div class="api-info">
            <h3>📋 测试说明</h3>
            <p>这个页面用于测试 <code>third-party-image-processor.js</code> 插件暴露的SillyTavern接口功能。</p>
            <ul>
                <li><strong>文档处理接口</strong>：测试文档上传和AI分析功能</li>
                <li><strong>AI调用接口</strong>：测试直接调用SillyTavern AI的功能</li>
                <li><strong>文件类型检测</strong>：测试支持的文件格式识别</li>
                <li><strong>接口可用性</strong>：检查各个接口是否正常工作</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🔍 接口状态检查</h2>
        <div class="test-section" id="interfaceStatus">
            <button onclick="checkInterfaces()">检查接口状态</button>
            <div class="result-box" id="interfaceResult">点击按钮检查接口状态...</div>
        </div>
    </div>

    <div class="container">
        <h2>🤖 SillyTavern AI接口测试</h2>
        <div class="test-section" id="aiStatus">
            <button onclick="testAIInterface()">测试AI接口</button>
            <button onclick="testDirectAICall()">测试直接AI调用</button>
            <div class="result-box" id="aiResult">点击按钮测试AI接口...</div>
        </div>
    </div>

    <div class="container">
        <h2>📄 文档处理接口测试</h2>
        <div class="test-section" id="documentStatus">
            <input type="file" id="documentFile" class="file-input" accept=".txt,.md,.json,.csv,.html,.xml,.rtf">
            <button onclick="testDocumentProcessing()">测试文档处理</button>
            <button onclick="testFileTypeDetection()">测试文件类型检测</button>
            <div class="result-box" id="documentResult">选择文件并点击按钮测试文档处理...</div>
        </div>
    </div>

    <div class="container">
        <h2>📊 支持的文件类型</h2>
        <div class="test-section" id="fileTypeStatus">
            <button onclick="showSupportedTypes()">显示支持的文件类型</button>
            <div class="result-box" id="fileTypeResult">点击按钮查看支持的文件类型...</div>
        </div>
    </div>

    <div class="container">
        <h2>🧪 综合功能测试</h2>
        <div class="test-section" id="integrationStatus">
            <button onclick="runFullTest()">运行完整测试</button>
            <div class="result-box" id="integrationResult">点击按钮运行完整的功能测试...</div>
        </div>
    </div>

    <script>
        // 检查接口状态
        function checkInterfaces() {
            const result = document.getElementById('interfaceResult');
            const section = document.getElementById('interfaceStatus');
            
            let output = '接口状态检查:\n\n';
            
            // 检查各个接口函数
            const interfaces = [
                '__processFileByPlugin',
                '__processDocumentByPlugin',
                '__uploadImageByPlugin',
                '__getSillyTavernAI',
                '__callSillyTavernAI',
                '__getSupportedFileTypes',
                '__isFileTypeSupported'
            ];
            
            let allAvailable = true;
            
            interfaces.forEach(func => {
                const available = typeof top.window[func] === 'function';
                const status = available ? '✅' : '❌';
                const indicator = available ? '🟢' : '🔴';
                
                output += `${status} ${func} ${indicator}\n`;
                if (!available) allAvailable = false;
            });
            
            // 检查SillyTavern AI可用性
            try {
                const aiInterface = top.window.__getSillyTavernAI ? top.window.__getSillyTavernAI() : null;
                if (aiInterface) {
                    output += `\n🤖 SillyTavern AI状态:\n`;
                    output += `   可用性: ${aiInterface.available ? '✅ 可用' : '❌ 不可用'}\n`;
                    output += `   生成函数: ${aiInterface.generate ? '✅ 已加载' : '❌ 未加载'}\n`;
                }
            } catch (error) {
                output += `\n❌ AI接口检查失败: ${error.message}\n`;
                allAvailable = false;
            }
            
            if (allAvailable) {
                output += '\n🎉 所有接口都可用！';
                section.className = 'test-section success';
            } else {
                output += '\n⚠️ 部分接口不可用，请检查插件是否正确加载';
                section.className = 'test-section warning';
            }
            
            result.textContent = output;
        }
        
        // 测试AI接口
        function testAIInterface() {
            const result = document.getElementById('aiResult');
            const section = document.getElementById('aiStatus');
            
            let output = 'AI接口测试:\n\n';
            
            try {
                if (typeof top.window.__getSillyTavernAI !== 'function') {
                    throw new Error('AI接口函数不存在');
                }
                
                const aiInterface = top.window.__getSillyTavernAI();
                output += `接口获取: ✅ 成功\n`;
                output += `AI可用性: ${aiInterface.available ? '✅ 可用' : '❌ 不可用'}\n`;
                output += `生成函数: ${aiInterface.generate ? '✅ 存在' : '❌ 不存在'}\n`;
                output += `调用函数: ${aiInterface.callAI ? '✅ 存在' : '❌ 不存在'}\n`;
                
                if (aiInterface.available) {
                    output += '\n🎉 AI接口完全可用！';
                    section.className = 'test-section success';
                } else {
                    output += '\n⚠️ AI接口不可用，可能SillyTavern未正确加载';
                    section.className = 'test-section warning';
                }
                
            } catch (error) {
                output += `❌ 测试失败: ${error.message}`;
                section.className = 'test-section error';
            }
            
            result.textContent = output;
        }
        
        // 测试直接AI调用
        async function testDirectAICall() {
            const result = document.getElementById('aiResult');
            const section = document.getElementById('aiStatus');
            
            let output = '直接AI调用测试:\n\n';
            result.textContent = output + '正在测试...';
            
            try {
                if (typeof top.window.__callSillyTavernAI !== 'function') {
                    throw new Error('直接AI调用函数不存在');
                }
                
                const testPrompt = '请简单回复"AI接口测试成功"';
                output += `发送测试提示: "${testPrompt}"\n`;
                output += `调用时间: ${new Date().toLocaleTimeString()}\n\n`;
                
                const aiResponse = await top.window.__callSillyTavernAI(testPrompt, {
                    fileName: 'interface-test.txt'
                });
                
                output += `AI回复: ${aiResponse}\n`;
                output += `\n✅ 直接AI调用测试成功！`;
                section.className = 'test-section success';
                
            } catch (error) {
                output += `❌ 直接AI调用失败: ${error.message}`;
                section.className = 'test-section error';
            }
            
            result.textContent = output;
        }
        
        // 测试文档处理
        async function testDocumentProcessing() {
            const fileInput = document.getElementById('documentFile');
            const result = document.getElementById('documentResult');
            const section = document.getElementById('documentStatus');
            
            if (!fileInput.files.length) {
                result.textContent = '请先选择一个文档文件';
                return;
            }
            
            const file = fileInput.files[0];
            let output = `文档处理测试:\n\n`;
            output += `文件名: ${file.name}\n`;
            output += `文件类型: ${file.type}\n`;
            output += `文件大小: ${(file.size / 1024).toFixed(2)} KB\n\n`;
            
            result.textContent = output + '正在处理...';
            
            try {
                if (typeof top.window.__processDocumentByPlugin !== 'function') {
                    throw new Error('文档处理函数不存在');
                }
                
                const processResult = await top.window.__processDocumentByPlugin(file, {
                    enableAIReading: true,
                    aiPrompt: '请简单总结这个文档的内容'
                });
                
                output += `处理状态: ✅ 成功\n`;
                output += `文档类型: ${processResult.type || '未知'}\n`;
                output += `内容长度: ${processResult.content ? processResult.content.length : 0} 字符\n`;
                
                if (processResult.content) {
                    const preview = processResult.content.length > 200 
                        ? processResult.content.substring(0, 200) + '...' 
                        : processResult.content;
                    output += `\n内容预览:\n${preview}\n`;
                }
                
                if (processResult.aiAnalysis) {
                    output += `\nAI分析结果:\n${processResult.aiAnalysis}\n`;
                }
                
                output += `\n🎉 文档处理测试成功！`;
                section.className = 'test-section success';
                
            } catch (error) {
                output += `❌ 文档处理失败: ${error.message}`;
                section.className = 'test-section error';
            }
            
            result.textContent = output;
        }
        
        // 测试文件类型检测
        function testFileTypeDetection() {
            const result = document.getElementById('documentResult');
            
            let output = '文件类型检测测试:\n\n';
            
            try {
                if (typeof top.window.__isFileTypeSupported !== 'function') {
                    throw new Error('文件类型检测函数不存在');
                }
                
                const testTypes = [
                    'text/plain',
                    'image/jpeg',
                    'application/json',
                    'application/pdf',
                    'video/mp4', // 不支持的类型
                    'audio/mp3'  // 不支持的类型
                ];
                
                testTypes.forEach(type => {
                    const supported = top.window.__isFileTypeSupported(type);
                    const status = supported ? '✅' : '❌';
                    output += `${status} ${type}\n`;
                });
                
                output += '\n✅ 文件类型检测测试完成！';
                
            } catch (error) {
                output += `❌ 文件类型检测失败: ${error.message}`;
            }
            
            result.textContent = output;
        }
        
        // 显示支持的文件类型
        function showSupportedTypes() {
            const result = document.getElementById('fileTypeResult');
            
            let output = '支持的文件类型:\n\n';
            
            try {
                if (typeof top.window.__getSupportedFileTypes !== 'function') {
                    throw new Error('获取文件类型函数不存在');
                }
                
                const supportedTypes = top.window.__getSupportedFileTypes();
                
                output += '📸 图像文件:\n';
                supportedTypes.images.forEach(type => {
                    output += `  • ${type}\n`;
                });
                
                output += '\n📄 文档文件:\n';
                supportedTypes.documents.forEach(type => {
                    output += `  • ${type}\n`;
                });
                
                output += `\n📊 总计: ${supportedTypes.all().length} 种文件类型`;
                
            } catch (error) {
                output += `❌ 获取文件类型失败: ${error.message}`;
            }
            
            result.textContent = output;
        }
        
        // 运行完整测试
        async function runFullTest() {
            const result = document.getElementById('integrationResult');
            const section = document.getElementById('integrationStatus');
            
            let output = '完整功能测试:\n\n';
            result.textContent = output + '正在运行测试...';
            
            let passedTests = 0;
            let totalTests = 0;
            
            // 测试1: 接口可用性
            totalTests++;
            try {
                const interfaces = ['__processFileByPlugin', '__getSillyTavernAI', '__callSillyTavernAI'];
                const allExists = interfaces.every(func => typeof top.window[func] === 'function');
                if (allExists) {
                    output += '✅ 测试1: 接口可用性 - 通过\n';
                    passedTests++;
                } else {
                    output += '❌ 测试1: 接口可用性 - 失败\n';
                }
            } catch (error) {
                output += `❌ 测试1: 接口可用性 - 异常: ${error.message}\n`;
            }
            
            // 测试2: AI接口状态
            totalTests++;
            try {
                const aiInterface = top.window.__getSillyTavernAI();
                if (aiInterface && aiInterface.available) {
                    output += '✅ 测试2: AI接口状态 - 通过\n';
                    passedTests++;
                } else {
                    output += '❌ 测试2: AI接口状态 - AI不可用\n';
                }
            } catch (error) {
                output += `❌ 测试2: AI接口状态 - 异常: ${error.message}\n`;
            }
            
            // 测试3: 文件类型支持
            totalTests++;
            try {
                const supportedTypes = top.window.__getSupportedFileTypes();
                if (supportedTypes && supportedTypes.all().length > 0) {
                    output += `✅ 测试3: 文件类型支持 - 通过 (${supportedTypes.all().length}种)\n`;
                    passedTests++;
                } else {
                    output += '❌ 测试3: 文件类型支持 - 失败\n';
                }
            } catch (error) {
                output += `❌ 测试3: 文件类型支持 - 异常: ${error.message}\n`;
            }
            
            // 测试结果
            output += `\n📊 测试结果: ${passedTests}/${totalTests} 通过\n`;
            
            if (passedTests === totalTests) {
                output += '\n🎉 所有测试通过！插件功能完全正常！';
                section.className = 'test-section success';
            } else if (passedTests > 0) {
                output += '\n⚠️ 部分测试通过，请检查失败的功能';
                section.className = 'test-section warning';
            } else {
                output += '\n❌ 所有测试失败，请检查插件是否正确加载';
                section.className = 'test-section error';
            }
            
            result.textContent = output;
        }
        
        // 页面加载时自动检查接口状态
        window.onload = function() {
            setTimeout(checkInterfaces, 500);
        };
    </script>
</body>
</html>
