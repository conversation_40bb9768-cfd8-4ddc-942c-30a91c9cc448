<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>养成系统</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: #f0f0f0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      }

      .phone-container {
        width: min(100vw, 375px);
        height: 580px; /* 降低高度 */
        border: 8px solid #5d4e75; /* 更可爱的深紫色边框 */
        border-radius: 32px;
        background: linear-gradient(145deg, #6c5b7b, #5d4e75); /* 渐变背景 */
        position: relative;
        overflow: hidden;
        box-shadow: 0 0 30px rgba(93, 78, 117, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.1),
          0 8px 32px rgba(93, 78, 117, 0.3); /* 多层阴影效果 */
      }

      .phone-container::before {
        content: '✨';
        position: absolute;
        top: -15px;
        left: 20px;
        font-size: 20px;
        z-index: 1000;
        animation: sparkle 2s ease-in-out infinite;
      }

      .phone-container::after {
        content: '✨';
        position: absolute;
        top: -15px;
        right: 20px;
        font-size: 16px;
        z-index: 1000;
        animation: sparkle 2s ease-in-out infinite 0.5s;
      }

      @keyframes sparkle {
        0%,
        100% {
          transform: scale(1) rotate(0deg);
          opacity: 0.7;
        }
        50% {
          transform: scale(1.2) rotate(180deg);
          opacity: 1;
        }
      }

      .phone-notch {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 140px;
        height: 12px;
        background: #5d4e75;
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
        z-index: 999;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 6px;
      }

      .notch-camera {
        width: 7px;
        height: 7px;
        background: #2c3e50;
        border-radius: 50%;
        border: 1px solid #34495e;
      }

      .notch-speaker {
        width: 35px;
        height: 3px;
        background: #2c3e50;
        border-radius: 2px;
      }

      .notch-sensor {
        width: 5px;
        height: 5px;
        background: #2c3e50;
        border-radius: 50%;
        border: 1px solid #34495e;
      }

      .game-container {
        width: 100%;
        height: 100%;
        position: relative;
        border-radius: 24px;
        overflow: hidden;
      }

      .background-video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
        transition: opacity 0.6s;
      }

      .background-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
        transition: opacity 0.6s;
        cursor: pointer;
      }

      .ui-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 2;
        display: flex;
        flex-direction: column;
        background: rgba(0, 0, 0, 0.3);
      }

      .top-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 16px 8px;
        background: linear-gradient(to bottom, rgba(93, 78, 117, 0.7), transparent);
      }

      .date-info {
        color: #fff;
        font-size: 14px;
        font-weight: 600;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
        background: rgba(255, 255, 255, 0.1);
        padding: 4px 8px;
        border-radius: 12px;
        backdrop-filter: blur(5px);
      }

      .coin-info {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #ffd700;
        font-size: 14px;
        font-weight: 700;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
        background: rgba(255, 215, 0, 0.1);
        padding: 4px 8px;
        border-radius: 12px;
        backdrop-filter: blur(5px);
      }

      .coin-icon {
        width: 20px;
        height: 20px;
        background: linear-gradient(135deg, #ffd700, #ffa500);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: #8b4513;
        font-size: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .main-area {
        flex: 1;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-start;
        padding: 15px;
        padding-top: 175px; /* 增加上边距，让按钮往下移 */
        position: relative;
      }

      .training-history-panel {
        width: calc(100% - 170px); /* 给右侧按钮留更多空间 */
        padding: 12px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.8));
        border-radius: 16px;
        box-shadow: 0 4px 15px rgba(93, 78, 117, 0.2), inset 0 1px 2px rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        margin-right: 10px;
      }

      .history-title {
        font-size: 16px;
        font-weight: 700;
        color: #5d4e75;
        margin-bottom: 8px;
        text-align: center;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
      }

      .training-history-content {
        max-height: 160px;
        overflow-y: auto;
        padding: 3px;
      }

      .training-history-content::-webkit-scrollbar {
        width: 4px;
      }

      .training-history-content::-webkit-scrollbar-track {
        background: rgba(93, 78, 117, 0.1);
        border-radius: 2px;
      }

      .training-history-content::-webkit-scrollbar-thumb {
        background: rgba(93, 78, 117, 0.3);
        border-radius: 2px;
      }

      .training-history-item {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 249, 250, 0.9));
        border: 1px solid rgba(93, 78, 117, 0.2);
        border-radius: 10px;
        padding: 8px;
        margin-bottom: 6px;
        font-size: 12px;
        line-height: 1.3;
        color: #5d4e75;
        animation: fadeIn 0.3s ease-out;
        box-shadow: 0 2px 8px rgba(93, 78, 117, 0.1);
      }

      .training-history-item:last-child {
        margin-bottom: 0;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(-10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .right-buttons {
        position: static; /* 改为静态定位 */
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        width: 140px;
        flex-shrink: 0; /* 不允许收缩 */
      }

      .game-button {
        width: 65px;
        height: 65px;
        border: none;
        border-radius: 50%; /* 圆形按钮更可爱 */
        color: #fff;
        font-size: 11px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 2px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), inset 0 2px 4px rgba(255, 255, 255, 0.2);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        position: relative;
        overflow: hidden;
      }

      .game-button::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transform: rotate(45deg);
        transition: all 0.5s;
        opacity: 0;
      }

      .game-button:hover::before {
        animation: shine 0.5s ease-in-out;
      }

      @keyframes shine {
        0% {
          transform: translateX(-100%) translateY(-100%) rotate(45deg);
          opacity: 0;
        }
        50% {
          opacity: 1;
        }
        100% {
          transform: translateX(100%) translateY(100%) rotate(45deg);
          opacity: 0;
        }
      }

      .game-button:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), inset 0 2px 4px rgba(255, 255, 255, 0.3);
      }

      .game-button:active {
        transform: translateY(-1px) scale(0.98);
      }

      .shop-btn {
        background: linear-gradient(135deg, #8b5a8c, #a569bd);
        grid-column: span 2; /* 商城按钮占两列 */
        width: 140px;
        border-radius: 25px; /* 椭圆形 */
      }

      .backpack-btn {
        background: linear-gradient(135deg, #48c9b0, #76d7c4);
      }

      .mission-btn {
        background: linear-gradient(135deg, #5dade2, #85c1e9);
      }

      .map-btn {
        background: linear-gradient(135deg, #82e0aa, #a9dfbf);
      }

      .training-btn {
        background: linear-gradient(135deg, #f7dc6f, #f8c471);
        grid-column: span 2; /* 培训按钮占两列 */
        width: 140px;
        border-radius: 25px; /* 椭圆形 */
      }

      .button-icon {
        font-size: 20px;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
      }

      .button-text {
        font-size: 10px;
        font-weight: 700;
        letter-spacing: 0.5px;
      }

      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
      }

      .modal-content {
        background-color: #fff;
        margin: 5% auto;
        padding: 20px;
        border-radius: 12px;
        width: 90%;
        max-width: 400px;
        max-height: 80vh;
        overflow-y: auto;
        position: relative;
      }

      /* 新增地图相关样式 */
      .map-container {
        background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
        border-radius: 12px;
        padding: 20px;
        min-height: 400px;
        display: flex;
        gap: 20px;
      }

      .district-buttons {
        display: flex;
        flex-direction: column;
        gap: 12px;
        width: 120px;
      }

      .district-button {
        background: rgba(255, 255, 255, 0.8);
        border: none;
        padding: 12px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        color: #2e7d32;
        transition: all 0.3s ease;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .district-button:hover {
        background: #fff;
        transform: translateX(5px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      .district-detail {
        flex: 1;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
        padding: 15px;
        display: none;
      }

      .district-detail.active {
        display: block;
        animation: fadeIn 0.3s ease-out;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .district-detail h3 {
        color: #1b5e20;
        margin-bottom: 12px;
        font-size: 18px;
      }

      .district-detail p {
        color: #37474f;
        line-height: 1.6;
        margin-bottom: 8px;
      }

      .visit-button {
        background: linear-gradient(135deg, #4caf50, #2e7d32);
        color: #fff;
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        width: 100%;
        margin-top: 15px;
        transition: all 0.3s ease;
      }

      .visit-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      .close-btn {
        position: absolute;
        right: 15px;
        top: 15px;
        font-size: 24px;
        cursor: pointer;
        color: #666;
      }

      .modal-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #333;
      }

      .modal-body {
        color: #666;
        line-height: 1.6;
      }

      .training-options {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .training-category {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
      }

      .training-category:hover {
        background: #e9ecef;
        border-color: #007bff;
      }

      .training-category h3 {
        color: #333;
        margin-bottom: 8px;
        font-size: 16px;
      }

      .training-category p {
        color: #666;
        font-size: 14px;
      }

      .training-detail {
        display: none;
        margin-top: 15px;
      }

      .training-option {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .training-option:hover {
        background: #f8f9fa;
        border-color: #007bff;
      }

      .training-option h4 {
        color: #333;
        margin-bottom: 4px;
        font-size: 14px;
      }

      .training-option p {
        color: #666;
        font-size: 12px;
        margin-bottom: 4px;
      }

      .training-cost {
        color: #ff6b6b;
        font-weight: 600;
        font-size: 12px;
      }

      .confirm-btn {
        background: linear-gradient(135deg, #4ecdc4, #45b7d1);
        color: #fff;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        width: 100%;
        margin-top: 15px;
        transition: all 0.3s ease;
      }

      .confirm-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      .confirm-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }

      .gold-display {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 15px;
        text-align: center;
        font-weight: 600;
        color: #333;
      }

      .shop-items {
        max-height: 300px;
        overflow-y: auto;
        margin-bottom: 20px;
        padding: 5px;
      }

      .shop-item {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s ease;
      }

      .shop-item:hover {
        background: #f8f9fa;
        border-color: #007bff;
        transform: translateY(-1px);
      }

      .shop-item-info {
        flex: 1;
      }

      .shop-item-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }

      .shop-item-desc {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }

      .shop-item-price {
        font-size: 14px;
        color: #ff6b6b;
        font-weight: 600;
      }

      .add-to-cart-btn {
        background: linear-gradient(135deg, #4ecdc4, #45b7d1);
        color: #fff;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-left: 10px;
      }

      .add-to-cart-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      .add-to-cart-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }

      .cart-section {
        border-top: 1px solid #e9ecef;
        padding-top: 15px;
      }

      .cart-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #333;
      }

      .cart-items {
        max-height: 150px;
        overflow-y: auto;
        margin-bottom: 10px;
      }

      .cart-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 5px;
      }

      .cart-item-info {
        flex: 1;
      }

      .cart-item-name {
        font-weight: 500;
        color: #333;
      }

      .cart-item-quantity {
        font-size: 12px;
        color: #666;
      }

      .cart-item-remove {
        background: #ff6b6b;
        color: #fff;
        border: none;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .cart-item-remove:hover {
        background: #ff5252;
        transform: scale(1.05);
      }

      .cart-total {
        text-align: center;
        font-weight: 600;
        font-size: 16px;
        color: #333;
        margin-bottom: 15px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
      }

      .cart-empty {
        text-align: center;
        color: #666;
        font-style: italic;
        padding: 20px;
      }

      .empty-state {
        text-align: center;
        color: #666;
        font-style: italic;
        margin: 20px 0;
      }

      .item-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .item {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 8px;
        border-left: 4px solid #4ecdc4;
      }

      .mission-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .mission {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 8px;
        border-left: 4px solid #45b7d1;
      }

      @media (max-width: 480px) {
        .phone-container {
          border: 6px solid #5d4e75;
          border-radius: 28px;
          margin: 8px;
          height: 520px;
        }

        .game-button {
          width: 55px;
          height: 55px;
          font-size: 10px;
        }

        .shop-btn,
        .training-btn {
          width: 120px;
        }

        .button-icon {
          font-size: 16px;
        }

        .button-text {
          font-size: 9px;
        }

        .training-history-panel {
          width: calc(100% - 140px);
          padding: 10px;
          margin-left: 8px;
        }

        .history-title {
          font-size: 14px;
        }

        .training-history-content {
          max-height: 120px;
        }

        .training-history-item {
          font-size: 11px;
          padding: 6px;
        }

        .right-buttons {
          width: 120px;
          gap: 10px;
          right: 12px;
        }
      }
    </style>
  </head>
  <body>
    <div class="phone-container">
      <div class="phone-notch">
        <div class="notch-camera"></div>
        <div class="notch-speaker"></div>
        <div class="notch-sensor"></div>
      </div>

      <div class="game-container">
        <video class="background-video" id="bgVideo" autoplay loop muted>
          <source src="https://files.catbox.moe/ap3v93.mov" type="video/mp4" />
        </video>
        <img
          class="background-image"
          id="bgImage"
          src="https://files.catbox.moe/06g8ro.png"
          alt="背景"
          onclick="nextBgImage()"
          style="display: none"
        />

        <div class="ui-overlay">
          <div class="top-bar">
            <div class="date-info" id="currentDate">--</div>
            <div class="coin-info">
              <div class="coin-icon">¥</div>
              <span id="currentCoins">--</span>
            </div>
          </div>

          <div class="main-area">
            <div class="training-history-panel">
              <h3 class="history-title">📚 培训记录</h3>
              <div id="trainingHistoryDisplay" class="training-history-content">
                <div class="empty-state">暂无培训记录</div>
              </div>
            </div>

            <div class="right-buttons">
              <button class="game-button shop-btn" onclick="handleShopClick()">
                <div class="button-icon">🏪</div>
                <div class="button-text">商城</div>
              </button>
              <button class="game-button backpack-btn" onclick="handleBackpackClick()">
                <div class="button-icon">🎒</div>
                <div class="button-text">背包</div>
              </button>
              <button class="game-button mission-btn" onclick="handleMissionClick()">
                <div class="button-icon">📋</div>
                <div class="button-text">任务</div>
              </button>
              <button class="game-button map-btn" onclick="handleMapClick()">
                <div class="button-icon">🗺️</div>
                <div class="button-text">大地图</div>
              </button>
              <button class="game-button training-btn" onclick="handleTrainingClick()">
                <div class="button-icon">🎯</div>
                <div class="button-text">培训</div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 背包模态框 -->
    <div id="backpackModal" class="modal">
      <div class="modal-content">
        <span class="close-btn" onclick="closeModal('backpackModal')">&times;</span>
        <h2 class="modal-title">背包</h2>
        <div class="modal-body">
          <div id="backpackContent" class="item-list">
            <div class="empty-state">背包为空</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务模态框 -->
    <div id="missionModal" class="modal">
      <div class="modal-content">
        <span class="close-btn" onclick="closeModal('missionModal')">&times;</span>
        <h2 class="modal-title">任务</h2>
        <div class="modal-body">
          <div id="missionContent" class="mission-list">
            <div class="empty-state">暂无任务</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 商城模态框 -->
    <div id="shopModal" class="modal">
      <div class="modal-content" style="max-width: 500px; max-height: 85vh">
        <span class="close-btn" onclick="closeModal('shopModal')">&times;</span>
        <h2 class="modal-title">🏪 商城</h2>
        <div class="modal-body">
          <div class="gold-display">
            <span>当前信用点：</span>
            <span id="shopCurrentCoins">--</span>
          </div>
          <div class="shop-items" id="shopItems"></div>
          <div class="cart-section">
            <h3 class="cart-title">🛒 购物车</h3>
            <div id="cartItems" class="cart-items"></div>
            <div class="cart-total">
              <span>总计：</span>
              <span id="cartTotal">0</span>
              <span>信用点</span>
            </div>
            <button id="checkoutBtn" class="confirm-btn" onclick="checkoutCart()">确定购买</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 培训模态框 -->
    <div id="trainingModal" class="modal">
      <div class="modal-content">
        <span class="close-btn" onclick="closeModal('trainingModal')">&times;</span>
        <h2 class="modal-title">培训</h2>
        <div class="modal-body">
          <div class="training-options">
            <div class="training-category" onclick="showTrainingDetail('performance')">
              <h3>🎭 排练室</h3>
              <p>提升表演技能和舞台表现力</p>
            </div>
            <div class="training-category" onclick="showTrainingDetail('singing')">
              <h3>🎤 练歌房</h3>
              <p>训练歌唱技巧和音乐素养</p>
            </div>
            <div class="training-category" onclick="showTrainingDetail('dancing')">
              <h3>💃 练舞室</h3>
              <p>学习舞蹈技巧和肢体协调</p>
            </div>
            <div class="training-category" onclick="showTrainingDetail('creation')">
              <h3>✨ 创作间</h3>
              <p>培养创作能力和艺术灵感</p>
            </div>
          </div>

          <div id="trainingDetail" class="training-detail">
            <div id="trainingOptions"></div>
            <button id="confirmTraining" class="confirm-btn" onclick="confirmTraining()">开始培训</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 大地图模态框 -->
    <div id="mapModal" class="modal">
      <div class="modal-content" style="max-width: 500px; max-height: 85vh">
        <span class="close-btn" onclick="closeModal('mapModal')">&times;</span>
        <h2 class="modal-title">🗺️ 大地图</h2>
        <div class="modal-body">
          <div id="districtList" class="item-list"></div>
          <div id="districtDetail" style="display: none; margin-top: 15px"></div>
        </div>
      </div>
    </div>

    <script>
      // 游戏状态
      let gameState = {
        currentMsgId: null,
        backpackItems: [],
        missions: [],
        trainingHistory: [],
      };

      let selectedTraining = null;
      let shoppingCart = [];

      // 商品数据
      const shopItems = [
        // 服装类
        { id: 'clothes1', name: '白色T恤', desc: '简约舒适的基本款', price: 50, category: '服装' },
        { id: 'clothes2', name: '牛仔裤', desc: '经典百搭款式', price: 120, category: '服装' },
        { id: 'clothes3', name: '运动鞋', desc: '透气轻便的运动鞋', price: 200, category: '服装' },
        { id: 'clothes4', name: '连衣裙', desc: '优雅的日常连衣裙', price: 180, category: '服装' },
        { id: 'clothes5', name: '毛衣', desc: '温暖的羊毛毛衣', price: 150, category: '服装' },

        // 食物类
        { id: 'food1', name: '苹果', desc: '新鲜的红富士苹果', price: 10, category: '食物' },
        { id: 'food2', name: '面包', desc: '香甜的吐司面包', price: 15, category: '食物' },
        { id: 'food3', name: '牛奶', desc: '营养丰富的纯牛奶', price: 20, category: '食物' },
        { id: 'food4', name: '方便面', desc: '快速解决温饱的方便食品', price: 5, category: '食物' },
        { id: 'food5', name: '巧克力', desc: '浓郁香甜的黑巧克力', price: 25, category: '食物' },
        { id: 'food6', name: '饼干', desc: '酥脆可口的消化饼干', price: 12, category: '食物' },

        // 家电类
        { id: 'appliance1', name: '电水壶', desc: '快速烧水的家用电器', price: 80, category: '家电' },
        { id: 'appliance2', name: '台灯', desc: '护眼LED台灯', price: 60, category: '家电' },
        { id: 'appliance3', name: '电风扇', desc: '夏日必备的降温神器', price: 150, category: '家电' },
        { id: 'appliance4', name: '电饭煲', desc: '自动保温的电饭煲', price: 300, category: '家电' },
        { id: 'appliance5', name: '吸尘器', desc: '强力清洁的手持吸尘器', price: 250, category: '家电' },

        // 日用品类
        { id: 'daily1', name: '牙刷', desc: '软毛护龈牙刷', price: 8, category: '日用品' },
        { id: 'daily2', name: '毛巾', desc: '吸水性强的纯棉毛巾', price: 30, category: '日用品' },
        { id: 'daily3', name: '洗发水', desc: '温和滋润的洗发露', price: 45, category: '日用品' },
        { id: 'daily4', name: '沐浴露', desc: '清香怡人的沐浴用品', price: 35, category: '日用品' },
        { id: 'daily5', name: '纸巾', desc: '柔软的面纸抽取式', price: 12, category: '日用品' },
        { id: 'daily6', name: '洗衣液', desc: '强效去污的洗衣用品', price: 25, category: '日用品' },
        // 新增生活用品
        { id: 'daily7', name: '洗手液', desc: '温和杀菌的泡沫洗手液', price: 18, category: '日用品' },
        { id: 'daily8', name: '垃圾袋', desc: '结实耐用的垃圾袋', price: 10, category: '日用品' },
        { id: 'daily9', name: '收纳盒', desc: '桌面收纳好帮手', price: 22, category: '日用品' },
        { id: 'daily10', name: '闹钟', desc: '可爱的卡通闹钟', price: 35, category: '日用品' },
        { id: 'daily11', name: '水杯', desc: '带吸管的塑料水杯', price: 15, category: '日用品' },
        { id: 'daily12', name: '拖鞋', desc: '柔软舒适的家居拖鞋', price: 28, category: '日用品' },

        // 其他杂物
        { id: 'misc1', name: '笔记本', desc: '记录生活点滴的日记本', price: 20, category: '文具' },
        { id: 'misc2', name: '圆珠笔', desc: '书写流畅的签字笔', price: 3, category: '文具' },
        { id: 'misc3', name: '雨伞', desc: '晴雨两用的折叠伞', price: 40, category: '生活用品' },
        { id: 'misc4', name: '背包', desc: '多功能的双肩背包', price: 90, category: '生活用品' },
        { id: 'misc5', name: '手机壳', desc: '防摔透明手机保护壳', price: 25, category: '配件' },
        { id: 'misc6', name: '充电宝', desc: '大容量移动电源', price: 80, category: '配件' },
        // 新增摆设/装饰品
        { id: 'deco1', name: '小夜灯', desc: '萌萌的兔子夜灯，只为好心情', price: 40, category: '摆设' },
        { id: 'deco2', name: '桌面绿植', desc: '假绿植，永不枯萎', price: 25, category: '摆设' },
        { id: 'deco3', name: '照片相框', desc: '可以放喜欢照片的相框', price: 18, category: '摆设' },
        { id: 'deco4', name: '迷你地球仪', desc: '转一转，梦想环游世界', price: 30, category: '摆设' },
        { id: 'deco5', name: '动漫手办', desc: '精致的动漫角色摆件', price: 120, category: '摆设' },
        { id: 'deco6', name: '香薰蜡烛', desc: '香香的蜡烛，放松心情', price: 35, category: '摆设' },
        // 新增宠物食品
        { id: 'petfood1', name: '猫粮', desc: '营养丰富的猫咪主粮', price: 32, category: '宠物食品' },
        { id: 'petfood2', name: '狗粮', desc: '美味健康的狗狗主粮', price: 30, category: '宠物食品' },
        { id: 'petfood3', name: '鱼食', desc: '适合观赏鱼的颗粒鱼食', price: 15, category: '宠物食品' },
        { id: 'petfood4', name: '宠物零食', desc: '宠物专属小零嘴', price: 20, category: '宠物食品' },
        { id: 'petfood5', name: '猫薄荷', desc: '让猫咪开心打滚的猫薄荷', price: 12, category: '宠物食品' },
        // 幽默感小物品
        { id: 'funny1', name: '空气吉他', desc: '弹不出声音，但气氛拉满', price: 8, category: '趣味' },
        { id: 'funny2', name: '隐形墨水笔', desc: '写了等于没写，适合写秘密', price: 15, category: '趣味' },
        { id: 'funny3', name: '假胡子', desc: '一秒变身神秘大叔', price: 12, category: '趣味' },
        { id: 'funny4', name: '迷你小黄鸭', desc: '放在桌上，心情自动变好', price: 10, category: '趣味' },
        { id: 'funny5', name: '无用按钮', desc: '按了什么都不会发生，但很解压', price: 5, category: '趣味' },
        { id: 'funny6', name: '假装有用的说明书', desc: '内容全是废话，但看着很专业', price: 6, category: '趣味' },
        { id: 'funny7', name: '神秘空盒子', desc: '里面什么都没有，但充满想象', price: 9, category: '趣味' },
        { id: 'funny8', name: '迷你放大镜', desc: '能把小事看得更大', price: 7, category: '趣味' },
        { id: 'funny9', name: '一张"好运贴纸"', desc: '贴上后，心情+1，运气未定', price: 3, category: '趣味' },
        { id: 'funny10', name: '"今日份摸鱼"证书', desc: '摸鱼有理，效率靠缘分', price: 4, category: '趣味' },
        // 饮料类
        { id: 'drink1', name: '矿泉水', desc: '清凉解渴的瓶装水', price: 5, category: '饮料' },
        { id: 'drink2', name: '可乐', desc: '快乐肥宅水', price: 8, category: '饮料' },
        { id: 'drink3', name: '橙汁', desc: '新鲜橙子榨的果汁', price: 10, category: '饮料' },
        { id: 'drink4', name: '奶茶', desc: '香甜顺滑的珍珠奶茶', price: 15, category: '饮料' },
        { id: 'drink5', name: '咖啡', desc: '提神醒脑的现磨咖啡', price: 16, category: '饮料' },
        { id: 'drink6', name: '能量饮料', desc: '满满元气，熬夜必备', price: 12, category: '饮料' },
        { id: 'drink7', name: '苏打水', desc: '气泡丰富，口感清爽', price: 7, category: '饮料' },
        // 玩具类
        { id: 'toy1', name: '魔方', desc: '锻炼脑力的益智玩具', price: 18, category: '玩具' },
        { id: 'toy2', name: '毛绒熊', desc: '软萌可爱的毛绒玩具熊', price: 35, category: '玩具' },
        { id: 'toy3', name: '遥控小车', desc: '速度与激情的遥控车', price: 60, category: '玩具' },
        { id: 'toy4', name: '弹力球', desc: '弹跳力超强的小球', price: 6, category: '玩具' },
        { id: 'toy5', name: '积木套装', desc: '拼搭乐趣无穷', price: 40, category: '玩具' },
        { id: 'toy6', name: '指尖陀螺', desc: '解压神器，转个不停', price: 9, category: '玩具' },
        { id: 'toy7', name: '水枪', desc: '夏日玩水必备', price: 15, category: '玩具' },
        // 家电类补充
        { id: 'appliance6', name: '微波炉', desc: '加热美食的好帮手', price: 350, category: '家电' },
        { id: 'appliance7', name: '榨汁机', desc: '自制果汁更健康', price: 120, category: '家电' },
        { id: 'appliance8', name: '吹风机', desc: '快速干发，造型必备', price: 90, category: '家电' },
        { id: 'appliance9', name: '电热毯', desc: '冬天温暖如春', price: 110, category: '家电' },
        { id: 'appliance10', name: '空气净化器', desc: '呼吸更健康', price: 400, category: '家电' },
        { id: 'appliance11', name: '咖啡机', desc: '一键萃取香浓咖啡', price: 280, category: '家电' },
        { id: 'appliance12', name: '扫地机器人', desc: '解放双手的清洁小能手', price: 600, category: '家电' },
      ];

      // 培训数据
      const trainingData = {
        performance: {
          title: '排练室',
          options: [
            { name: '基础表演', description: '练习基本表演技巧', cost: 0, skills: ['演技+1'] },
            { name: '专业表演', description: '深入学习表演理论', cost: 500, skills: ['演技+5', '魅力+2'] },
          ],
        },
        singing: {
          title: '练歌房',
          options: [
            { name: '发声练习', description: '基础发声和气息训练', cost: 0, skills: ['唱功+1'] },
            { name: '专业声乐', description: '专业声乐技巧训练', cost: 800, skills: ['唱功+5', '气质+2'] },
          ],
        },
        dancing: {
          title: '练舞室',
          options: [
            { name: '基础舞蹈', description: '学习基本舞蹈动作', cost: 0, skills: ['舞技+1'] },
            { name: '专业舞蹈', description: '专业舞蹈技巧提升', cost: 600, skills: ['舞技+5', '体力+2'] },
          ],
        },
        creation: {
          title: '创作间',
          options: [
            { name: '灵感启发', description: '培养创作思维', cost: 0, skills: ['创意+1'] },
            { name: '专业创作', description: '系统性创作技能培训', cost: 1000, skills: ['创意+5', '智力+3'] },
          ],
        },
      };

      const districts = [
        {
          id: 'tianjing',
          name: '天璟区',
          en: 'Tianjing District',
          intro:
            '华城的中央商务区（CBD），也是整个H省的经济心脏。这里是世界五百强企业、国际金融机构和顶级律师事务所的聚集地。天璟区的建筑以现代、奢华和充满未来感而著称，其中最著名的地标是高达688米的"天璟塔"。各大娱乐公司的总部也多设于此，在这里，你随时可能在咖啡厅偶遇正在谈论上亿合同的制片人或经纪人。',
          mood: '精英、高端、快节奏、纸醉金迷',
          spots: '天璟塔、环球金融中心、星河集团总部、璀璨娱乐大厦、云顶私人会所',
        },
        {
          id: 'xingyao',
          name: '星曜区',
          en: 'Xingyao District',
          intro:
            '华城的娱乐与时尚中心，得名于"星光闪耀"之意。这里遍布着大大小小的电视台、影视拍摄基地、录音棚、Livehouse和奢侈品旗舰店。星曜区的夜晚比白天更加璀璨，狗仔队在这里无处不在，随时准备捕捉最新的头条新闻。这里是明星、练习生、网红和追星族的聚集地，也是流行文化的发源地。',
          mood: '时尚、潮流、喧嚣、充满活力与机遇',
          spots: '华城电视台、星光大道、MUSE-X摄影棚、"焦点"购物中心、1OAK俱乐部',
        },
        {
          id: 'liuli',
          name: '琉璃区',
          en: 'Liuli District',
          intro:
            '华城的文化与艺术之魂。与天璟区的现代感不同，琉璃区保留了大量历史建筑，红砖洋房与青石板路交相辉映。这里聚集了众多的美术馆、博物馆、独立书店、画廊和剧院。许多艺术家、作家和独立音乐人选择在此居住和创作，为这里注入了浓厚的文艺气息。',
          mood: '文艺、复古、宁静、充满创造力',
          spots: '华城大剧院、琉璃艺术馆、南巷7号、"渡口"Livehouse、旧时光咖啡馆',
        },
        {
          id: 'yunting',
          name: '云汀区',
          en: 'Yunting District',
          intro:
            '位于沧江沿岸的新兴富人区。这里环境优美，私密性极高，拥有许多高档住宅、别墅和私人游艇码头。云汀区以其宁静的氛围和开阔的江景吸引了众多明星、名流和企业家在此安家。',
          mood: '宁静、私密、奢华、上流社会',
          spots: '云汀湾一号、天鹅堡高尔夫俱乐部、沧江游艇会、H省国际学校',
        },
        {
          id: 'hanhai',
          name: '瀚海区',
          en: 'Hanhai District',
          intro:
            '华城的工业港口区，也是这座城市最初发展的起点。这里的空气中总是弥漫着海水的咸味和机油的气息。瀚海区龙蛇混杂，虽然不如其他区光鲜，但其独特的仓库和废弃工厂景观吸引了先锋艺术与亚文化。',
          mood: '工业、粗犷、混乱、充满江湖气息',
          spots: '瀚海港码头、798改造艺术区、红星加工厂、"钢铁之心"俱乐部、海鲜大排档',
        },
        {
          id: 'bank',
          name: '银行',
          intro: '办理存取款、贷款、理财等金融业务的地方。',
          mood: '安全、稳定',
          spots: '华城银行总行',
        },
        {
          id: 'hospital',
          name: '医院',
          intro: '提供医疗救治和健康体检的机构。',
          mood: '专业、救治',
          spots: '华城第一人民医院',
        },
      ];

      // 点击事件处理
      function handleShopClick() {
        document.getElementById('shopModal').style.display = 'block';
        renderShopItems();
        updateShopCoins();
        renderCart(); // 初始化购物车显示
      }

      // 渲染商品列表
      function renderShopItems() {
        const shopItemsContainer = document.getElementById('shopItems');
        shopItemsContainer.innerHTML = '';

        shopItems.forEach(item => {
          const itemDiv = document.createElement('div');
          itemDiv.className = 'shop-item';
          itemDiv.innerHTML = `
            <div class="shop-item-info">
              <div class="shop-item-name">${item.name}</div>
              <div class="shop-item-desc">${item.desc}</div>
              <div class="shop-item-price">${item.price} 信用点</div>
            </div>
            <button class="add-to-cart-btn" onclick="addToCart('${item.id}')">
              加入购物车
            </button>
          `;
          shopItemsContainer.appendChild(itemDiv);
        });
      }

      // 更新商城中的信用点显示
      function updateShopCoins() {
        const currentCoins = document.getElementById('currentCoins').textContent;
        document.getElementById('shopCurrentCoins').textContent = currentCoins;
      }

      // 同层减少信用点功能（商城购买专用）
      function reduceCoinsInSameLayer(amount) {
        if (
          typeof getCurrentMessageId === 'function' &&
          typeof getChatMessages === 'function' &&
          typeof setChatMessages === 'function'
        ) {
          const currentMsgId = getCurrentMessageId();
          const messages = getChatMessages(currentMsgId);
          if (!messages || messages.length === 0) return false;

          let content = messages[0].message;
          const ycRegex = /<yangcheng>([\s\S]*?)<\/yangcheng>/;
          const ycMatch = content.match(ycRegex);
          if (!ycMatch) return false;

          let yc = ycMatch[1];

          // 查找并更新信用点
          const coinsUpdated = yc.replace(/\[信用点\|([^\]]+)\]/, (m, p1) => {
            const numMatch = p1.match(/(\d+)/);
            if (!numMatch) return m;
            const oldNum = parseInt(numMatch[1]);
            const newNum = Math.max(0, oldNum - amount); // 确保信用点不会变成负数
            const suffix = p1.slice(numMatch.index + numMatch[1].length);
            return `[信用点|${newNum}${suffix}]`;
          });

          // 如果信用点被更新了
          if (coinsUpdated !== yc) {
            // 重新拼接内容
            const newContent = content.replace(ycRegex, `<yangcheng>${coinsUpdated}</yangcheng>`);

            // 更新消息内容
            messages[0].message = newContent;
            setChatMessages(currentMsgId, messages);

            // 更新本地显示
            const newCoinsMatch = coinsUpdated.match(/\[信用点\|([^\]]+)\]/);
            if (newCoinsMatch) {
              const coinText = newCoinsMatch[1];
              const coinNumber = coinText.match(/\d+/);
              if (coinNumber) {
                document.getElementById('currentCoins').textContent = coinNumber[0];
                updateShopCoins();
              }
            }

            return true;
          }
        }
        return false;
      }

      // 加入购物车
      function addToCart(itemId) {
        const item = shopItems.find(i => i.id === itemId);
        if (!item) return;

        // 检查信用点是否足够
        const currentCoins = parseInt(document.getElementById('currentCoins').textContent);
        if (isNaN(currentCoins) || currentCoins === '--') {
          alert('请先确保游戏数据已加载！');
          return;
        }

        const cartTotal = calculateCartTotal();
        if (cartTotal + item.price > currentCoins) {
          alert('信用点不足！');
          return;
        }

        // 查找购物车中是否已有该商品
        const existingItem = shoppingCart.find(cartItem => cartItem.id === itemId);
        if (existingItem) {
          existingItem.quantity += 1;
        } else {
          shoppingCart.push({
            id: itemId,
            name: item.name,
            price: item.price,
            quantity: 1,
          });
        }

        renderCart();
      }

      // 从购物车移除商品
      function removeFromCart(itemId) {
        const itemIndex = shoppingCart.findIndex(item => item.id === itemId);
        if (itemIndex === -1) return;

        if (shoppingCart[itemIndex].quantity > 1) {
          shoppingCart[itemIndex].quantity -= 1;
        } else {
          shoppingCart.splice(itemIndex, 1);
        }

        renderCart();
      }

      // 渲染购物车
      function renderCart() {
        const cartItemsContainer = document.getElementById('cartItems');
        cartItemsContainer.innerHTML = '';

        if (shoppingCart.length === 0) {
          cartItemsContainer.innerHTML = '<div class="cart-empty">购物车为空</div>';
        } else {
          shoppingCart.forEach(item => {
            const cartItemDiv = document.createElement('div');
            cartItemDiv.className = 'cart-item';
            cartItemDiv.innerHTML = `
              <div class="cart-item-info">
                <div class="cart-item-name">${item.name}</div>
                <div class="cart-item-quantity">数量: ${item.quantity} | 单价: ${item.price}信用点</div>
              </div>
              <button class="cart-item-remove" onclick="removeFromCart('${item.id}')">
                移除
              </button>
            `;
            cartItemsContainer.appendChild(cartItemDiv);
          });
        }

        // 更新总价
        const total = calculateCartTotal();
        document.getElementById('cartTotal').textContent = total;

        // 更新结算按钮状态
        document.getElementById('checkoutBtn').disabled = shoppingCart.length === 0;
      }

      // 计算购物车总价
      function calculateCartTotal() {
        return shoppingCart.reduce((total, item) => total + item.price * item.quantity, 0);
      }

      // 结算购物车
      function checkoutCart() {
        if (shoppingCart.length === 0) return;

        const total = calculateCartTotal();

        // 检查信用点是否足够
        const currentCoins = parseInt(document.getElementById('currentCoins').textContent);
        if (isNaN(currentCoins) || currentCoins === '--') {
          alert('请先确保游戏数据已加载！');
          return;
        }

        if (total > currentCoins) {
          alert('信用点不足！');
          return;
        }

        const itemNames = shoppingCart
          .map(item => (item.quantity > 1 ? `${item.name} ×${item.quantity}` : item.name))
          .join('、');

        // 尝试同层减少信用点
        const coinsReduced = reduceCoinsInSameLayer(total);

        // 发送消息到SillyTavern
        if (typeof triggerSlash === 'function') {
          if (coinsReduced) {
            // 如果成功减少了信用点，只发送购买信息
            triggerSlash(`/send 购买了${itemNames}|/trigger`);
          } else {
            // 如果没有找到可减少的信用点，发送完整信息让AI处理
            triggerSlash(`/send 花费${total}信用点，购买了${itemNames}|/trigger`);
          }
        } else {
          alert(`花费${total}信用点，购买了${itemNames}`);
        }

        // 如果没有成功同层减少，则更新本地显示（等待AI更新）
        if (!coinsReduced) {
          const newCoins = Math.max(0, currentCoins - total);
          document.getElementById('currentCoins').textContent = newCoins.toString();
          updateShopCoins();
        }

        // 清空购物车
        shoppingCart = [];
        renderCart();

        // 关闭商城模态框
        closeModal('shopModal');
      }

      function handleBackpackClick() {
        updateBackpackDisplay();
        document.getElementById('backpackModal').style.display = 'block';
      }

      function handleMissionClick() {
        updateMissionDisplay();
        document.getElementById('missionModal').style.display = 'block';
      }

      function handleMapClick() {
        // 打开地图模态框
        document.getElementById('mapModal').style.display = 'block';
        renderDistrictList();
        document.getElementById('districtDetail').style.display = 'none';
      }

      function handleTrainingClick() {
        document.getElementById('trainingModal').style.display = 'block';
        hideTrainingDetail();
        selectedTraining = null;
      }

      function showTrainingDetail(category) {
        const detail = document.getElementById('trainingDetail');
        const options = document.getElementById('trainingOptions');
        const data = trainingData[category];

        if (!data || !data.options) {
          console.error('培训数据不存在:', category);
          return;
        }

        options.innerHTML = '';
        data.options.forEach((option, index) => {
          const optionDiv = document.createElement('div');
          optionDiv.className = 'training-option';
          optionDiv.onclick = () => selectTraining(category, index);
          optionDiv.innerHTML = `
            <h4>${option.name}</h4>
            <p>${option.description}</p>
            <p>技能提升: ${option.skills.join(', ')}</p>
            <div class="training-cost">${option.cost === 0 ? '免费' : `消耗信用点: ${option.cost}`}</div>
          `;
          options.appendChild(optionDiv);
        });

        detail.style.display = 'block';
        selectedTraining = null;
        updateConfirmButton();
      }

      function selectTraining(category, index) {
        // 清除之前的选中状态
        document.querySelectorAll('.training-option').forEach(el => {
          el.style.backgroundColor = '#fff';
          el.style.borderColor = '#e9ecef';
        });

        // 选中当前项
        const currentElement = event.currentTarget || event.target;
        currentElement.style.backgroundColor = '#e3f2fd';
        currentElement.style.borderColor = '#2196f3';

        selectedTraining = { category, index };
        updateConfirmButton();
      }

      function hideTrainingDetail() {
        document.getElementById('trainingDetail').style.display = 'none';
        selectedTraining = null;
        updateConfirmButton();
      }

      function updateConfirmButton() {
        const btn = document.getElementById('confirmTraining');
        btn.disabled = !selectedTraining;
        btn.textContent = selectedTraining ? '开始培训' : '请选择培训项目';
      }

      function updateDateAndCoins() {
        if (typeof getCurrentMessageId === 'function' && typeof getChatMessages === 'function') {
          const currentMsgId = getCurrentMessageId();
          const messages = getChatMessages(currentMsgId);

          if (messages && messages.length > 0) {
            const messageContent = messages[0].message;

            // 确保在yangcheng标签内查找
            const yangchengMatch = messageContent.match(/<yangcheng>([\s\S]*?)<\/yangcheng>/);
            if (yangchengMatch) {
              const yangchengContent = yangchengMatch[1];

              // 提取日期
              const dateMatch = yangchengContent.match(/\[日期\|([^\]]+)\]/);
              if (dateMatch) {
                document.getElementById('currentDate').textContent = dateMatch[1];
              } else {
                document.getElementById('currentDate').textContent = '--';
              }

              // 提取信用点 - 支持 "10000信用点" 格式
              const coinsMatch = yangchengContent.match(/\[信用点\|([^\]]+)\]/);
              if (coinsMatch) {
                const coinText = coinsMatch[1];
                // 提取数字部分，支持 "10000信用点" 或 "10000" 格式
                const coinNumber = coinText.match(/\d+/);
                if (coinNumber) {
                  document.getElementById('currentCoins').textContent = coinNumber[0];
                } else {
                  document.getElementById('currentCoins').textContent = '--';
                }
              } else {
                document.getElementById('currentCoins').textContent = '--';
              }
            } else {
              // 没有找到yangcheng标签时显示占位符
              document.getElementById('currentDate').textContent = '--';
              document.getElementById('currentCoins').textContent = '--';
            }
          } else {
            // 没有消息时显示占位符
            document.getElementById('currentDate').textContent = '--';
            document.getElementById('currentCoins').textContent = '--';
          }
        } else {
          // SillyTavern API不可用时显示占位符
          document.getElementById('currentDate').textContent = '--';
          document.getElementById('currentCoins').textContent = '--';
        }
      }

      function updateBackpackDisplay() {
        const content = document.getElementById('backpackContent');

        if (typeof getCurrentMessageId === 'function' && typeof getChatMessages === 'function') {
          const currentMsgId = getCurrentMessageId();
          const messages = getChatMessages(currentMsgId);

          if (messages && messages.length > 0) {
            const messageContent = messages[0].message;

            // 在yangcheng标签内查找背包
            const yangchengMatch = messageContent.match(/<yangcheng>([\s\S]*?)<\/yangcheng>/);
            if (yangchengMatch) {
              const backpackMatch = yangchengMatch[1].match(/<背包>([\s\S]*?)<\/背包>/);

              if (backpackMatch) {
                const items = backpackMatch[1].trim();
                if (items) {
                  // 支持逗号分隔的物品，如：防狼喷雾，电棍，物理书
                  const itemList = items.split(/[，,]/).filter(item => item.trim());
                  content.innerHTML = itemList.map(item => `<div class="item">${item.trim()}</div>`).join('');
                } else {
                  content.innerHTML = '<div class="empty-state">背包为空</div>';
                }
              } else {
                content.innerHTML = '<div class="empty-state">背包为空</div>';
              }
            } else {
              content.innerHTML = '<div class="empty-state">背包为空</div>';
            }
          } else {
            content.innerHTML = '<div class="empty-state">背包为空</div>';
          }
        } else {
          content.innerHTML = '<div class="empty-state">背包为空</div>';
        }
      }

      function updateMissionDisplay() {
        const content = document.getElementById('missionContent');

        if (typeof getCurrentMessageId === 'function' && typeof getChatMessages === 'function') {
          const currentMsgId = getCurrentMessageId();
          const messages = getChatMessages(currentMsgId);

          if (messages && messages.length > 0) {
            const messageContent = messages[0].message;

            // 在yangcheng标签内查找任务
            const yangchengMatch = messageContent.match(/<yangcheng>([\s\S]*?)<\/yangcheng>/);
            if (yangchengMatch) {
              const missionMatch = yangchengMatch[1].match(/<任务>([\s\S]*?)<\/任务>/);

              if (missionMatch) {
                const missions = missionMatch[1].trim();
                if (missions) {
                  // 按行分割任务，支持多行任务
                  const missionList = missions.split('\n').filter(mission => mission.trim());
                  content.innerHTML = missionList
                    .map(mission => `<div class="mission">${mission.trim()}</div>`)
                    .join('');
                } else {
                  content.innerHTML = '<div class="empty-state">暂无任务</div>';
                }
              } else {
                content.innerHTML = '<div class="empty-state">暂无任务</div>';
              }
            } else {
              content.innerHTML = '<div class="empty-state">暂无任务</div>';
            }
          } else {
            content.innerHTML = '<div class="empty-state">暂无任务</div>';
          }
        } else {
          content.innerHTML = '<div class="empty-state">暂无任务</div>';
        }
      }

      // 从SillyTavern更新培训历史显示
      function updateTrainingHistoryDisplay() {
        const historyDisplay = document.getElementById('trainingHistoryDisplay');

        if (typeof getCurrentMessageId === 'function' && typeof getChatMessages === 'function') {
          const currentMsgId = getCurrentMessageId();
          const messages = getChatMessages(currentMsgId);

          if (messages && messages.length > 0) {
            const messageContent = messages[0].message;

            // 在yangcheng标签内查找培训历史
            const yangchengMatch = messageContent.match(/<yangcheng>([\s\S]*?)<\/yangcheng>/);
            if (yangchengMatch) {
              const trainingMatch = yangchengMatch[1].match(/<培训>([\s\S]*?)<\/培训>/);

              if (trainingMatch) {
                const trainingHistory = trainingMatch[1].trim();

                if (trainingHistory) {
                  // 按行分割，过滤空行
                  const trainingList = trainingHistory.split('\n').filter(item => item.trim());
                  historyDisplay.innerHTML = '';

                  // 显示最近的培训记录（最多10条）
                  trainingList.slice(-10).forEach(item => {
                    const historyItem = document.createElement('div');
                    historyItem.className = 'training-history-item';
                    historyItem.textContent = item.trim();
                    historyDisplay.appendChild(historyItem);
                  });

                  // 滚动到最新记录
                  historyDisplay.scrollTop = historyDisplay.scrollHeight;
                } else {
                  historyDisplay.innerHTML = '<div class="empty-state">暂无培训记录</div>';
                }
              } else {
                historyDisplay.innerHTML = '<div class="empty-state">暂无培训记录</div>';
              }
            } else {
              historyDisplay.innerHTML = '<div class="empty-state">暂无培训记录</div>';
            }
          } else {
            historyDisplay.innerHTML = '<div class="empty-state">暂无培训记录</div>';
          }
        } else {
          historyDisplay.innerHTML = '<div class="empty-state">暂无培训记录</div>';
        }
      }

      /* 新增：同层插入培训结果并更新信用点 */
      function insertTrainingResultAndUpdateCoins(result, cost) {
        if (
          typeof getCurrentMessageId === 'function' &&
          typeof getChatMessages === 'function' &&
          typeof setChatMessages === 'function'
        ) {
          const currentMsgId = getCurrentMessageId();
          const messages = getChatMessages(currentMsgId);
          if (!messages || messages.length === 0) return;

          let content = messages[0].message;
          const ycRegex = /<yangcheng>([\s\S]*?)<\/yangcheng>/;
          const ycMatch = content.match(ycRegex);
          if (!ycMatch) return;

          let yc = ycMatch[1];

          // 更新信用点
          if (cost > 0) {
            yc = yc.replace(/\[信用点\|([^\]]+)\]/, (m, p1) => {
              const numMatch = p1.match(/(\d+)/);
              if (!numMatch) return m;
              const oldNum = parseInt(numMatch[1]);
              const newNum = Math.max(0, oldNum - cost);
              const suffix = p1.slice(numMatch.index + numMatch[1].length);
              return `[信用点|${newNum}${suffix}]`;
            });
          }

          // 插入培训结果
          yc = yc.replace(/<培训>([\s\S]*?)<\/培训>/, (m, inner) => {
            const trimmed = inner.trim();
            const newInner = trimmed ? `${trimmed}\n${result}` : result;
            return `<培训>\n${newInner}\n</培训>`;
          });

          // 重新拼接内容
          const newContent = content.replace(ycRegex, `<yangcheng>${yc}</yangcheng>`);

          setChatMessages([{ message_id: currentMsgId, message: newContent }], { refresh: 'none' });
        }
      }

      function confirmTraining() {
        if (!selectedTraining) return;

        const { category, index } = selectedTraining;
        const data = trainingData[category];
        const option = data.options[index];

        // 检查信用点是否足够
        const currentCoinsText = document.getElementById('currentCoins').textContent;
        if (currentCoinsText === '--') {
          alert('请先确保游戏数据已加载！');
          return;
        }

        const currentCoins = parseInt(currentCoinsText);
        if (isNaN(currentCoins) || option.cost > currentCoins) {
          alert('信用点不足！');
          return;
        }

        // 生成培训结果文案
        const trainingResults = generateTrainingResult(category, option);

        // 同层插入培训结果并更新信用点
        insertTrainingResultAndUpdateCoins(trainingResults, option.cost);

        // 在页面上显示培训结果
        addTrainingHistoryToDisplay(trainingResults);

        // 关闭模态框
        closeModal('trainingModal');

        // 延迟更新显示，确保数据同步
        setTimeout(() => {
          updateDateAndCoins();
          updateTrainingHistoryDisplay();
        }, 300);
      }

      // 在页面显示培训历史
      function addTrainingHistoryToDisplay(trainingResult) {
        const historyDisplay = document.getElementById('trainingHistoryDisplay');

        // 如果是空状态，先清空
        if (historyDisplay.innerHTML.includes('暂无培训记录')) {
          historyDisplay.innerHTML = '';
        }

        // 创建新的培训记录项
        const historyItem = document.createElement('div');
        historyItem.className = 'training-history-item';
        historyItem.textContent = trainingResult;

        // 添加到底部（最新的记录在下面）
        historyDisplay.appendChild(historyItem);

        // 限制显示最多10条记录
        const items = historyDisplay.querySelectorAll('.training-history-item');
        if (items.length > 10) {
          historyDisplay.removeChild(items[0]);
        }

        // 滚动到最新记录
        historyDisplay.scrollTop = historyDisplay.scrollHeight;
      }

      // 生成培训结果文案
      function generateTrainingResult(category, option) {
        const resultTemplates = {
          performance: {
            基础表演: [
              '在排练室里反复练习了基本表演动作，感觉演技有了一些进步。',
              '跟着教练学习了一些基础的表演技巧，收获颇丰。',
              '在镜子前练习了各种表情和动作，对表演有了更深的理解。',
            ],
            专业表演: [
              '接受了专业表演导师的指导，学会了更高级的表演技巧，演技和魅力都有了显著提升。',
              '参加了高强度的表演训练课程，不仅演技大幅提升，个人魅力也得到了很好的锻炼。',
              '在专业排练室里进行了系统性的表演训练，感觉自己的演技水平和舞台魅力都上了一个台阶。',
            ],
          },
          singing: {
            发声练习: [
              '在练歌房里练习了基本的发声技巧，嗓音变得更加清澈。',
              '跟着音乐练习了气息控制，唱功有了一定的提升。',
              '反复练习了几首歌曲，感觉音准和节奏感都有了改善。',
            ],
            专业声乐: [
              '接受了专业声乐老师的悉心指导，唱功得到了质的飞跃，整个人的气质也变得更加优雅。',
              '参加了高级声乐训练课程，不仅技法大幅提升，音乐素养和个人气质也得到了很好的培养。',
              '在专业录音棚里进行了系统的声乐训练，感觉自己的歌唱水平和艺术气质都有了显著提升。',
            ],
          },
          dancing: {
            基础舞蹈: [
              '在练舞室里学习了基本的舞蹈动作，身体的协调性得到了锻炼。',
              '跟着视频教程练习了一些简单的舞蹈，舞技有了一些进步。',
              '反复练习了基础舞步，感觉肢体表现力有了一定的提升。',
            ],
            专业舞蹈: [
              '接受了专业舞蹈老师的指导，学会了更高难度的舞蹈技巧，舞技和体力都有了很大提升。',
              '参加了高强度的舞蹈训练课程，不仅技巧大幅提升，身体素质也得到了很好的锻炼。',
              '在专业舞蹈教室里进行了系统训练，感觉自己的舞蹈水平和身体协调性都上了一个新的台阶。',
            ],
          },
          creation: {
            灵感启发: [
              '在创作间里冥想和思考，感觉创意思维得到了一些启发。',
              '尝试了一些新的创作方法，创意能力有了一定的提升。',
              '通过各种创作练习，感觉想象力和创造力都有了进步。',
            ],
            专业创作: [
              '接受了专业创作导师的指导，学会了系统性的创作技巧，创意能力和智力都有了质的提升。',
              '参加了高级创作训练课程，不仅创作技法大幅提升，思维能力也得到了很好的锻炼。',
              '在专业创作工作室里进行了深度学习，感觉自己的创意水平和思维深度都上了一个新的层次。',
            ],
          },
        };

        const templates = resultTemplates[category][option.name];
        const randomTemplate = templates[Math.floor(Math.random() * templates.length)];

        const skillGains = option.skills.join('、');
        const costText = option.cost > 0 ? `，消耗了${option.cost}信用点` : '';

        return `${randomTemplate}（${skillGains}${costText}）`;
      }

      function renderDistrictList() {
        const list = document.getElementById('districtList');
        list.innerHTML = `
          <div class="map-container">
            <div class="district-buttons">
              ${districts
                .filter(d => !['bank', 'hospital'].includes(d.id))
                .map(
                  d => `
                <button class="district-button" onclick="showDistrictDetail('${d.id}')">
                  ${d.name}
                </button>
              `,
                )
                .join('')}
            </div>
            <div id="districtDetailPanel" class="district-detail"></div>
          </div>
        `;
      }

      function showDistrictDetail(id) {
        const detailDiv = document.getElementById('districtDetailPanel');
        const dist = districts.find(d => d.id === id);
        if (!dist) return;

        // 移除所有按钮的活跃状态
        document.querySelectorAll('.district-button').forEach(btn => {
          btn.style.background = 'rgba(255, 255, 255, 0.8)';
        });
        // 设置当前按钮的活跃状态
        const currentBtn = event.currentTarget;
        currentBtn.style.background = '#fff';

        detailDiv.innerHTML = `
          <h3>${dist.name} (${dist.en})</h3>
          <p>${dist.intro}</p>
          <p><strong>氛围：</strong>${dist.mood}</p>
          <p><strong>关键地点：</strong>${dist.spots}</p>
          <button class="visit-button" onclick="confirmTravel('${dist.name}')">前往${dist.name}</button>
        `;

        detailDiv.style.display = 'block';
        detailDiv.classList.remove('active');
        void detailDiv.offsetWidth; // 触发重排
        detailDiv.classList.add('active');
      }

      function confirmTravel(name) {
        if (typeof triggerSlash === 'function') {
          triggerSlash(`/send 前往${name}|/trigger`);
        } else {
          alert(`前往${name}`);
        }
        closeModal('mapModal');
      }

      // 关闭模态框函数
      function closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
          modal.style.display = 'none';
        }
      }

      // 点击模态框外部关闭
      window.onclick = function (event) {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
          if (event.target === modal) {
            modal.style.display = 'none';
          }
        });
      };

      // 初始化
      function init() {
        updateDateAndCoins();
        updateTrainingHistoryDisplay();

        // 定期更新数据
        setInterval(() => {
          updateDateAndCoins();
          updateTrainingHistoryDisplay();
        }, 1000);
      }

      // 背景图片数组
      const bgImages = [
        'video',
        'https://files.catbox.moe/06g8ro.png',
        'https://files.catbox.moe/zp392s.png',
        'https://files.catbox.moe/vnzn94.png',
        'https://files.catbox.moe/nmdqpq.jpeg',
        'https://files.catbox.moe/a6e9z3.jpeg',
        'https://files.catbox.moe/w26obm.png',
        'https://files.catbox.moe/17qh0m.png',
        'https://files.catbox.moe/knw7vv.png',
        'https://files.catbox.moe/lyfsuz.png',
      ];
      let currentBgIndex = 0;
      let bgTimer = null;

      function setBgImage(idx) {
        const img = document.getElementById('bgImage');
        const video = document.getElementById('bgVideo');
        if (idx === 0) {
          // 显示视频，隐藏图片
          if (img) img.style.display = 'none';
          if (video) {
            video.style.display = '';
            video.style.opacity = 1;
          }
        } else {
          // 显示图片，隐藏视频
          if (video) video.style.display = 'none';
          if (img) {
            img.style.opacity = 0;
            img.style.display = '';
            setTimeout(() => {
              img.src = bgImages[idx];
              img.style.opacity = 1;
            }, 300);
          }
        }
      }

      function nextBgImage() {
        currentBgIndex = (currentBgIndex + 1) % bgImages.length;
        setBgImage(currentBgIndex);
      }

      function autoBgSwitch() {
        bgTimer = setInterval(() => {
          nextBgImage();
        }, 10000);
      }

      // 初始化背景
      function initBgImage() {
        currentBgIndex = 0; // 默认视频
        setBgImage(currentBgIndex);
        autoBgSwitch();
      }

      // 页面加载完成后初始化
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function () {
          init();
          initBgImage();
        });
      } else {
        init();
        initBgImage();
      }
    </script>
  </body>
</html>
