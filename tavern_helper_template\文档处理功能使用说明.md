# 📄 文档处理功能使用说明

## 功能概述

现在你的同层私聊界面已经支持真实文档处理功能！可以上传和处理各种文档格式，并通过SillyTavern内置的AI生成函数进行文档分析。

## 🔧 技术实现

### 核心机制
- **利用SillyTavern内置函数**：`AI_GENERATE` 和 `AI_GENERATE_RAW`
- **文档内容注入**：将文档内容作为系统提示注入到AI请求中
- **延迟分析**：上传后不立即触发，等待用户点击绿色按钮
- **智能识别**：自动检测5分钟内上传的文档并包含在AI分析中

## 🚀 已完成的功能

### 1. **插件扩展**
- ✅ 扩展了 `third-party-image-processor.js` 插件
- ✅ 添加了文档处理功能
- ✅ 支持自动文件类型识别

### 2. **支持的文件格式**
- 📄 **文本文件**: `.txt` ✅ 完全支持
- 📘 **Word文档**: `.doc`, `.docx` ⚠️ 基础支持（建议转换为.txt）
- 📋 **JSON文件**: `.json` ✅ 完全支持
- 📝 **Markdown文件**: `.md` ✅ 完全支持
- 📊 **CSV文件**: `.csv` ✅ 完全支持
- 🌐 **HTML文件**: `.html` ✅ 完全支持
- 📋 **XML文件**: `.xml` ✅ 完全支持
- 📄 **RTF文件**: `.rtf` ✅ 完全支持
- 📕 **PDF文档**: `.pdf` ❌ 需要SillyTavern Data Bank

### 3. **新增的全局函数**
```javascript
// 处理任意文件（自动识别类型）
window.__processFileByPlugin(file, options)

// 专门处理文档
window.__processDocumentByPlugin(documentFile, options)

// 获取支持的文件类型
window.__getSupportedFileTypes()

// 文档操作函数
window.toggleDocumentContent(messageId)
window.copyDocumentContent(messageId)
window.requestDocumentAnalysis(messageId)
```

## 📱 如何使用

### 方法1：上传真实文件
1. 点击聊天界面的 **📄 文件** 按钮
2. 选择 **📁 上传真实文件**
3. 选择你要上传的文档文件
4. 添加说明（可选）
5. 勾选 **🤖 启用AI阅读分析**（推荐）
6. 点击 **发送**
7. **重要**：文档上传后不会立即触发AI回复
8. **点击左上角绿色按钮**开始与AI对话，AI会自动分析刚上传的文档

### 方法2：文字描述
1. 点击聊天界面的 **📄 文件** 按钮
2. 选择 **✏️ 文字描述**
3. 输入文档的描述内容
4. 点击 **发送**

## 🤖 AI阅读功能

当你上传真实文档并启用AI阅读时，系统会：

1. **自动读取文档内容**
2. **发送给AI进行分析**
3. **AI会提供**：
   - 文档主题和类型
   - 关键信息摘要
   - 重要观点或结论
   - 需要讨论的问题

## 💡 使用示例

### 示例1：上传项目文档
```
用户操作：
1. 上传 "项目计划.docx"
2. 说明：请帮我分析这个项目的时间安排
3. 启用AI阅读

AI回复：
根据您上传的项目计划文档，我分析了以下内容：

1. **项目类型**：软件开发项目
2. **关键信息**：
   - 项目周期：3个月
   - 团队规模：5人
   - 主要里程碑：需求分析、设计、开发、测试
3. **时间安排分析**：
   - 第1月：需求分析和设计
   - 第2月：核心功能开发
   - 第3月：测试和部署
4. **建议**：建议在第2个月增加代码审查环节
```

### 示例2：上传数据文件
```
用户操作：
1. 上传 "销售数据.csv"
2. 说明：帮我分析销售趋势
3. 启用AI阅读

AI回复：
根据您的销售数据文件，我发现：

1. **数据概况**：包含6个月的销售记录
2. **趋势分析**：
   - 整体呈上升趋势
   - 3月和6月是销售高峰
   - 产品A的销量最好
3. **关键发现**：
   - 周末销量比工作日高30%
   - 促销活动效果显著
4. **建议**：可以考虑增加周末促销活动
```

## 🔧 技术实现

### 插件架构
```
third-party-image-processor.js
├── 图像处理功能（原有）
├── 文档处理功能（新增）
│   ├── DocumentProcessor 类
│   ├── 文件验证和读取
│   └── AI阅读集成
└── 统一文件处理接口
```

### 消息渲染
- 新增 `renderDocumentMessage` 函数
- 支持文档预览和操作
- 美观的文档卡片样式

### 文件处理流程
```
用户选择文件 → 插件处理 → 内容提取 → 消息发送 → AI分析（可选）
```

## 🐛 故障排除

### 问题1：点击发送后没有显示消息
**解决方案**：
1. 检查浏览器控制台是否有错误
2. 确认插件是否正确加载
3. 检查文件格式是否支持

### 问题2：AI无法读取文档
**解决方案**：
1. 确认文档内容不为空
2. 检查文件编码是否正确
3. 尝试重新上传文件

### 问题3：插件功能不可用
**解决方案**：
1. 刷新页面重新加载插件
2. 检查 `third-party-image-processor.js` 是否正确加载
3. 查看控制台调试信息

## 📋 调试信息

打开浏览器控制台，你应该看到：
```
📄 Document processing functions loaded:
- toggleDocumentContent(messageId): 切换文档内容显示
- copyDocumentContent(messageId): 复制文档内容  
- requestDocumentAnalysis(messageId): 请求AI分析文档
✅ 文档处理插件已加载
✅ 文档消息渲染器已注册
```

## 🎯 下一步计划

1. **增强PDF支持**：集成PDF.js库
2. **Office文档支持**：添加Word/Excel解析
3. **文档搜索**：在历史文档中搜索
4. **批量处理**：支持多文件上传
5. **文档管理**：文档库和分类功能

## 💬 使用建议

1. **文件大小**：建议单个文件不超过50MB
2. **文件格式**：优先使用.txt、.json、.md等纯文本格式
3. **AI分析**：复杂文档建议启用AI阅读
4. **备注说明**：添加清晰的处理说明有助于AI理解

---

现在你可以在同层私聊界面中真正地上传和处理文档了！🎉
