<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>伊莱恩手机主界面</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      body {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: #f0f0f8;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      }
      .phone-container {
        width: 360px;
        height: 640px;
        border: 8px solid #a48ad4;
        border-radius: 36px;
        background: linear-gradient(145deg, #e6e0f8, #c9b6e4);
        position: relative;
        overflow: hidden;
        box-shadow: 0 0 30px rgba(164, 138, 212, 0.25), 0 8px 32px rgba(164, 138, 212, 0.18);
      }
      .phone-notch {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 120px;
        height: 12px;
        background: #a48ad4;
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
        z-index: 999;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 6px;
      }
      .notch-camera {
        width: 7px;
        height: 7px;
        background: #2c3e50;
        border-radius: 50%;
        border: 1px solid #34495e;
      }
      .notch-speaker {
        width: 35px;
        height: 3px;
        background: #2c3e50;
        border-radius: 2px;
      }
      .notch-sensor {
        width: 5px;
        height: 5px;
        background: #2c3e50;
        border-radius: 50%;
        border: 1px solid #34495e;
      }
      .screen {
        width: 100%;
        height: 100%;
        background: url('https://files.catbox.moe/dyvyw4.jpeg') center center/cover no-repeat,
          linear-gradient(135deg, #f7f3ff 0%, #e6e0f8 100%);
        border-radius: 28px;
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
      }
      .status-bar {
        height: 32px;
        background: rgba(164, 138, 212, 0.08);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 18px;
        color: #7c5fa3;
        font-size: 13px;
        z-index: 1000;
      }
      .app-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 32px 28px;
        margin: 70px 0 0 0;
        justify-items: center;
        flex: 1;
      }
      .app-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        transition: transform 0.2s ease;
      }
      .app-icon:hover {
        transform: scale(1.08);
      }
      .app-icon-bg {
        width: 62px;
        height: 62px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        box-shadow: 0 4px 12px rgba(164, 138, 212, 0.13);
      }
      .shop-icon {
        background: linear-gradient(135deg, #ffb6b9 60%, #fcdff1 100%);
      }
      .bilibili-icon {
        background: linear-gradient(135deg, #00a1d6 60%, #fb7299 100%);
      }
      .wechat-icon {
        background: linear-gradient(135deg, #a8e063 60%, #56ab2f 100%);
      }
      .app-name {
        color: #7c5fa3;
        font-size: 13px;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
        font-weight: 600;
      }
      .dock {
        width: 100%;
        display: flex;
        justify-content: space-around;
        align-items: center;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 0 0 24px 24px;
        padding: 12px 0 10px 0;
        box-shadow: 0 -2px 8px rgba(164, 138, 212, 0.08);
        position: absolute;
        bottom: 0;
        left: 0;
      }
      .dock-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: linear-gradient(135deg, #f7cac9 60%, #fff6e0 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 8px;
        box-shadow: 0 2px 6px rgba(164, 138, 212, 0.1);
      }
      .dock-icon svg {
        width: 28px;
        height: 28px;
      }
      .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.35);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 2000;
      }
      .modal-content {
        background: #fff;
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
        width: 90vw;
        max-width: 500px;
        max-height: 85vh;
        overflow-y: auto;
        position: relative;
      }
      .close-btn {
        position: absolute;
        right: 18px;
        top: 12px;
        font-size: 28px;
        cursor: pointer;
        color: #aaa;
        z-index: 10;
      }
      .close-btn:hover {
        color: #e57373;
      }
      .shop-items {
        display: flex;
        flex-direction: column;
        gap: 14px;
        max-height: 300px;
        overflow-y: auto;
        margin-bottom: 20px;
        padding: 5px;
      }
      .shop-item {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 14px 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(164, 138, 212, 0.07);
      }
      .shop-item:hover {
        background: #f8f9fa;
        border-color: #a48ad4;
        transform: translateY(-1px);
      }
      .shop-item-info {
        flex: 1;
        min-width: 0;
      }
      .shop-item-name {
        font-weight: 700;
        color: #7c5fa3;
        margin-bottom: 4px;
        font-size: 15px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .shop-item-desc {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
        word-break: break-all;
      }
      .shop-item-price {
        font-size: 14px;
        color: #ff6b6b;
        font-weight: 600;
      }
      .add-to-cart-btn {
        background: linear-gradient(135deg, #a48ad4 60%, #ffb6b9 100%);
        color: #fff;
        border: none;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 13px;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-left: 10px;
        font-weight: 600;
        box-shadow: 0 2px 6px rgba(164, 138, 212, 0.1);
      }
      .add-to-cart-btn:hover {
        background: linear-gradient(135deg, #ffb6b9 60%, #a48ad4 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(164, 138, 212, 0.18);
      }
      .add-to-cart-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
      .cart-section {
        border-top: 1px solid #e9ecef;
        padding-top: 15px;
      }
      .cart-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #7c5fa3;
      }
      .cart-items {
        max-height: 150px;
        overflow-y: auto;
        margin-bottom: 10px;
      }
      .cart-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 5px;
      }
      .cart-item-info {
        flex: 1;
      }
      .cart-item-name {
        font-weight: 500;
        color: #7c5fa3;
      }
      .cart-item-quantity {
        font-size: 12px;
        color: #666;
      }
      .cart-item-remove {
        background: #ff6b6b;
        color: #fff;
        border: none;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
      }
      .cart-item-remove:hover {
        background: #ff5252;
        transform: scale(1.05);
      }
      .cart-total {
        text-align: center;
        font-weight: 600;
        font-size: 16px;
        color: #7c5fa3;
        margin-bottom: 15px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
      }
      .cart-empty {
        text-align: center;
        color: #666;
        font-style: italic;
        padding: 20px;
      }
      .gold-display {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 15px;
        text-align: center;
        font-weight: 600;
        color: #7c5fa3;
      }
      .confirm-btn {
        background: linear-gradient(135deg, #a48ad4 60%, #ffb6b9 100%);
        color: #fff;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        width: 100%;
        margin-top: 15px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 6px rgba(164, 138, 212, 0.1);
      }
      .confirm-btn:hover {
        background: linear-gradient(135deg, #ffb6b9 60%, #a48ad4 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(164, 138, 212, 0.18);
      }
      .confirm-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
    </style>
  </head>
  <body>
    <div class="phone-container">
      <div class="phone-notch">
        <div class="notch-camera"></div>
        <div class="notch-speaker"></div>
        <div class="notch-sensor"></div>
      </div>
      <div class="screen">
        <div class="status-bar">
          <div id="time">12:34</div>
          <div>📶 <span id="gold-amount">$1</span> 🔋 100%</div>
        </div>
        <div class="app-grid">
          <div class="app-icon" onclick="openShop()">
            <div class="app-icon-bg shop-icon">
              <svg width="36" height="36" viewBox="0 0 24 24" fill="none">
                <path
                  d="M3 7l1.5 9a2 2 0 002 2h11a2 2 0 002-2L21 7"
                  stroke="#fff"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <circle cx="9" cy="21" r="1" fill="#fff" />
                <circle cx="17" cy="21" r="1" fill="#fff" />
              </svg>
            </div>
            <div class="app-name">商店</div>
          </div>
          <div class="app-icon" onclick="openBilibili()">
            <div class="app-icon-bg bilibili-icon">
              <svg width="36" height="36" viewBox="0 0 24 24" fill="none">
                <path
                  d="M7 2L9 4H15L17 2H19V4H21C21.5523 4 22 4.44772 22 5V19C22 19.5523 21.5523 20 21 20H3C2.44772 20 2 19.5523 2 19V5C2 4.44772 2.44772 4 3 4H5V2H7Z"
                  fill="#fff"
                />
                <circle cx="8" cy="12" r="1.5" fill="#00a1d6" />
                <circle cx="16" cy="12" r="1.5" fill="#00a1d6" />
                <path
                  d="M6 16C6 16 8 18 12 18C16 18 18 16 18 16"
                  stroke="#00a1d6"
                  stroke-width="2"
                  stroke-linecap="round"
                />
              </svg>
            </div>
            <div class="app-name">哔哩哔哩</div>
          </div>
          <div class="app-icon" onclick="openWechat()">
            <div class="app-icon-bg wechat-icon">
              <svg width="36" height="36" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" fill="#fff" />
                <ellipse cx="9" cy="12" rx="1.5" ry="1.5" fill="#7c5fa3" />
                <ellipse cx="15" cy="12" rx="1.5" ry="1.5" fill="#7c5fa3" />
              </svg>
            </div>
            <div class="app-name">微信</div>
          </div>
        </div>
        <div class="dock">
          <div class="dock-icon" onclick="openShop()">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
              <path
                d="M3 7l1.5 9a2 2 0 002 2h11a2 2 0 002-2L21 7"
                stroke="#ffb6b9"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <circle cx="9" cy="21" r="1" fill="#ffb6b9" />
              <circle cx="17" cy="21" r="1" fill="#ffb6b9" />
            </svg>
          </div>
          <div class="dock-icon" onclick="openBilibili()">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
              <path
                d="M7 2L9 4H15L17 2H19V4H21C21.5523 4 22 4.44772 22 5V19C22 19.5523 21.5523 20 21 20H3C2.44772 20 2 19.5523 2 19V5C2 4.44772 2.44772 4 3 4H5V2H7Z"
                fill="#00a1d6"
              />
              <circle cx="8" cy="12" r="1.5" fill="#fff" />
              <circle cx="16" cy="12" r="1.5" fill="#fff" />
              <path d="M6 16C6 16 8 18 12 18C16 18 18 16 18 16" stroke="#fff" stroke-width="2" stroke-linecap="round" />
            </svg>
          </div>
          <div class="dock-icon" onclick="openWechat()">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="10" fill="#a8e063" />
              <ellipse cx="9" cy="12" rx="1.5" ry="1.5" fill="#fff" />
              <ellipse cx="15" cy="12" rx="1.5" ry="1.5" fill="#fff" />
            </svg>
          </div>
        </div>
      </div>
      <div id="shopModal" class="modal">
        <div class="modal-content">
          <span class="close-btn" onclick="closeModal('shopModal')">&times;</span>
          <h2 class="modal-title">🪐 虫族世界商店</h2>
          <div class="modal-body">
            <div class="gold-display">
              <span>当前信用点：</span>
              <span id="shopCurrentGold">$1</span>
            </div>
            <div class="shop-items" id="shopItems"></div>
            <div class="cart-section">
              <h3 class="cart-title">🛒 购物车</h3>
              <div id="cartItems" class="cart-items"></div>
              <div class="cart-total">
                <span>总计：</span>
                <span id="cartTotal">0</span>
                <span>信用点</span>
              </div>
              <button id="checkoutBtn" class="confirm-btn" onclick="checkoutCart()">确定购买</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      // 初始化游戏状态变量
      let gold = 1; // 初始信用点
      let shoppingCart = []; // 购物车

      function updateTime() {
        const now = new Date();
        const timeStr = now.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        });
        document.getElementById('time').textContent = timeStr;
      }
      setInterval(updateTime, 1000);
      updateTime();

      // 从SillyTavern更新信用点显示
      function updateGoldFromST() {
        if (typeof getCurrentMessageId === 'function' && typeof getChatMessages === 'function') {
          const currentMsgId = getCurrentMessageId();
          const messages = getChatMessages(currentMsgId);

          if (messages && messages.length > 0) {
            const messageContent = messages[0].message;

            // 首先尝试匹配养成系统的 <yangcheng> 标签内的信用点
            const yangchengRegex = /<yangcheng>([\s\S]*?)<\/yangcheng>/;
            const yangchengMatch = messageContent.match(yangchengRegex);
            if (yangchengMatch) {
              const yangchengContent = yangchengMatch[1];
              const goldMatch = yangchengContent.match(/\[信用点\|([^\]]+)\]/);
              if (goldMatch) {
                const goldText = goldMatch[1];
                const goldNumber = goldText.match(/\d+/);
                if (goldNumber) {
                  gold = parseInt(goldNumber[0]);
                  updateGoldDisplay();
                  return;
                }
              }
            }

            // 备用方案1：匹配 <zhujiemian> 标签内的信用点
            const zhujiemianMatch = messageContent.match(/<zhujiemian>\s*信用点：(\d+)\s*<\/zhujiemian>/);
            if (zhujiemianMatch) {
              gold = parseInt(zhujiemianMatch[1]);
              updateGoldDisplay();
              return;
            }

            // 备用方案2：查找其他格式的信用点信息
            const goldMatch = messageContent.match(/\[信用点\|([^\]]+)\]|\[信用点\|([^\]]+)\]/);
            if (goldMatch) {
              const goldText = goldMatch[1] || goldMatch[2];
              // 提取数字部分
              const goldNumber = goldText.match(/\d+/);
              if (goldNumber) {
                gold = parseInt(goldNumber[0]);
                updateGoldDisplay();
              }
            }
          }
        }
      }

      // 更新信用点显示
      function updateGoldDisplay() {
        document.getElementById('gold-amount').textContent = `$${gold}`;
      }

      // 同层减少信用点功能
      function reduceGoldInSameLayer(amount) {
        if (
          typeof getCurrentMessageId === 'function' &&
          typeof getChatMessages === 'function' &&
          typeof setChatMessages === 'function'
        ) {
          const currentMsgId = getCurrentMessageId();
          const messages = getChatMessages(currentMsgId);
          if (!messages || messages.length === 0) return false;

          let content = messages[0].message;

          // 优先处理养成系统的 <yangcheng> 标签内的信用点
          const yangchengRegex = /<yangcheng>([\s\S]*?)<\/yangcheng>/;
          const yangchengMatch = content.match(yangchengRegex);

          if (yangchengMatch) {
            let yangchengContent = yangchengMatch[1];

            // 查找并更新信用点
            const goldUpdated = yangchengContent.replace(/\[信用点\|([^\]]+)\]/, (match, goldText) => {
              const goldNumber = goldText.match(/(\d+)/);
              if (!goldNumber) return match;

              const oldGold = parseInt(goldNumber[1]);
              const newGold = Math.max(0, oldGold - amount); // 确保信用点不会变成负数
              const suffix = goldText.slice(goldNumber.index + goldNumber[1].length);
              return `[信用点|${newGold}${suffix}]`;
            });

            // 如果信用点被更新了
            if (goldUpdated !== yangchengContent) {
              // 重新拼接内容
              const newContent = content.replace(yangchengRegex, `<yangcheng>${goldUpdated}</yangcheng>`);

              // 更新消息内容
              messages[0].message = newContent;
              setChatMessages(currentMsgId, messages);

              // 更新本地显示
              const newGoldMatch = goldUpdated.match(/\[信用点\|([^\]]+)\]/);
              if (newGoldMatch) {
                const goldText = newGoldMatch[1];
                const goldNumber = goldText.match(/\d+/);
                if (goldNumber) {
                  gold = parseInt(goldNumber[0]);
                  updateGoldDisplay();
                }
              }

              return true;
            }
          }

          // 备用方案：处理 <zhujiemian> 标签内的信用点
          const zhujiemianRegex = /<zhujiemian>\s*信用点：(\d+)\s*<\/zhujiemian>/;
          const zhujiemianMatch = content.match(zhujiemianRegex);

          if (zhujiemianMatch) {
            const currentGold = parseInt(zhujiemianMatch[1]);
            const newGold = Math.max(0, currentGold - amount); // 确保信用点不会变成负数

            // 替换信用点数量
            content = content.replace(zhujiemianRegex, `<zhujiemian>\n信用点：${newGold}\n</zhujiemian>`);

            // 更新消息内容
            messages[0].message = content;
            setChatMessages(currentMsgId, messages);

            // 更新本地显示
            gold = newGold;
            updateGoldDisplay();

            return true;
          }
        }
        return false;
      }

      // 定期更新信用点显示
      setInterval(updateGoldFromST, 1000);
      function openShop() {
        var modal = document.getElementById('shopModal');
        modal.style.display = 'flex';
        renderShopItems();
        updateShopGold();
        renderCart();
      }
      function closeModal(id) {
        document.getElementById(id).style.display = 'none';
      }
      // 商品数据
      const shopItems = [
        // 食品与消耗品区
        {
          id: 'food1',
          name: '基础营养膏（香蕉味）',
          desc: '最基础的热量来源，呈半透明凝胶状，味道如同化学试剂混合了过期的香蕉香精。是底层劳工与军雌维持生命体征的必需品。',
          price: 5,
        },
        {
          id: 'food2',
          name: '“激励”牌高能压缩军粮',
          desc: '一块巴掌大的、坚硬如石的饼干，能提供一名军雌进行12小时高强度作战所需的热量。口感粗糙，但非常管用。',
          price: 30,
        },
        {
          id: 'food3',
          name: '三等星速食拉面（地狱辣味）',
          desc: '在底层民众中极受欢迎的垃圾食品，以其夸张的辣度和廉价的饱腹感著称。包装上印着一个喷火的卡通怪兽。',
          price: 15,
        },
        {
          id: 'food4',
          name: '“晨露”合成浆果',
          desc: '在无菌实验室里培育出的、完美模拟自然浆果口感的奢侈零食。每一颗都大小均匀，甜度精准，专供雄虫享用。',
          price: 250,
        },
        {
          id: 'food5',
          name: '活体蛋白块',
          desc: '从活体养殖罐中新鲜切割下来的高纯度蛋白质，保留着生物活性。口感鲜嫩，营养丰富，是雄虫餐桌上的常见菜肴。',
          price: 800,
        },
        {
          id: 'food6',
          name: '“蜂皇”特供糖果',
          desc: '据说配方源自失落的古代地球，用极其稀有的天然花蜜和一种发光昆虫的分泌物制成。有安抚精神力的奇效，是只有最高等级的雄虫才能享用的贡品。',
          price: 9800,
        },
        // 饮品与烟酒区
        {
          id: 'drink1',
          name: '净化再生水',
          desc: '将城市废水与空气中的湿气收集后，经过多重过滤循环制成的饮用水。无色无味，是帝国最普及的饮品。',
          price: 2,
        },
        {
          id: 'drink2',
          name: '“充能”能量饮料',
          desc: '带有强烈金属甜味的碳酸饮料，能迅速提神醒脑。是工程师、司机和学生党的伴侣，长期饮用会导致神经衰弱。',
          price: 25,
        },
        {
          id: 'drink3',
          name: '“静谧之泉”舒缓花草茶',
          desc: '采集自首都星温室花园的七种宁神花草制成，冲泡后会散发出柔和的白光。能有效缓解雄虫的精神力波动。',
          price: 450,
        },
        {
          id: 'drink4',
          name: '“星尘”牌合成酒精',
          desc: '用工业酒精和各种化学香料勾兑出的烈酒，口感辛辣，后劲极大。是边境星球和底层街区最受欢迎的麻醉剂。',
          price: 60,
        },
        // 日常用品与个虫护理区
        {
          id: 'daily1',
          name: '信息素强效抑制贴',
          desc: '每一个未婚军雌和进入发情期的雌虫都必须使用的东西。能有效阻断99%的信息素外泄，避免引起不必要的“麻烦”。',
          price: 120,
        },
        {
          id: 'daily2',
          name: '雄虫专用安抚香薰（海洋味）',
          desc: '一块雕刻精美的晶石，插入电源后会散发出模拟自然海洋气息的、带有安抚效果的信息素。能让雄虫的居所变得舒适宜虫。',
          price: 1800,
        },
        {
          id: 'daily3',
          name: '多功能强力清洁凝胶',
          desc: '能清洁从油污、血迹到机甲润滑油等各种污渍。味道刺鼻，是每个家庭主雌和维修工的必备品。',
          price: 45,
        },
        {
          id: 'daily4',
          name: '军雌专用伤痕遮瑕膏',
          desc: '遮盖力极强的膏体，能完美掩盖住战场上留下的狰狞伤疤，让雌虫的皮肤看起来更“干净”、“完整”，以取悦雄主。',
          price: 350,
        },
        {
          id: 'daily5',
          name: '便携式万用工具组',
          desc: '包含高斯螺丝刀、离子焊枪、分子钳等二十多种常用维修工具，是工程师的生命。',
          price: 2200,
        },
        // 娱乐与摆设区
        {
          id: 'ent1',
          name: '旧型号“角斗士”机甲模型（1:72）',
          desc: '已停产的经典机甲模型，做工精细，是军事爱好者和模型宅的收藏品。',
          price: 1500,
        },
        {
          id: 'ent2',
          name: '《帝国雄虫魅力宝典》',
          desc: '一本教导雌虫如何从穿着、谈吐、仪态等各方面提升自己，以获得雄虫青睐的畅销书。',
          price: 99,
        },
        {
          id: 'ent3',
          name: '会模仿雄主说话的电子仙人掌',
          desc: '一个毫无用处的桌面摆件，能录下雄主的声音并用滑稽的电音重复。在部分雌虫中很受欢迎。',
          price: 288,
        },
        {
          id: 'ent4',
          name: '仿古地球星象仪',
          desc: '用黄铜和水晶打造的、复刻古代地球星空的精美摆件。象征着品味与财富，是雄虫书房里的常见装饰。',
          price: 12000,
        },
        {
          id: 'ent5',
          name: '电子宠物石',
          desc: '一块光滑的、连接着USB口的石头，插上电后会微微发热。说明书上写着：“它爱你，只是不善表达。”',
          price: 198,
        },
        {
          id: 'ent6',
          name: '退役激光枪零件制成的烟灰缸',
          desc: '用报废的激光枪枪托和能量核心外壳改造而成，充满了粗犷的工业风，深受部分军迷喜爱。',
          price: 550,
        },
        // 科技与交通工具区
        {
          id: 'tech1',
          name: '通用数据存储芯片（512TB）',
          desc: '标准的数据存储介质，容量巨大，是信息时代的“纸张”。',
          price: 150,
        },
        {
          id: 'tech2',
          name: '便携式天气护盾发生器',
          desc: '一个巴掌大的圆盘，启动后能在半径三米内形成一个阻挡风雨的能量护盾。是外勤人员的福音。',
          price: 3800,
        },
        {
          id: 'tech3',
          name: '雄主心情探测手环（娱乐版）',
          desc: '一个噱头产品，声称能通过分析雄主的信息素浓度变化来显示其心情（笑脸/哭脸）。准确率极低，但销量不错。',
          price: 799,
        },
        {
          id: 'tech4',
          name: '公共磁力悬浮列车单程票',
          desc: '贯穿城市的主要交通工具，高效、准时、且异常拥挤。',
          price: 10,
        },
        {
          id: 'tech5',
          name: '“流萤”型单人悬浮滑板',
          desc: '年轻虫最爱的代步工具，速度快，操作灵活，但续航能力很差。',
          price: 4500,
        },
        {
          id: 'tech6',
          name: '“工蜂”牌二手运输飞船',
          desc: '帝国最常见的小型货运飞船，皮实耐用，但舒适度为零。是底层小商贩和拾荒者的最爱。',
          price: 280000,
        },
        // 特殊与定制服务区
        {
          id: 'spec1',
          name: '记忆影像定制服务',
          desc: '可以将客户指定的记忆片段（如与雄主的甜蜜时光）提取并制作成全息影像，供随时观赏。',
          price: 5000,
        },
        {
          id: 'spec2',
          name: '雄主荣誉勋章展示柜（黑胡桃木定制）',
          desc: '为尊贵的雄主量身定做的、带有恒温恒湿和安保系统的展示柜，用于陈列其家族或个人的荣誉勋章。',
          price: 150000,
        },
        {
          id: 'spec3',
          name: '“完美雌虫”基因优化咨询',
          desc: '一项昂贵的服务，为即将孕育后代的家庭提供基因筛查与优化建议，以确保能诞生出更健康、更“优秀”（通常指更符合雄虫审美）的后代。',
          price: 500000,
        },
        // 载具与机甲
        {
          id: 'vehicle1',
          name: '“幻影”反重力敞篷飞车',
          desc: '专为帝都的雄虫贵族设计，车身由稀有的月光石混合记忆金属打造，能根据主虫的心情变换颜色。敞篷设计，最高时速可达800公里，是夜晚在空中赛道上炫耀的绝佳选择。',
          price: 1200000,
        },
        {
          id: 'vehicle2',
          name: '全地形武装悬浮座驾“君主”',
          desc: '与其说是车，不如说是一个移动的、武装到牙齿的单虫王座。配备全景生态护盾、自动防御机枪塔和小型酒柜，能让雄主在最危险的蛮荒星球，也如同在自家后花园般安全惬意。',
          price: 2500000,
        },
        {
          id: 'vehicle3',
          name: '“彗星”级私人星际游艇',
          desc: '流线型的舰体，专为短途星际旅行设计。内部设有一间主卧、一间娱乐室和最多可容纳六名雌侍的次卧。拥有军用级的隐形涂层，是进行私密旅行的完美选择。',
          price: 7500000,
        },
        {
          id: 'vehicle4',
          name: '皇家御用级“帝王蝎”观赏性机甲',
          desc: '这并非军用武器，而是纯粹的身份象征。高十二米，外壳镀有无实际防御作用但极其美观的黑曜石合金，驾驶舱内部由手工缝制的稀有兽皮包裹。通常用于参加阅兵或在私人领地里进行“狩猎”游戏。',
          price: 15000000,
        },
        // 服饰与配件
        {
          id: 'dress1',
          name: '“星蛛丝”雄虫礼服',
          desc: '由帝国基因实验室培育的、只以彗星尘埃为食的“星光蛛”吐出的丝线织成。这种面料轻如无物，在光线下会呈现出如同银河般流动的璀璨光泽。',
          price: 850000,
        },
        {
          id: 'dress2',
          name: '记忆金属袖扣',
          desc: '一对看似普通的袖扣，可通过个人终端设定，变换成任意形状，甚至能在表面投射出家族的动态徽章或是爱人的影像。',
          price: 250000,
        },
        {
          id: 'dress3',
          name: '“龙瞳”义眼',
          desc: '纯粹为了美观而设计的赛博格义眼。瞳孔是模仿古代神话生物的金色竖瞳，在黑暗中会发出微光，可通过神经连接展现威压感。是追求个性化的年轻雄虫的最爱。',
          price: 980000,
        },
        {
          id: 'dress4',
          name: '信息素增幅怀表',
          desc: '一件制作精密的复古风格怀表，内部并非齿轮，而是一个微型信息素谐振放大器。能将雄主的信息素 subtly (巧妙地) 增强15%，让其在社交场合更具压倒性的魅力。',
          price: 1500000,
        },
        // 居家与生活
        {
          id: 'home1',
          name: '全息生态环境模拟室',
          desc: '在家中设置一间这样的房间，就可以通过程序，完美再现宇宙中任何一个有记录的星球的环境。无论是失落的地球上那片蔚蓝的沙滩，还是异星上漂浮着发光水母的雨林，都能让你身临其境。',
          price: 4000000,
        },
        {
          id: 'home2',
          name: '“月光兽”宠物',
          desc: '基因工程的杰作。这种猫科大小的哺乳动物，浑身长着雪白柔软的长毛，血液中含有发光蛋白，夜晚会散发出柔和的、如同月光般的银辉。它被设计成绝对忠诚、无需训练、且叫声频率能舒缓精神力的完美宠物。',
          price: 600000,
        },
        {
          id: 'home3',
          name: '悬浮式休眠舱',
          desc: '通过强大的反重力磁场，让使用者悬浮在充满营养液和镇静剂的舱体内。能隔绝一切噪音、光线和重力，保证8小时的睡眠质量等同于普通虫24小时的深度休息。',
          price: 1800000,
        },
        {
          id: 'home4',
          name: '古文明战舰残骸雕塑',
          desc: '从已被帝国征服的、某个古代文明的战舰坟场里，切割下的一块真实残骸，经过艺术化处理后，制成的巨型雕塑。每一件都独一无二，是彰显征服历史与家族底蕴的顶级藏品。',
          price: 5500000,
        },
        // 食品与消耗品补充
        {
          id: 'food7',
          name: '“创世纪”基因重组鱼子酱',
          desc: '一种只存在于实验室中的、经过上千次基因序列重组才诞生出的深海生物的卵。据说其味道能直接刺激大脑的愉悦中枢，带来无与伦比的味觉体验。',
          price: 120000,
        },
        {
          id: 'food8',
          name: '百年陈酿“恒星之火”',
          desc: '一种传奇的合成白兰地。将原液密封在特制的水晶桶中，送入近中子星轨道，利用其强大的引力和辐射进行长达一百年的“催陈”。口感如同吞下了一颗燃烧的恒星。',
          price: 750000,
        },
        {
          id: 'other1',
          name: '无主小行星命名权',
          desc: '帝国会定期拍卖一些新发现的、无战略价值的荒芜小行星的永久命名权。将自己的名字镌刻于星海之上，是比任何珠宝华服都更极致的浪漫与炫耀。',
          price: 8800000,
        },
      ];
      function renderShopItems() {
        const shopItemsContainer = document.getElementById('shopItems');
        shopItemsContainer.innerHTML = '';
        // 商品分区定义
        const sections = [
          {
            title: '食品与消耗品区',
            ids: ['food1', 'food2', 'food3', 'food4', 'food5', 'food6'],
          },
          {
            title: '饮品与烟酒区',
            ids: ['drink1', 'drink2', 'drink3', 'drink4'],
          },
          {
            title: '日常用品与个虫护理区',
            ids: ['daily1', 'daily2', 'daily3', 'daily4', 'daily5'],
          },
          {
            title: '娱乐与摆设区',
            ids: ['ent1', 'ent2', 'ent3', 'ent4', 'ent5', 'ent6'],
          },
          {
            title: '科技与交通工具区',
            ids: ['tech1', 'tech2', 'tech3', 'tech4', 'tech5', 'tech6'],
          },
          {
            title: '特殊与定制服务区',
            ids: ['spec1', 'spec2', 'spec3'],
          },
          { title: '载具与机甲', ids: ['vehicle1', 'vehicle2', 'vehicle3', 'vehicle4'] },
          { title: '服饰与配件', ids: ['dress1', 'dress2', 'dress3', 'dress4'] },
          { title: '居家与生活', ids: ['home1', 'home2', 'home3', 'home4'] },
          { title: '食品与消耗品补充', ids: ['food7', 'food8'] },
          { title: '其他', ids: ['other1'] },
        ];
        sections.forEach(section => {
          const sectionDiv = document.createElement('div');
          sectionDiv.style.marginBottom = '18px';
          sectionDiv.innerHTML = `<div style="font-weight:700;font-size:16px;color:#a48ad4;margin-bottom:8px;">${section.title}</div>`;
          section.ids.forEach(id => {
            const item = shopItems.find(i => i.id === id);
            if (!item) return;
            const itemDiv = document.createElement('div');
            itemDiv.className = 'shop-item';
            itemDiv.innerHTML = `
              <div class="shop-item-info">
                <div class="shop-item-name">${item.name}</div>
                <div class="shop-item-desc">${item.desc}</div>
                <div class="shop-item-price">${item.price} 信用点</div>
              </div>
              <button class="add-to-cart-btn" onclick="addToCart('${item.id}')">加入购物车</button>
            `;
            sectionDiv.appendChild(itemDiv);
          });
          shopItemsContainer.appendChild(sectionDiv);
        });
      }
      function updateShopGold() {
        document.getElementById('shopCurrentGold').textContent = `$${gold}`;
        updateGoldDisplay();
      }
      function addToCart(itemId) {
        const item = shopItems.find(i => i.id === itemId);
        if (!item) return;

        // 检查信用点是否足够
        const cartTotal = calculateCartTotal();
        if (cartTotal + item.price > gold) {
          alert('信用点不足！');
          return;
        }

        const existingItem = shoppingCart.find(cartItem => cartItem.id === itemId);
        if (existingItem) {
          existingItem.quantity += 1;
        } else {
          shoppingCart.push({ id: itemId, name: item.name, price: item.price, quantity: 1 });
        }
        renderCart();
      }
      function removeFromCart(itemId) {
        const itemIndex = shoppingCart.findIndex(item => item.id === itemId);
        if (itemIndex === -1) return;
        if (shoppingCart[itemIndex].quantity > 1) {
          shoppingCart[itemIndex].quantity -= 1;
        } else {
          shoppingCart.splice(itemIndex, 1);
        }
        renderCart();
      }
      function renderCart() {
        const cartItemsContainer = document.getElementById('cartItems');
        cartItemsContainer.innerHTML = '';
        if (shoppingCart.length === 0) {
          cartItemsContainer.innerHTML = '<div class="cart-empty">购物车为空</div>';
        } else {
          shoppingCart.forEach(item => {
            const cartItemDiv = document.createElement('div');
            cartItemDiv.className = 'cart-item';
            cartItemDiv.innerHTML = `
              <div class="cart-item-info">
                <div class="cart-item-name">${item.name}</div>
                <div class="cart-item-quantity">数量: ${item.quantity} | 单价: ${item.price}信用点</div>
              </div>
              <button class="cart-item-remove" onclick="removeFromCart('${item.id}')">移除</button>
            `;
            cartItemsContainer.appendChild(cartItemDiv);
          });
        }
        const total = calculateCartTotal();
        document.getElementById('cartTotal').textContent = total;
        document.getElementById('checkoutBtn').disabled = shoppingCart.length === 0;
      }
      function calculateCartTotal() {
        return shoppingCart.reduce((total, item) => total + item.price * item.quantity, 0);
      }
      function checkoutCart() {
        if (shoppingCart.length === 0) return;

        const total = calculateCartTotal();
        if (total > gold) {
          alert('信用点不足！');
          return;
        }

        const itemNames = shoppingCart
          .map(item => (item.quantity > 1 ? `${item.name} ×${item.quantity}` : item.name))
          .join('、');

        // 尝试同层减少信用点
        const goldReduced = reduceGoldInSameLayer(total);

        // 发送消息到SillyTavern
        if (typeof triggerSlash === 'function') {
          // 发送购买信息
          triggerSlash(`/send 花费${total}信用点，购买了${itemNames}|/trigger`);
        } else {
          alert(`花费${total}信用点，购买了${itemNames}`);
        }

        // 如果没有成功同层减少，则更新本地显示（等待AI更新）
        if (!goldReduced) {
          gold -= total;
          updateShopGold();
        }

        // 清空购物车
        shoppingCart = [];
        renderCart();

        // 关闭商城模态框
        closeModal('shopModal');
      }
      // 初始化页面
      document.addEventListener('DOMContentLoaded', function () {
        updateGoldDisplay();
        updateGoldFromST(); // 尝试从SillyTavern获取信用点
      });
      function openBilibili() {
        // 显示哔哩哔哩访问选择界面
        const bilibiliModal = document.createElement('div');
        bilibiliModal.id = 'bilibiliModal';
        bilibiliModal.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          background: linear-gradient(135deg, #00a1d6, #fb7299, #ff6699);
          z-index: 3000;
          display: flex;
          align-items: center;
          justify-content: center;
        `;

        bilibiliModal.innerHTML = `
          <div style="
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            color: white;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
          ">
            <div style="font-size: 48px; margin-bottom: 20px;">📺</div>
            <div style="font-size: 28px; font-weight: bold; margin-bottom: 10px;">哔哩哔哩访问方式</div>
            <div style="font-size: 16px; opacity: 0.8; margin-bottom: 40px;">选择您偏好的访问方式</div>

            <div style="display: flex; flex-direction: column; gap: 15px;">
              <button onclick="openBilibiliInIframe()" style="
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 20px;
                color: white;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 16px;
                width: 100%;
              " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'">
                <div style="font-size: 24px; margin-bottom: 8px;">📱</div>
                <div style="font-weight: bold; margin-bottom: 5px;">内嵌访问</div>
                <div style="font-size: 14px; opacity: 0.8;">在当前页面中打开哔哩哔哩</div>
              </button>

              <button onclick="window.open('https://www.bilibili.com/', '_blank')" style="
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 20px;
                color: white;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 16px;
                width: 100%;
              " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'">
                <div style="font-size: 24px; margin-bottom: 8px;">🔗</div>
                <div style="font-weight: bold; margin-bottom: 5px;">新标签页打开</div>
                <div style="font-size: 14px; opacity: 0.8;">在新标签页中打开哔哩哔哩官网</div>
              </button>

              <button onclick="window.open('https://m.bilibili.com/', '_blank')" style="
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 20px;
                color: white;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 16px;
                width: 100%;
              " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'">
                <div style="font-size: 24px; margin-bottom: 8px;">📲</div>
                <div style="font-weight: bold; margin-bottom: 5px;">移动版本</div>
                <div style="font-size: 14px; opacity: 0.8;">打开哔哩哔哩移动版网页</div>
              </button>
            </div>

            <div style="
              background: rgba(0, 161, 214, 0.2);
              border: 1px solid rgba(0, 161, 214, 0.5);
              border-radius: 10px;
              padding: 15px;
              margin-top: 30px;
              font-size: 14px;
              line-height: 1.5;
            ">
              <strong>💡 提示：</strong><br>
              哔哩哔哩对iframe嵌入相对友好，内嵌访问通常可以正常工作。
              如果遇到问题，可以尝试新标签页打开。
            </div>

            <button onclick="closeBilibiliModal()" style="
              background: rgba(255, 255, 255, 0.2);
              border: none;
              color: white;
              padding: 12px 24px;
              border-radius: 25px;
              font-size: 16px;
              cursor: pointer;
              margin-top: 20px;
              transition: all 0.3s ease;
            " onmouseover="this.style.background='rgba(255, 255, 255, 0.3)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'">
              返回
            </button>
          </div>
        `;

        document.body.appendChild(bilibiliModal);

        // 添加键盘事件监听（ESC键关闭）
        const handleKeyPress = function (event) {
          if (event.key === 'Escape') {
            closeBilibiliModal();
            document.removeEventListener('keydown', handleKeyPress);
          }
        };
        document.addEventListener('keydown', handleKeyPress);
      }
      // 内嵌方式打开哔哩哔哩
      function openBilibiliInIframe() {
        // 先关闭选择界面
        closeBilibiliModal();

        // 创建iframe界面，框在手机框内
        const iframeModal = document.createElement('div');
        iframeModal.id = 'bilibiliIframeModal';
        iframeModal.className = 'modal';
        iframeModal.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 3000;
        `;

        iframeModal.innerHTML = `
          <div style="
            width: 360px;
            height: 640px;
            border: 8px solid #00a1d6;
            border-radius: 36px;
            background: #000;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 30px rgba(0, 161, 214, 0.5), 0 8px 32px rgba(0, 161, 214, 0.3);
          ">
            <!-- 手机刘海 -->
            <div style="
              position: absolute;
              top: 0;
              left: 50%;
              transform: translateX(-50%);
              width: 120px;
              height: 12px;
              background: #00a1d6;
              border-bottom-left-radius: 10px;
              border-bottom-right-radius: 10px;
              z-index: 999;
              display: flex;
              justify-content: center;
              align-items: center;
              gap: 6px;
            ">
              <div style="width: 7px; height: 7px; background: #2c3e50; border-radius: 50%; border: 1px solid #34495e;"></div>
              <div style="width: 35px; height: 3px; background: #2c3e50; border-radius: 2px;"></div>
              <div style="width: 5px; height: 5px; background: #2c3e50; border-radius: 50%; border: 1px solid #34495e;"></div>
            </div>

            <!-- 顶部状态栏 -->
            <div style="
              height: 32px;
              background: linear-gradient(135deg, #00a1d6, #fb7299);
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 0 18px;
              color: white;
              font-size: 13px;
              z-index: 1000;
              position: relative;
            ">
              <div style="display: flex; align-items: center; gap: 10px;">
                <button onclick="closeBilibiliIframe()" style="
                  background: none;
                  border: none;
                  color: white;
                  font-size: 16px;
                  cursor: pointer;
                  padding: 2px;
                ">←</button>
                <span style="font-size: 12px;">哔哩哔哩</span>
              </div>
              <div style="display: flex; gap: 8px; align-items: center;">
                <button onclick="refreshBilibiliIframe()" style="
                  background: none;
                  border: none;
                  color: white;
                  font-size: 12px;
                  cursor: pointer;
                  padding: 2px;
                ">🔄</button>
                <button onclick="window.open('https://www.bilibili.com/', '_blank')" style="
                  background: none;
                  border: none;
                  color: white;
                  font-size: 12px;
                  cursor: pointer;
                  padding: 2px;
                ">🔗</button>
                <span style="font-size: 11px;">📶 🔋 100%</span>
              </div>
            </div>

            <!-- iframe内容区域 -->
            <iframe
              id="bilibiliFrame"
              src="https://m.bilibili.com/"
              style="
                width: 100%;
                height: calc(100% - 32px);
                border: none;
                background: white;
                border-radius: 0 0 28px 28px;
              "
              allow="camera; microphone; autoplay; encrypted-media; fullscreen"
              allowfullscreen
              sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-top-navigation"
            ></iframe>
          </div>
        `;

        document.body.appendChild(iframeModal);

        // 添加加载提示
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'bilibiliLoading';
        loadingDiv.style.cssText = `
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: #00a1d6;
          font-size: 18px;
          text-align: center;
          z-index: 3001;
          background: rgba(255, 255, 255, 0.9);
          padding: 20px;
          border-radius: 15px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        `;
        loadingDiv.innerHTML = `
          <div style="margin-bottom: 15px; font-weight: bold;">正在加载哔哩哔哩...</div>
          <div style="font-size: 12px; color: #666;">
            使用移动版以获得更好的体验
          </div>
        `;
        iframeModal.appendChild(loadingDiv);

        // 监听iframe加载完成
        const iframe = document.getElementById('bilibiliFrame');
        iframe.onload = function () {
          const loading = document.getElementById('bilibiliLoading');
          if (loading) {
            loading.remove();
          }
        };

        // 如果8秒后还在加载，显示提示
        setTimeout(() => {
          const loading = document.getElementById('bilibiliLoading');
          if (loading) {
            loading.innerHTML = `
              <div style="margin-bottom: 15px; font-weight: bold;">加载时间较长</div>
              <div style="font-size: 12px; color: #666; margin-bottom: 15px;">
                可能是网络问题，您可以尝试刷新或在新标签页中打开
              </div>
              <div style="display: flex; flex-direction: column; gap: 8px;">
                <button onclick="window.open('https://www.bilibili.com/', '_blank')" style="
                  background: linear-gradient(135deg, #00a1d6, #fb7299);
                  color: white;
                  border: none;
                  padding: 8px 16px;
                  border-radius: 15px;
                  font-size: 12px;
                  cursor: pointer;
                ">在新标签页打开</button>
                <button onclick="closeBilibiliIframe()" style="
                  background: #f0f0f0;
                  color: #666;
                  border: none;
                  padding: 8px 16px;
                  border-radius: 15px;
                  font-size: 12px;
                  cursor: pointer;
                ">返回</button>
              </div>
            `;
          }
        }, 8000);

        // ESC键关闭
        const handleEsc = e => {
          if (e.key === 'Escape') {
            closeBilibiliIframe();
            document.removeEventListener('keydown', handleEsc);
          }
        };
        document.addEventListener('keydown', handleEsc);
      }

      // 关闭哔哩哔哩iframe
      function closeBilibiliIframe() {
        const modal = document.getElementById('bilibiliIframeModal');
        if (modal) {
          modal.remove();
        }
      }

      // 刷新哔哩哔哩iframe
      function refreshBilibiliIframe() {
        const iframe = document.getElementById('bilibiliFrame');
        if (iframe) {
          iframe.src = iframe.src;
        }
      }

      // 关闭哔哩哔哩模态框
      function closeBilibiliModal() {
        const modal = document.getElementById('bilibiliModal');
        if (modal) {
          modal.remove();
        }
      }

      function openWechat() {
        alert('微信功能暂时使用快速回复');
      }
      // 点击modal外部关闭
      window.onclick = function (event) {
        var modal = document.getElementById('shopModal');
        if (event.target === modal) {
          modal.style.display = 'none';
        }
      };
    </script>
  </body>
</html>
