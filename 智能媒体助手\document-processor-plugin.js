/**
 * 文档处理专用插件 - SillyTavern Extension
 * 专门处理txt、json等文档文件，避免与图片处理冲突
 * 作者: kencuo
 * 版本: 1.0.0
 */

// 插件元数据
const DOCUMENT_PLUGIN_ID = 'document-processor-kencuo';
const DOCUMENT_MODULE_NAME = 'document-processor';

// 支持的文档格式配置
const DOCUMENT_CONFIG = {
  supportedTypes: [
    'text/plain',
    'application/json',
    'text/markdown',
    'text/csv',
    'text/html',
    'text/xml',
    'application/xml',
    'text/javascript',
    'application/javascript',
    'text/css',
    'application/rtf'
  ],
  supportedExtensions: [
    'txt', 'json', 'md', 'csv', 'html', 'xml', 'js', 'css', 'rtf',
    'log', 'conf', 'config', 'ini', 'yaml', 'yml'
  ],
  maxFileSize: 10 * 1024 * 1024, // 10MB
  encoding: 'UTF-8'
};

/**
 * 文档类型检测器
 */
class DocumentTypeDetector {
  static isDocument(file) {
    if (!file || !file.name) return false;
    
    const fileType = file.type || '';
    const fileName = file.name || '';
    const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
    
    // 检查MIME类型
    const isSupportedType = DOCUMENT_CONFIG.supportedTypes.includes(fileType) ||
                           fileType.startsWith('text/') ||
                           fileType.includes('json') ||
                           fileType.includes('xml');
    
    // 检查文件扩展名
    const isSupportedExtension = DOCUMENT_CONFIG.supportedExtensions.includes(fileExtension);
    
    // 排除图片文件
    const isNotImage = !fileType.startsWith('image/') && 
                      !['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(fileExtension);
    
    return (isSupportedType || isSupportedExtension) && isNotImage;
  }
  
  static getDocumentType(file) {
    const fileType = file.type || '';
    const fileName = file.name || '';
    const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
    
    // 根据扩展名确定文档类型
    switch (fileExtension) {
      case 'json':
        return 'json';
      case 'md':
        return 'markdown';
      case 'html':
      case 'htm':
        return 'html';
      case 'xml':
        return 'xml';
      case 'csv':
        return 'csv';
      case 'js':
        return 'javascript';
      case 'css':
        return 'css';
      case 'yaml':
      case 'yml':
        return 'yaml';
      case 'log':
        return 'log';
      default:
        return 'text';
    }
  }
}

/**
 * 文档内容处理器
 */
class DocumentContentProcessor {
  static async processContent(file, documentType) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = function(e) {
        try {
          let content = e.target.result;
          
          // 根据文档类型进行特殊处理
          switch (documentType) {
            case 'json':
              content = DocumentContentProcessor.formatJSON(content);
              break;
            case 'xml':
              content = DocumentContentProcessor.formatXML(content);
              break;
            case 'csv':
              content = DocumentContentProcessor.formatCSV(content);
              break;
            default:
              // 其他类型保持原样
              break;
          }
          
          resolve(content);
        } catch (error) {
          reject(new Error(`文档内容处理失败: ${error.message}`));
        }
      };
      
      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsText(file, DOCUMENT_CONFIG.encoding);
    });
  }
  
  static formatJSON(content) {
    try {
      const jsonObj = JSON.parse(content);
      return JSON.stringify(jsonObj, null, 2);
    } catch (error) {
      console.warn('[Document Processor] JSON格式化失败，返回原始内容');
      return content;
    }
  }
  
  static formatXML(content) {
    // 简单的XML格式化（可以根据需要扩展）
    try {
      // 移除多余的空白字符
      return content.replace(/>\s+</g, '><').trim();
    } catch (error) {
      return content;
    }
  }
  
  static formatCSV(content) {
    // CSV内容预处理
    try {
      const lines = content.split('\n');
      const maxPreviewLines = 50; // 只显示前50行
      
      if (lines.length > maxPreviewLines) {
        const previewLines = lines.slice(0, maxPreviewLines);
        return previewLines.join('\n') + `\n\n... (文件共${lines.length}行，仅显示前${maxPreviewLines}行)`;
      }
      
      return content;
    } catch (error) {
      return content;
    }
  }
}

/**
 * 文档验证器
 */
class DocumentValidator {
  static validate(file) {
    if (!file || typeof file !== 'object') {
      throw new Error('无效的文件对象');
    }
    
    if (!DocumentTypeDetector.isDocument(file)) {
      throw new Error(`不支持的文档类型: ${file.type || '未知'} (${file.name})`);
    }
    
    if (file.size > DOCUMENT_CONFIG.maxFileSize) {
      const maxSizeMB = Math.round(DOCUMENT_CONFIG.maxFileSize / 1024 / 1024);
      throw new Error(`文件过大，限制: ${maxSizeMB}MB`);
    }
    
    return true;
  }
}

/**
 * 主要的文档处理函数
 */
async function processDocument(file, options = {}) {
  try {
    console.log('[Document Processor] 开始处理文档:', {
      name: file.name,
      type: file.type,
      size: file.size
    });
    
    // 验证文件
    DocumentValidator.validate(file);
    
    // 检测文档类型
    const documentType = DocumentTypeDetector.getDocumentType(file);
    console.log('[Document Processor] 文档类型:', documentType);
    
    // 处理文档内容
    const content = await DocumentContentProcessor.processContent(file, documentType);
    
    // 生成文件信息
    const fileInfo = {
      originalName: file.name,
      type: file.type || 'text/plain',
      size: file.size,
      documentType: documentType,
      contentLength: content.length,
      timestamp: new Date().toISOString()
    };
    
    console.log('[Document Processor] 文档处理完成:', fileInfo);
    
    return {
      success: true,
      content: content,
      metadata: fileInfo,
      documentType: documentType
    };
    
  } catch (error) {
    console.error('[Document Processor] 处理失败:', error);
    throw error;
  }
}

/**
 * 发送文档到聊天
 */
async function sendDocumentToChat(content, fileName, documentType) {
  try {
    // 获取SillyTavern的聊天函数
    const addOneMessage = typeof window.addOneMessage === 'function' ? window.addOneMessage :
                         typeof parent.addOneMessage === 'function' ? parent.addOneMessage :
                         typeof top.addOneMessage === 'function' ? top.addOneMessage : null;
    
    if (addOneMessage) {
      // 限制显示长度
      const maxLength = 2000;
      const displayContent = content.length > maxLength ? 
        content.substring(0, maxLength) + '\n\n...(内容已截断)' : content;
      
      // 根据文档类型选择图标
      const typeIcons = {
        json: '📋',
        markdown: '📝',
        html: '🌐',
        xml: '📄',
        csv: '📊',
        javascript: '⚡',
        css: '🎨',
        yaml: '⚙️',
        log: '📜',
        text: '📄'
      };
      
      const icon = typeIcons[documentType] || '📄';
      const messageContent = `${icon} **文档内容** (${fileName})\n\n\`\`\`${documentType}\n${displayContent}\n\`\`\``;
      
      await addOneMessage({
        name: 'User',
        is_user: true,
        is_system: false,
        send_date: new Date().toISOString(),
        mes: messageContent,
        extra: {
          type: 'document_upload',
          file_name: fileName,
          document_type: documentType,
          processed_by: 'document_processor_plugin'
        }
      });
      
      console.log('[Document Processor] 文档已发送到聊天');
    } else {
      console.warn('[Document Processor] 无法找到聊天发送函数');
    }
  } catch (error) {
    console.error('[Document Processor] 发送文档失败:', error);
  }
}

// 导出主要接口
window.__processDocumentOnly = processDocument;
window.__sendDocumentToChat = sendDocumentToChat;
window.__isDocumentFile = DocumentTypeDetector.isDocument;

// 兼容性接口
window.__processDocumentByDocumentPlugin = async function(file, options = {}) {
  try {
    const result = await processDocument(file, options);
    
    // 如果需要发送到聊天
    if (options.sendToChat !== false) {
      await sendDocumentToChat(result.content, result.metadata.originalName, result.documentType);
    }
    
    return result;
  } catch (error) {
    if (typeof toastr !== 'undefined') {
      toastr.error(`文档处理失败: ${error.message}`, '文档处理');
    }
    throw error;
  }
};

console.log('[Document Processor Plugin] 文档处理插件已加载');
