# 识图功能逻辑说明

## 整体流程

### 1. 图片上传阶段
```
用户选择图片 → 转换为base64/上传到服务器 → 显示在聊天界面 → 标记需要识图
```

### 2. 识图处理阶段（用户点击左上角按钮后）
```
用户点击AI回复按钮 → 检查待识图图片 → 调用识图API → 获取图片描述 → 继续AI对话
```

## 详细识图逻辑

### 方案A：使用配置的识图API（推荐）
```javascript
// 1. 调用外部识图API（如OpenAI GPT-4V、硅基流动等）
const visionMessages = [
  {
    role: 'user',
    content: [
      {
        type: 'text',
        text: '请详细描述这张图片的内容...'
      },
      {
        type: 'image_url',
        image_url: {
          url: imageData  // base64格式的图片数据
        }
      }
    ]
  }
];

// 2. 发送到识图API
const response = await fetch(`${visionApiUrl}/chat/completions`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${visionApiKey}`
  },
  body: JSON.stringify({
    model: visionModel,  // 如 gpt-4-vision-preview
    messages: visionMessages,
    max_tokens: 800,
    temperature: 0.3
  })
});

// 3. 获取图片描述
const imageDescription = response.choices[0].message.content;
```

### 方案B：使用酒馆内置AI（备用）
```javascript
// 如果没有配置识图API，使用酒馆的AI进行分析
const visionPrompt = `请分析这张图片，描述你看到的内容。`;

// 调用酒馆的generate函数
const response = await AI_GENERATE({
  injects: [
    { 
      role: 'system', 
      content: visionPrompt, 
      position: 'in_chat', 
      depth: 0, 
      should_scan: true 
    }
  ],
  should_stream: false,
  image: imageData  // 图片数据
});
```

## 数据流向

### 完整的数据流程：
```
1. 用户上传图片
   ↓
2. 图片转换为base64格式存储在消息历史中
   ↓
3. 用户点击AI回复按钮
   ↓
4. 系统检查是否有待识图的图片
   ↓
5. 调用识图API分析图片内容
   ↓
6. 获取图片描述文本
   ↓
7. 构建包含图片描述的消息发送给角色AI
   ↓
8. 角色AI基于图片描述生成回复
```

### 发送给角色AI的消息格式：
```javascript
// 如果用户有图片描述
const messageForAI = `用户发送了一张图片并说："${userDescription}"\n\n图片内容描述：${aiImageDescription}`;

// 如果用户没有图片描述
const messageForAI = `用户发送了一张图片。\n\n图片内容描述：${aiImageDescription}`;
```

## 两种识图方案的区别

### 方案A：外部识图API
- **优点**：
  - 识图精度高（专业视觉模型）
  - 支持多种图片格式
  - 可以识别文字、物体、场景等
  - 不占用酒馆的token配额

- **缺点**：
  - 需要额外配置API
  - 可能产生费用
  - 依赖网络连接

### 方案B：酒馆内置AI
- **优点**：
  - 无需额外配置
  - 使用现有的AI模型
  - 免费（使用酒馆配额）

- **缺点**：
  - 识图能力有限
  - 占用对话token
  - 可能不支持所有图片格式

## 用户体验优化

### 修改前的问题：
- 图片上传后立即触发AI回复
- 用户无法控制何时开始识图
- 可能打断用户的操作流程

### 修改后的改进：
- 图片上传后不自动触发AI回复
- 显示"🤖 AI识图中..."状态指示器
- 用户主动点击左上角按钮时才开始识图+回复
- 识图完成后显示"✅ 已识图"状态

### 状态指示器说明：
- `🤖 AI识图中...` - 正在调用识图API分析图片
- `✅ 已识图` - 识图完成，可以进行AI对话
- `❌ 识图失败` - 识图API调用失败，会回退到备用方案

## 配置说明

### 支持的识图API：
1. **OpenAI GPT-4 Vision**
   - API地址：`https://api.openai.com/v1`
   - 模型：`gpt-4-vision-preview`, `gpt-4o` 等

2. **硅基流动**
   - API地址：`https://api.siliconflow.cn/v1`
   - 模型：`Pro/OpenAI/GPT-4o`, `Pro/Qwen/Qwen2-VL-72B-Instruct` 等

3. **其他兼容OpenAI格式的API**
   - 只要支持 `/chat/completions` 接口和 `image_url` 格式

### 模型自动识别：
系统会自动识别包含以下关键词的模型作为视觉模型：
- `vision`, `gpt-4`, `claude`, `gemini`
- `qwen-vl`, `qwen2-vl`, `internvl`, `llava`
- `minicpm`, `yi-vision`, `cogvlm`
- `-v`, `visual`, `multimodal`

## 总结

识图功能的核心逻辑是：
1. **识图模型**负责理解图片内容，生成文字描述
2. **角色AI模型**负责基于图片描述进行角色扮演和对话
3. 两个模型分工合作，识图模型专注视觉理解，角色AI专注对话生成
4. 用户可以控制何时开始识图和AI回复，提供更好的交互体验

这样的设计既保证了识图的准确性，又保持了角色AI的对话质量。
