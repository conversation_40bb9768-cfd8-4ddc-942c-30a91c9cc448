<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档处理功能修复测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-section h2 {
            color: #555;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .file-input {
            margin: 15px 0;
            padding: 10px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-input:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        
        .file-input input {
            display: none;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            background: #fff;
            border-left: 4px solid #667eea;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success {
            border-left-color: #4CAF50;
            background: #f1f8e9;
        }
        
        .error {
            border-left-color: #f44336;
            background: #ffebee;
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status.loading {
            background: #2196F3;
            color: white;
        }
        
        .status.success {
            background: #4CAF50;
            color: white;
        }
        
        .status.error {
            background: #f44336;
            color: white;
        }
        
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        
        .plugin-status {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .plugin-item {
            padding: 10px 15px;
            border-radius: 8px;
            font-weight: bold;
            flex: 1;
            min-width: 200px;
            text-align: center;
        }
        
        .plugin-loaded {
            background: #c8e6c9;
            color: #2e7d32;
        }
        
        .plugin-missing {
            background: #ffcdd2;
            color: #c62828;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 文档处理功能修复测试</h1>
        
        <!-- 插件状态检查 -->
        <div class="test-section">
            <h2>🔧 插件状态检查</h2>
            <div class="plugin-status" id="pluginStatus">
                <div class="plugin-item" id="mainPlugin">主插件: 检查中...</div>
                <div class="plugin-item" id="docPlugin">文档插件: 检查中...</div>
                <div class="plugin-item" id="imagePlugin">图片插件: 检查中...</div>
            </div>
            <button class="btn" onclick="checkPluginStatus()">重新检查插件状态</button>
        </div>
        
        <!-- 文档文件测试 -->
        <div class="test-section">
            <h2>📄 文档文件处理测试</h2>
            <p>测试txt、json等文档文件的处理功能</p>
            
            <div class="file-input" onclick="document.getElementById('docFile').click()">
                <input type="file" id="docFile" accept=".txt,.json,.md,.csv,.html,.xml,.js,.css,.log" onchange="handleDocumentFile(this)">
                <p>📁 点击选择文档文件 (txt, json, md, csv, html, xml, js, css, log)</p>
            </div>
            
            <button class="btn" onclick="testDocumentProcessing()" id="testDocBtn" disabled>测试文档处理</button>
            <button class="btn" onclick="createTestFiles()">创建测试文件</button>
            
            <div class="result" id="docResult" style="display: none;"></div>
        </div>
        
        <!-- 图片文件测试 -->
        <div class="test-section">
            <h2>🖼️ 图片文件处理测试</h2>
            <p>确保图片文件仍然正常处理</p>
            
            <div class="file-input" onclick="document.getElementById('imgFile').click()">
                <input type="file" id="imgFile" accept="image/*" onchange="handleImageFile(this)">
                <p>🖼️ 点击选择图片文件 (jpg, png, gif, webp)</p>
            </div>
            
            <button class="btn" onclick="testImageProcessing()" id="testImgBtn" disabled>测试图片处理</button>
            
            <div class="result" id="imgResult" style="display: none;"></div>
        </div>
        
        <!-- 混合文件测试 -->
        <div class="test-section">
            <h2>🔄 文件类型识别测试</h2>
            <p>测试系统是否能正确识别不同类型的文件</p>
            
            <div class="file-input" onclick="document.getElementById('mixFile').click()">
                <input type="file" id="mixFile" onchange="handleMixedFile(this)">
                <p>📂 选择任意文件测试类型识别</p>
            </div>
            
            <button class="btn" onclick="testFileTypeDetection()" id="testMixBtn" disabled>测试类型识别</button>
            
            <div class="result" id="mixResult" style="display: none;"></div>
        </div>
    </div>

    <script>
        let selectedDocFile = null;
        let selectedImgFile = null;
        let selectedMixFile = null;
        
        // 检查插件状态
        function checkPluginStatus() {
            const mainPlugin = document.getElementById('mainPlugin');
            const docPlugin = document.getElementById('docPlugin');
            const imagePlugin = document.getElementById('imagePlugin');
            
            // 检查主插件
            if (typeof window.__processFileByPlugin === 'function') {
                mainPlugin.textContent = '主插件: ✅ 已加载';
                mainPlugin.className = 'plugin-item plugin-loaded';
            } else {
                mainPlugin.textContent = '主插件: ❌ 未加载';
                mainPlugin.className = 'plugin-item plugin-missing';
            }
            
            // 检查文档插件
            if (typeof window.__processDocumentByDocumentPlugin === 'function') {
                docPlugin.textContent = '文档插件: ✅ 已加载';
                docPlugin.className = 'plugin-item plugin-loaded';
            } else {
                docPlugin.textContent = '文档插件: ❌ 未加载';
                docPlugin.className = 'plugin-item plugin-missing';
            }
            
            // 检查图片插件
            if (typeof window.__uploadImageByPlugin === 'function') {
                imagePlugin.textContent = '图片插件: ✅ 已加载';
                imagePlugin.className = 'plugin-item plugin-loaded';
            } else {
                imagePlugin.textContent = '图片插件: ❌ 未加载';
                imagePlugin.className = 'plugin-item plugin-missing';
            }
        }
        
        // 处理文档文件选择
        function handleDocumentFile(input) {
            const file = input.files[0];
            if (file) {
                selectedDocFile = file;
                document.getElementById('testDocBtn').disabled = false;
                
                const fileInfo = `已选择: ${file.name} (${file.type || '未知类型'}, ${(file.size/1024).toFixed(1)} KB)`;
                input.parentElement.querySelector('p').textContent = fileInfo;
            }
        }
        
        // 处理图片文件选择
        function handleImageFile(input) {
            const file = input.files[0];
            if (file) {
                selectedImgFile = file;
                document.getElementById('testImgBtn').disabled = false;
                
                const fileInfo = `已选择: ${file.name} (${file.type}, ${(file.size/1024).toFixed(1)} KB)`;
                input.parentElement.querySelector('p').textContent = fileInfo;
            }
        }
        
        // 处理混合文件选择
        function handleMixedFile(input) {
            const file = input.files[0];
            if (file) {
                selectedMixFile = file;
                document.getElementById('testMixBtn').disabled = false;
                
                const fileInfo = `已选择: ${file.name} (${file.type || '未知类型'}, ${(file.size/1024).toFixed(1)} KB)`;
                input.parentElement.querySelector('p').textContent = fileInfo;
            }
        }
        
        // 测试文档处理
        async function testDocumentProcessing() {
            if (!selectedDocFile) return;
            
            const resultDiv = document.getElementById('docResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '<span class="status loading">处理中...</span> 正在处理文档文件...';
            
            try {
                let result;
                
                // 优先使用专门的文档处理插件
                if (typeof window.__processDocumentByDocumentPlugin === 'function') {
                    console.log('使用专门的文档处理插件');
                    result = await window.__processDocumentByDocumentPlugin(selectedDocFile, {
                        sendToChat: false // 测试时不发送到聊天
                    });
                } else if (typeof window.__processFileByPlugin === 'function') {
                    console.log('使用主插件处理文档');
                    result = await window.__processFileByPlugin(selectedDocFile, {
                        sendToChat: false
                    });
                } else {
                    throw new Error('没有可用的文档处理插件');
                }
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <span class="status success">成功</span> 文档处理成功！
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('文档处理失败:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <span class="status error">失败</span> 文档处理失败: ${error.message}
                    <pre>错误详情: ${error.stack || error.toString()}</pre>
                `;
            }
        }
        
        // 测试图片处理
        async function testImageProcessing() {
            if (!selectedImgFile) return;
            
            const resultDiv = document.getElementById('imgResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '<span class="status loading">处理中...</span> 正在处理图片文件...';
            
            try {
                let result;
                
                if (typeof window.__uploadImageByPlugin === 'function') {
                    result = await window.__uploadImageByPlugin(selectedImgFile);
                } else if (typeof window.__processFileByPlugin === 'function') {
                    result = await window.__processFileByPlugin(selectedImgFile);
                } else {
                    throw new Error('没有可用的图片处理插件');
                }
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <span class="status success">成功</span> 图片处理成功！
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('图片处理失败:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <span class="status error">失败</span> 图片处理失败: ${error.message}
                    <pre>错误详情: ${error.stack || error.toString()}</pre>
                `;
            }
        }
        
        // 测试文件类型识别
        async function testFileTypeDetection() {
            if (!selectedMixFile) return;
            
            const resultDiv = document.getElementById('mixResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '<span class="status loading">检测中...</span> 正在检测文件类型...';
            
            try {
                const file = selectedMixFile;
                const fileType = file.type || '';
                const fileName = file.name || '';
                const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
                
                // 检测是否为文档
                const isDocument = typeof window.__isDocumentFile === 'function' ? 
                    window.__isDocumentFile(file) : false;
                
                // 检测是否为图片
                const isImage = fileType.startsWith('image/') || 
                    ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(fileExtension);
                
                const detectionResult = {
                    fileName: fileName,
                    fileType: fileType,
                    fileExtension: fileExtension,
                    fileSize: file.size,
                    isDocument: isDocument,
                    isImage: isImage,
                    recommendedProcessor: isImage ? '图片处理器' : isDocument ? '文档处理器' : '未知'
                };
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <span class="status success">完成</span> 文件类型检测结果：
                    <pre>${JSON.stringify(detectionResult, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('文件类型检测失败:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <span class="status error">失败</span> 文件类型检测失败: ${error.message}
                `;
            }
        }
        
        // 创建测试文件
        function createTestFiles() {
            // 创建测试JSON文件
            const jsonData = {
                name: "测试文档",
                type: "json",
                content: "这是一个测试JSON文件",
                timestamp: new Date().toISOString(),
                data: [1, 2, 3, 4, 5]
            };
            
            const jsonBlob = new Blob([JSON.stringify(jsonData, null, 2)], { type: 'application/json' });
            const jsonFile = new File([jsonBlob], 'test.json', { type: 'application/json' });
            
            // 创建测试TXT文件
            const txtContent = `这是一个测试文本文件
创建时间: ${new Date().toLocaleString()}
内容: 用于测试文档处理功能
包含中文字符和特殊符号: !@#$%^&*()
多行文本测试`;
            
            const txtBlob = new Blob([txtContent], { type: 'text/plain' });
            const txtFile = new File([txtBlob], 'test.txt', { type: 'text/plain' });
            
            // 模拟文件选择
            selectedDocFile = jsonFile;
            document.getElementById('testDocBtn').disabled = false;
            
            alert('已创建测试文件！请点击"测试文档处理"按钮进行测试。');
        }
        
        // 页面加载时检查插件状态
        window.addEventListener('load', function() {
            checkPluginStatus();
            
            // 尝试加载插件
            const script1 = document.createElement('script');
            script1.src = '智能媒体助手/third-party-image-processor.js';
            script1.onload = () => {
                const script2 = document.createElement('script');
                script2.src = '智能媒体助手/document-processor-plugin.js';
                script2.onload = () => {
                    setTimeout(checkPluginStatus, 500);
                };
                document.head.appendChild(script2);
            };
            document.head.appendChild(script1);
        });
    </script>
</body>
</html>
