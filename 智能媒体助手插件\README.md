# 🎯 智能媒体助手 - SillyTavern Extension

一个功能强大的SillyTavern插件，提供智能的图片和文档处理功能。

## ✨ 主要功能

### 📸 图片处理
- **智能压缩**: 自动优化图片大小和质量
- **格式转换**: 支持多种图片格式
- **尺寸调整**: 自动调整图片尺寸
- **AI识图**: 集成AI图片识别功能

### 📄 文档处理
- **多格式支持**: txt, json, md, csv, html, xml, js, css等
- **智能识别**: 准确识别文件类型，避免误判
- **内容格式化**: JSON美化、CSV预览等
- **AI阅读**: 自动分析文档内容

### 🔧 核心特性
- **统一接口**: 一个插件处理所有媒体文件
- **智能识别**: 准确区分图片和文档文件
- **错误修复**: 解决txt、json文件被误识别为图片的问题
- **完整配置**: 丰富的设置选项
- **标准结构**: 符合SillyTavern插件规范

## 🚀 安装方法

### 方法1: 直接安装
1. 将整个 `智能媒体助手插件` 文件夹复制到SillyTavern的 `public/scripts/extensions/third-party/` 目录下
2. 重启SillyTavern
3. 在扩展管理中启用"智能媒体助手"

### 方法2: Git安装
```bash
cd SillyTavern/public/scripts/extensions/third-party/
git clone [your-repo-url] smart-media-assistant
```

## ⚙️ 配置选项

插件提供以下配置选项：

### 基础设置
- **启用图片处理**: 开启/关闭图片处理功能
- **启用文档处理**: 开启/关闭文档处理功能
- **文件大小限制**: 设置最大文件大小（MB）

### 图片设置
- **图片质量**: 压缩质量 (10-100)
- **图片最大尺寸**: 最大宽度或高度（像素）

### 文档设置
- **启用AI阅读**: 自动使用AI分析文档内容
- **显示处理信息**: 显示处理进度和结果
- **启用调试日志**: 在控制台输出详细日志

## 📋 支持的文件格式

### 图片格式
- **MIME类型**: `image/jpeg`, `image/png`, `image/gif`, `image/webp`, `image/bmp`
- **扩展名**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.webp`, `.bmp`

### 文档格式
- **文本文件**: `.txt`, `.md`, `.csv`, `.log`
- **代码文件**: `.js`, `.css`, `.html`, `.xml`
- **数据文件**: `.json`, `.yaml`, `.yml`
- **配置文件**: `.conf`, `.config`, `.ini`
- **富文本**: `.rtf`

## 🔌 API接口

插件提供以下全局API：

```javascript
// 通用文件处理（自动识别类型）
const result = await window.__processFileByPlugin(file, options);

// 专门的图片处理
const result = await window.__uploadImageByPlugin(file, options);

// 专门的文档处理
const result = await window.__processDocumentByPlugin(file, options);

// 检测是否为文档文件
const isDocument = window.__isDocumentFile(file);

// 获取支持的文件类型
const supportedTypes = window.__getSupportedFileTypes();
```

## 🛠️ 使用示例

### 基础使用
```javascript
// 处理任意文件（自动识别类型）
const fileInput = document.getElementById('fileInput');
const file = fileInput.files[0];

try {
  const result = await window.__processFileByPlugin(file, {
    sendToChat: true,        // 发送到聊天
    enableAIReading: true    // 启用AI阅读
  });
  
  console.log('处理成功:', result);
} catch (error) {
  console.error('处理失败:', error);
}
```

### 图片处理
```javascript
const imageFile = document.getElementById('imageInput').files[0];

const result = await window.__uploadImageByPlugin(imageFile);
console.log('图片URL:', result.url);
console.log('压缩信息:', result.metadata);
```

### 文档处理
```javascript
const docFile = document.getElementById('docInput').files[0];

const result = await window.__processDocumentByPlugin(docFile, {
  sendToChat: true,
  enableAIReading: true
});

console.log('文档内容:', result.content);
console.log('文档信息:', result.metadata);
```

## 🔍 故障排除

### 常见问题

**Q: txt文件仍然显示"Invalid image format"错误**
A: 确保插件正确加载，检查浏览器控制台是否有错误信息。

**Q: 文档处理功能不工作**
A: 检查插件设置中是否启用了"启用文档处理"选项。

**Q: 图片处理失败**
A: 检查图片文件大小是否超过限制，确保图片格式受支持。

### 调试方法

1. **启用调试日志**: 在插件设置中开启"启用调试日志"
2. **查看控制台**: 打开浏览器开发者工具查看详细日志
3. **检查插件状态**: 确认插件在扩展管理中正确加载

## 📝 更新日志

### v2.0.0 (2024-08-02)
- 🔧 修复txt、json等文档文件被误识别为图片的问题
- 🏗️ 重构为标准的SillyTavern插件结构
- 📋 整合图片和文档处理功能到单一插件
- ⚙️ 添加完整的配置界面和设置选项
- 🎨 改进用户界面和错误处理
- 📚 添加详细的文档和使用说明

### v1.0.0 (2024-07-01)
- 🎉 初始版本发布
- 📸 基础图片处理功能
- 📄 简单文档处理支持

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

## 👨‍💻 作者

**kencuo**
- GitHub: [kencuo](https://github.com/kencuo)

## 🙏 致谢

感谢SillyTavern社区的支持和反馈！
