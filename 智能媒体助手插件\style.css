/**
 * 智能媒体助手插件样式
 * 作者: kencuo
 * 版本: 2.0.0
 */

/* ==================== 基础样式 ==================== */

.smart-media-assistant {
  font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
}

/* ==================== 收缩栏样式 ==================== */

.smart-media-collapsible {
  border: 1px solid #444;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
  background: #2a2a2a;
  box-shadow: none;
  transition: all 0.2s ease;
}

.smart-media-collapsible:hover {
  border-color: #555;
}

.smart-media-header {
  background: #333;
  color: #ccc;
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: normal;
  font-size: 13px;
  transition: all 0.2s ease;
  user-select: none;
  list-style: none;
  border: none;
  outline: none;
  border-bottom: 1px solid #444;
}

.smart-media-header:hover {
  background: #3a3a3a;
  color: #fff;
}

.smart-media-header:active {
  background: #2a2a2a;
}

.smart-media-header::-webkit-details-marker {
  display: none;
}

.smart-media-icon {
  font-size: 14px;
  min-width: 16px;
  text-align: center;
  color: #888;
}

.smart-media-title {
  flex: 1;
  font-weight: normal;
  color: #ccc;
}

.smart-media-version {
  background: #555;
  color: #ccc;
  padding: 1px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: normal;
}

.smart-media-collapse-indicator {
  font-size: 10px;
  transition: transform 0.2s ease;
  color: #888;
  min-width: 12px;
  text-align: center;
}

.smart-media-collapsible[open] .smart-media-collapse-indicator {
  transform: rotate(180deg);
}

.smart-media-content {
  padding: 15px;
  background: #2a2a2a;
  border-top: none;
}

/* ==================== 文件处理状态样式 ==================== */

.smart-media-processing {
  position: relative;
  opacity: 0.7;
  pointer-events: none;
  transition: all 0.3s ease;
}

.smart-media-processing::after {
  content: '处理中...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: bold;
  z-index: 1000;
  white-space: nowrap;
}

.smart-media-success {
  border: 2px solid #4caf50 !important;
  border-radius: 6px;
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.smart-media-error {
  border: 2px solid #f44336 !important;
  border-radius: 6px;
  box-shadow: 0 0 10px rgba(244, 67, 54, 0.3);
  transition: all 0.3s ease;
}

/* ==================== 文件上传区域样式 ==================== */

.smart-media-drop-zone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.smart-media-drop-zone:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.smart-media-drop-zone.dragover {
  border-color: #4caf50;
  background: rgba(76, 175, 80, 0.1);
  transform: scale(1.02);
}

.smart-media-drop-zone .icon {
  font-size: 48px;
  margin-bottom: 10px;
  opacity: 0.6;
}

.smart-media-drop-zone .text {
  font-size: 16px;
  color: #666;
  margin-bottom: 5px;
}

.smart-media-drop-zone .subtext {
  font-size: 12px;
  color: #999;
}

/* ==================== 文件预览样式 ==================== */

.smart-media-preview {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #f9f9f9;
  margin: 10px 0;
}

.smart-media-preview .file-icon {
  font-size: 24px;
  width: 40px;
  text-align: center;
}

.smart-media-preview .file-info {
  flex: 1;
}

.smart-media-preview .file-name {
  font-weight: bold;
  margin-bottom: 2px;
}

.smart-media-preview .file-details {
  font-size: 12px;
  color: #666;
}

.smart-media-preview .file-actions {
  display: flex;
  gap: 5px;
}

.smart-media-preview .btn-small {
  padding: 4px 8px;
  font-size: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.smart-media-preview .btn-process {
  background: #4caf50;
  color: white;
}

.smart-media-preview .btn-process:hover {
  background: #45a049;
}

.smart-media-preview .btn-remove {
  background: #f44336;
  color: white;
}

.smart-media-preview .btn-remove:hover {
  background: #da190b;
}

/* ==================== 进度条样式 ==================== */

.smart-media-progress {
  width: 100%;
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
  margin: 10px 0;
}

.smart-media-progress-bar {
  height: 100%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 3px;
  transition: width 0.3s ease;
  position: relative;
}

.smart-media-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* ==================== 设置面板样式 ==================== */

.smart-media-settings {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 15px;
  margin: 10px 0;
}

.smart-media-settings h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.smart-media-setting-group {
  margin-bottom: 15px;
}

.smart-media-setting-group:last-child {
  margin-bottom: 0;
}

.smart-media-setting-label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #555;
}

.smart-media-setting-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.smart-media-setting-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
}

.smart-media-setting-checkbox {
  margin-right: 8px;
}

.smart-media-setting-description {
  font-size: 12px;
  color: #777;
  margin-top: 3px;
}

/* ==================== 状态指示器样式 ==================== */

.smart-media-status {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.smart-media-status.loading {
  background: #2196f3;
  color: white;
}

.smart-media-status.success {
  background: #4caf50;
  color: white;
}

.smart-media-status.error {
  background: #f44336;
  color: white;
}

.smart-media-status.warning {
  background: #ff9800;
  color: white;
}

.smart-media-status .icon {
  font-size: 14px;
}

/* ==================== 响应式设计 ==================== */

@media (max-width: 768px) {
  .smart-media-preview {
    flex-direction: column;
    align-items: flex-start;
  }

  .smart-media-preview .file-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .smart-media-drop-zone {
    padding: 15px;
  }

  .smart-media-drop-zone .icon {
    font-size: 36px;
  }

  .smart-media-settings {
    padding: 10px;
  }
}

/* ==================== 动画效果 ==================== */

@keyframes smart-media-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes smart-media-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.smart-media-fade-in {
  animation: smart-media-fade-in 0.3s ease;
}

.smart-media-pulse {
  animation: smart-media-pulse 2s infinite;
}

/* ==================== 工具提示样式 ==================== */

.smart-media-tooltip {
  position: relative;
  cursor: help;
}

.smart-media-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1000;
}

.smart-media-tooltip:hover::after {
  opacity: 1;
}

/* ==================== 深色主题适配 ==================== */

[data-theme='dark'] .smart-media-settings {
  background: #2a2a2a;
  color: #e0e0e0;
}

[data-theme='dark'] .smart-media-settings h3 {
  color: #e0e0e0;
}

[data-theme='dark'] .smart-media-setting-label {
  color: #ccc;
}

[data-theme='dark'] .smart-media-setting-input {
  background: #333;
  border-color: #555;
  color: #e0e0e0;
}

[data-theme='dark'] .smart-media-setting-input:focus {
  border-color: #667eea;
}

[data-theme='dark'] .smart-media-preview {
  background: #2a2a2a;
  border-color: #555;
  color: #e0e0e0;
}

[data-theme='dark'] .smart-media-drop-zone {
  background: rgba(255, 255, 255, 0.02);
  border-color: #555;
  color: #ccc;
}

[data-theme='dark'] .smart-media-drop-zone:hover {
  background: rgba(102, 126, 234, 0.1);
}
