<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>智能媒体助手插件 - 收缩栏演示</title>
    <link rel="stylesheet" href="智能媒体助手插件/style.css" />
    <style>
      body {
        font-family: 'Microsoft YaHei', Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background: #1a1a1a;
        min-height: 100vh;
        color: #ccc;
      }

      .demo-container {
        background: #2a2a2a;
        border: 1px solid #444;
        border-radius: 8px;
        padding: 30px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }

      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
        font-size: 2.5em;
      }

      .demo-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        background: #f9f9f9;
      }

      .demo-section h2 {
        color: #555;
        margin-top: 0;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        margin: 5px;
        transition: all 0.3s ease;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .info-box {
        background: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 15px;
        margin: 15px 0;
        border-radius: 0 8px 8px 0;
      }

      .feature-list {
        list-style: none;
        padding: 0;
      }

      .feature-list li {
        padding: 8px 0;
        border-bottom: 1px solid #eee;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .feature-list li:last-child {
        border-bottom: none;
      }

      .feature-icon {
        font-size: 18px;
        min-width: 25px;
      }

      .code-block {
        background: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        overflow-x: auto;
        margin: 10px 0;
      }
    </style>
  </head>
  <body>
    <div class="demo-container">
      <h1>🎯 智能媒体助手插件收缩栏演示</h1>

      <div class="demo-section">
        <h2>📋 功能介绍</h2>
        <p>
          这是参考index.js实现的收缩栏功能演示。收缩栏提供了优雅的界面折叠体验，让用户可以根据需要显示或隐藏设置内容。
        </p>

        <div class="info-box">
          <strong>💡 设计特点：</strong>
          <ul class="feature-list">
            <li><span class="feature-icon">🎨</span>渐变色彩设计，视觉效果优美</li>
            <li><span class="feature-icon">🔄</span>平滑的展开/收缩动画</li>
            <li><span class="feature-icon">💾</span>自动保存收缩状态到localStorage</li>
            <li><span class="feature-icon">🖱️</span>鼠标悬停和点击反馈效果</li>
            <li><span class="feature-icon">📱</span>响应式设计，适配不同屏幕</li>
          </ul>
        </div>
      </div>

      <div class="demo-section">
        <h2>🎮 实际演示</h2>
        <p>下面是智能媒体助手插件的实际设置界面，具有完整的收缩栏功能：</p>

        <!-- 这里是实际的插件设置界面 -->
        <div class="smart-media-assistant">
          <details class="smart-media-collapsible" open>
            <summary class="smart-media-header">
              <span class="smart-media-icon">🎯</span>
              <span class="smart-media-title">智能媒体助手</span>
              <span class="smart-media-version">v2.0.0</span>
              <span class="smart-media-collapse-indicator">▼</span>
            </summary>
            <div class="smart-media-content">
              <div class="setting-group">
                <h4>🔧 基础设置</h4>
                <label> <input type="checkbox" checked /> 启用图片处理 </label>
                <div class="setting-description">开启图片压缩、优化和AI识图功能</div>

                <label> <input type="checkbox" checked /> 启用文档处理 </label>
                <div class="setting-description">开启txt、json等文档文件的处理功能</div>
              </div>

              <div class="setting-group">
                <h4>🖼️ 图片设置</h4>
                <label>
                  图片质量: <span>85</span>%
                  <input type="range" min="10" max="100" step="5" value="85" />
                </label>
                <div class="setting-description">图片压缩质量，数值越高质量越好但文件越大</div>

                <label>
                  图片最大尺寸: <span>2048</span>px
                  <input type="range" min="512" max="4096" step="128" value="2048" />
                </label>
                <div class="setting-description">图片的最大宽度或高度（像素）</div>
              </div>

              <div class="setting-group">
                <h4>📄 文档设置</h4>
                <label> <input type="checkbox" checked /> 启用AI文档阅读 </label>
                <div class="setting-description">自动使用AI分析上传的文档内容</div>

                <label>
                  文件大小限制: <span>20</span>MB
                  <input type="range" min="1" max="100" step="1" value="20" />
                </label>
                <div class="setting-description">允许处理的最大文件大小</div>
              </div>

              <div class="setting-group">
                <h4>⚙️ 高级设置</h4>
                <label> <input type="checkbox" /> 显示处理信息 </label>
                <div class="setting-description">显示文件处理的详细信息和进度</div>

                <label> <input type="checkbox" /> 启用调试日志 </label>
                <div class="setting-description">在控制台输出详细的调试信息</div>
              </div>
            </div>
          </details>
        </div>
      </div>

      <div class="demo-section">
        <h2>🔧 控制按钮</h2>
        <p>使用下面的按钮来测试收缩栏功能：</p>

        <button class="btn" onclick="toggleCollapsible()">🔄 切换收缩状态</button>
        <button class="btn" onclick="expandCollapsible()">🔼 展开设置</button>
        <button class="btn" onclick="collapseCollapsible()">🔽 收缩设置</button>
        <button class="btn" onclick="resetCollapsibleState()">🔄 重置状态</button>
      </div>

      <div class="demo-section">
        <h2>💻 技术实现</h2>
        <p>收缩栏功能基于HTML5的 <code>&lt;details&gt;</code> 和 <code>&lt;summary&gt;</code> 元素实现：</p>

        <div class="code-block">
          &lt;details class="smart-media-collapsible" open&gt; &lt;summary class="smart-media-header"&gt; &lt;span
          class="smart-media-icon"&gt;🎯&lt;/span&gt; &lt;span class="smart-media-title"&gt;智能媒体助手&lt;/span&gt;
          &lt;span class="smart-media-version"&gt;v2.0.0&lt;/span&gt; &lt;span
          class="smart-media-collapse-indicator"&gt;▼&lt;/span&gt; &lt;/summary&gt; &lt;div
          class="smart-media-content"&gt; &lt;!-- 设置内容 --&gt; &lt;/div&gt; &lt;/details&gt;
        </div>

        <div class="info-box">
          <strong>🔑 关键特性：</strong>
          <ul>
            <li><strong>状态持久化：</strong>使用localStorage保存用户的收缩偏好</li>
            <li><strong>动画效果：</strong>CSS transition实现平滑的展开/收缩动画</li>
            <li><strong>事件监听：</strong>监听toggle事件来响应状态变化</li>
            <li><strong>视觉反馈：</strong>指示器旋转和悬停效果</li>
          </ul>
        </div>
      </div>
    </div>

    <script>
      // 收缩栏控制函数
      function toggleCollapsible() {
        const details = document.querySelector('.smart-media-collapsible');
        if (details.hasAttribute('open')) {
          details.removeAttribute('open');
          console.log('🔽 设置面板已收缩');
        } else {
          details.setAttribute('open', '');
          console.log('🔼 设置面板已展开');
        }
      }

      function expandCollapsible() {
        const details = document.querySelector('.smart-media-collapsible');
        details.setAttribute('open', '');
        console.log('🔼 设置面板已展开');
      }

      function collapseCollapsible() {
        const details = document.querySelector('.smart-media-collapsible');
        details.removeAttribute('open');
        console.log('🔽 设置面板已收缩');
      }

      function resetCollapsibleState() {
        localStorage.removeItem('smart-media-assistant-collapsed');
        expandCollapsible();
        console.log('🔄 收缩状态已重置');
        alert('✅ 收缩状态已重置为默认（展开）');
      }

      // 初始化收缩栏功能
      function initCollapsibleDemo() {
        const details = document.querySelector('.smart-media-collapsible');
        const indicator = document.querySelector('.smart-media-collapse-indicator');

        // 监听收缩状态变化
        details.addEventListener('toggle', function () {
          const isOpen = this.hasAttribute('open');

          // 更新指示器
          if (isOpen) {
            indicator.style.transform = 'rotate(180deg)';
          } else {
            indicator.style.transform = 'rotate(0deg)';
          }

          console.log(`📋 收缩栏状态: ${isOpen ? '展开' : '收缩'}`);
        });

        // 添加鼠标交互效果
        const header = document.querySelector('.smart-media-header');
        header.addEventListener('mousedown', function () {
          this.style.transform = 'translateY(0px)';
        });

        header.addEventListener('mouseup', function () {
          this.style.transform = 'translateY(-1px)';
        });

        header.addEventListener('mouseleave', function () {
          this.style.transform = 'translateY(-1px)';
        });

        console.log('🎯 收缩栏演示已初始化');
      }

      // 页面加载完成后初始化
      document.addEventListener('DOMContentLoaded', initCollapsibleDemo);
    </script>
  </body>
</html>
