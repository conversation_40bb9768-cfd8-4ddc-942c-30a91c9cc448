<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>居民论坛</title>
    <style>
      /* 全局样式 */
      body {
        margin: 0;
        font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
        background-attachment: fixed;
        font-size: 14px;
        color: #000;
      }

      /* 导航栏 */
      .navbar {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 10px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        min-height: 50px;
        color: #fff;
        border-bottom: 2px solid #4fc3f7;
      }

      .nav-logo {
        font-size: 24px;
        font-weight: bold;
        color: #4fc3f7;
        margin-left: 10px;
        text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
      }

      /* 论坛内容 */
      .forum-container {
        max-width: 1000px;
        margin: 80px auto 40px;
        padding: 0;
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid #4fc3f7;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(79, 195, 247, 0.2);
        backdrop-filter: blur(10px);
      }

      .forum-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        color: #fff;
        padding: 20px 15px;
        margin-bottom: 0;
        text-align: center;
        border-bottom: 1px solid #4fc3f7;
        position: relative;
        border-radius: 8px 8px 0 0;
      }

      .forum-header::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(to right, #4fc3f7, #29b6f6, #4fc3f7);
      }

      .forum-title {
        font-size: 28px;
        color: #4fc3f7;
        margin-bottom: 5px;
        font-weight: 600;
        letter-spacing: 1px;
        text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
      }

      .forum-description {
        color: #e3f2fd;
        font-size: 14px;
        font-style: italic;
      }

      /* 帖子列表 */
      .post-list {
        display: flex;
        flex-direction: column;
        gap: 0;
        max-height: 600px;
        overflow-y: auto;
        border-top: 1px solid #e1f5fe;
      }

      .post-list::-webkit-scrollbar {
        width: 8px;
      }

      .post-list::-webkit-scrollbar-track {
        background: #f1f8e9;
      }

      .post-list::-webkit-scrollbar-thumb {
        background: #4fc3f7;
        border-radius: 4px;
      }

      .post-list::-webkit-scrollbar-thumb:hover {
        background: #29b6f6;
      }

      .post-item {
        background: #fff;
        padding: 15px;
        transition: all 0.3s ease;
        cursor: pointer;
        border-bottom: 1px solid #e1f5fe;
      }

      .post-item:hover {
        background: #e3f2fd;
        transform: translateX(5px);
        box-shadow: -3px 0 0 #4fc3f7;
      }

      .post-item.sticky {
        background: #f3e5f5;
        border-left: 3px solid #9c27b0;
      }

      .post-title {
        font-size: 16px;
        color: #1565c0;
        margin-bottom: 4px;
        font-weight: bold;
      }

      .post-content {
        font-size: 14px;
        color: #555;
        margin-bottom: 10px;
        line-height: 1.4;
      }

      .post-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #888;
      }

      .post-author {
        font-weight: bold;
        color: #1e3c72;
      }

      .post-type {
        background: linear-gradient(90deg, #1e3c72, #2a5298);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 10px;
      }

      .post-stats {
        display: flex;
        gap: 10px;
      }

      .post-stat {
        display: flex;
        align-items: center;
        gap: 3px;
      }

      .post-stat i {
        font-size: 12px;
      }

      /* 发帖按钮 */
      .new-post-btn {
        position: fixed;
        right: 25px;
        bottom: 25px;
        width: auto;
        height: auto;
        background: linear-gradient(90deg, #1e3c72, #2a5298);
        color: white;
        border: none;
        border-radius: 25px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 15px rgba(30, 60, 114, 0.4);
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 12px 24px;
        font-weight: bold;
      }

      .new-post-btn:hover {
        background: linear-gradient(90deg, #2a5298, #1e3c72);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(30, 60, 114, 0.6);
      }

      /* 警告横幅 */
      .warning-banner {
        background: linear-gradient(90deg, #e8f5e8, #f0f8ff);
        color: #1565c0;
        text-align: center;
        padding: 15px;
        font-size: 16px;
        border-bottom: 1px solid #e1f5fe;
        font-weight: bold;
        border-radius: 8px 8px 0 0;
      }

      .classified {
        font-size: 32px;
        font-weight: bold;
        letter-spacing: 3px;
        margin: 5px 0;
        color: #1e3c72;
        text-shadow: 0 0 10px rgba(30, 60, 114, 0.3);
      }

      .warning-text {
        font-size: 14px;
        margin-top: 5px;
        color: #2a5298;
      }

      /* 站点新闻 */
      .site-news {
        background: #fff;
        border: 1px solid #e1f5fe;
        margin: 15px;
        border-radius: 4px;
        overflow: hidden;
      }

      .section-header {
        background: linear-gradient(90deg, #1e3c72, #2a5298);
        color: #4fc3f7;
        padding: 8px 15px;
        font-weight: bold;
        position: relative;
      }

      .section-header::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 20px 33px 0;
        border-color: transparent #fff transparent transparent;
      }

      .section-content {
        padding: 15px;
        background: #f8fcff;
      }

      .news-date {
        font-weight: bold;
        margin-bottom: 10px;
        color: #1565c0;
      }

      /* 弹窗 */
      .modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 2000;
        display: none;
        backdrop-filter: blur(5px);
      }

      .modal-content {
        background: #fff;
        padding: 20px;
        border: 2px solid #4fc3f7;
        border-radius: 8px;
        width: 400px;
        max-width: 90%;
        box-shadow: 0 10px 30px rgba(79, 195, 247, 0.3);
      }

      .modal-header {
        font-size: 18px;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 2px solid #4fc3f7;
        padding-bottom: 10px;
      }

      .modal-header h2 {
        margin: 0;
        color: #1e3c72;
        font-size: 20px;
      }

      .modal-header .close-btn {
        font-size: 22px;
        color: #1e3c72;
        cursor: pointer;
        border: none;
        background: none;
      }

      .modal-body {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .modal-body label {
        font-size: 14px;
        color: #1e3c72;
        font-weight: bold;
      }

      .modal-body input,
      .modal-body textarea,
      .modal-body select {
        width: 100%;
        padding: 8px;
        border: 1px solid #4fc3f7;
        border-radius: 4px;
        font-size: 14px;
        font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
        transition: border-color 0.3s;
      }

      .modal-body input:focus,
      .modal-body textarea:focus,
      .modal-body select:focus {
        outline: none;
        border-color: #1e3c72;
        box-shadow: 0 0 5px rgba(79, 195, 247, 0.3);
      }

      .modal-body textarea {
        resize: vertical;
        min-height: 100px;
      }

      .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
      }

      .modal-footer button {
        background: linear-gradient(90deg, #1e3c72, #2a5298);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        font-weight: bold;
      }

      .modal-footer button:hover {
        background: linear-gradient(90deg, #2a5298, #1e3c72);
        transform: translateY(-1px);
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .forum-container {
          margin: 70px 10px 30px;
        }

        .forum-title {
          font-size: 22px;
        }

        .classified {
          font-size: 24px;
        }

        .post-title {
          font-size: 14px;
        }

        .new-post-btn {
          right: 15px;
          bottom: 15px;
          font-size: 12px;
          padding: 10px 20px;
        }

        .modal-content {
          width: 300px;
        }
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-logo">星际联邦居民论坛</div>
    </nav>

    <!-- 论坛内容 -->
    <div class="forum-container">
      <!-- 警告横幅 -->
      <div class="warning-banner">
        <div>欢迎来到星际联邦第七区域</div>
        <div class="classified">居民交流论坛</div>
        <div class="warning-text">请遵守联邦公民守则，理性讨论，和谐共处</div>
      </div>

      <div class="forum-header">
        <h1 class="forum-title">星域生活交流区</h1>
        <p class="forum-description">连接星际，分享日常</p>
      </div>

      <!-- 站点新闻 -->
      <div class="site-news">
        <div class="section-header">联邦公告</div>
        <div class="section-content">
          <div class="news-date">最新通知</div>
          <p>
            星际帝国提醒各位居民：新的雄虫优先政策将于下月实施，请各位雌虫提前了解相关规定。论坛内请保持文明讨论，尊重等级秩序。
          </p>
        </div>
      </div>

      <!-- 帖子列表 -->
      <div class="post-list">
        <!-- 置顶帖 -->
        $1
        <!-- 其他帖子 -->
        $2
      </div>
    </div>

    <!-- 发帖按钮 -->
    <button class="new-post-btn" id="show-modal-btn">发布新话题</button>

    <!-- 发送帖子的弹窗 -->
    <div class="modal" id="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>发布新话题</h2>
          <button class="close-btn" id="close-modal-btn">&times;</button>
        </div>
        <div class="modal-body">
          <label for="post-title">话题标题:</label>
          <input type="text" id="post-title" placeholder="输入话题标题" />

          <label for="post-content">话题内容:</label>
          <textarea id="post-content" rows="4" placeholder="分享你的想法或经历"></textarea>

          <label for="post-category">分类:</label>
          <select id="post-category">
            <option value="日常">日常分享</option>
            <option value="工作">工作交流</option>
            <option value="情感">情感话题</option>
            <option value="求助">求助咨询</option>
            <option value="闲聊">随便聊聊</option>
          </select>
        </div>
        <div class="modal-footer">
          <button id="submit-btn">发布话题</button>
          <button id="close-btn-modal">取消</button>
        </div>
      </div>
    </div>

    <script>
      // 打开弹窗
      document.getElementById('show-modal-btn').addEventListener('click', function () {
        document.getElementById('modal').style.display = 'flex';
      });

      // 关闭弹窗
      document.getElementById('close-modal-btn').addEventListener('click', function () {
        document.getElementById('modal').style.display = 'none';
      });

      document.getElementById('close-btn-modal').addEventListener('click', function () {
        document.getElementById('modal').style.display = 'none';
      });

      // 监听帖子点击（包括置顶和普通帖子）
      document.querySelectorAll('.post-item').forEach(post => {
        post.addEventListener('click', function () {
          const title = post.querySelector('.post-title').textContent.replace('【置顶】', '');
          triggerSlash(`/send 查看论坛${title}|/trigger`);
        });
      });

      // 发布按钮
      document.getElementById('submit-btn').addEventListener('click', function () {
        const title = document.getElementById('post-title').value;
        const content = document.getElementById('post-content').value;
        const category = document.getElementById('post-category').value;
        triggerSlash(`/send 发布论坛新帖: ${category} - 标题：${title} 帖子内容：${content}|/trigger`);
        document.getElementById('modal').style.display = 'none';
      });
    </script>
  </body>
</html>
```
