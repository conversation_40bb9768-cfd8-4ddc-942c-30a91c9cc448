import*as e from"../../../../../scripts/slash-commands/SlashCommandCommonEnumsProvider.js";import*as t from"../../../../../scripts/slash-commands/SlashCommandEnumValue.js";import*as n from"../../../../../scripts/power-user.js";import*as a from"../../../../../scripts/macros.js";import*as r from"../../../../../scripts/slash-commands/SlashCommand.js";import*as i from"../../../../../scripts/RossAscends-mods.js";import*as s from"../../../../../scripts/slash-commands/SlashCommandArgument.js";import*as o from"../../../../../lib/jszip.min.js";import*as c from"../../../../../scripts/utils.js";import*as l from"../../../../../scripts/popup.js";import*as d from"../../../../../script.js";import*as p from"../../../../../scripts/i18n.js";import*as h from"../../../../../scripts/extensions/regex/engine.js";import*as u from"../../../../../scripts/slash-commands/SlashCommandParser.js";import*as f from"../../../../../scripts/extensions.js";import*as m from"../../../../../scripts/authors-note.js";import*as g from"../../../../../scripts/openai.js";import*as v from"../../../../../scripts/world-info.js";import*as y from"../../../../../scripts/PromptManager.js";import*as b from"../../../../../scripts/group-chats.js";import*as w from"../../../../../scripts/slash-commands.js";var E,S,C,T,A,x,I,R,k,O={9:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.d(t,{$:()=>d});var r=n(1613),i=n(363),s=n.n(i),o=n(190),c=e([o]);o=(c.then?(await c)():c)[0];var l=n.n(o);class d{model;view;syncService;cardFactory;constructor(e,t,n,a){this.model=e,this.view=t,this.syncService=n,this.cardFactory=a}async init(e){this.view.initUI(),this.bindEvents(e),await this.syncService.initCurrentType(),await this.syncService.setCurrentType("global"),await this.loadVariables("global")}bindEvents(e){e.find(".tab-item").on("click",this.handleTabChange.bind(this)),e.on("click",".add-list-item",this.handleAddListItem.bind(this)),e.on("click",".list-item-delete",this.handleDeleteListItem.bind(this)),e.on("click",".delete-btn",this.handleDeleteVariableCard.bind(this)),e.on("click",".save-btn",this.handleSaveVariableCard.bind(this)),e.on("click","#add-variable",this.handleAddVariable.bind(this)),e.on("click","#clear-all",this.handleClearAll.bind(this)),e.on("click","#filter-icon",this.handleFilterIconClick.bind(this)),e.on("change",".filter-checkbox",this.handleFilterOptionChange.bind(this)),e.on("input","#variable-search",this.handleVariableSearch.bind(this)),e.on("click","#floor-filter-btn",this.handleFloorRangeFilter.bind(this)),e.on("nested-card:changed",".variable-card",this.handleNestedCardChanged.bind(this))}async loadVariables(e){const t=await this.model.loadFromTavern(e);if(0!==t.length){if("message"===e){this.view.container.find("#floor-filter-container").show();const[e,t]=this.model.getFloorRange();this.view.updateFloorRangeInputs(e,t)}else this.view.container.find("#floor-filter-container").hide();this.view.refreshVariableCards(e,t),this.applyFilters()}}async handleTabChange(e){const t=$(e.currentTarget).attr("id");if(!t)return;const n=t.replace("-tab","");n!==this.model.getActiveVariableType()&&(this.view.setActiveTab(n),await this.syncService.setCurrentType(n),await this.loadVariables(n))}handleAddListItem(e){const t=$(e.currentTarget).siblings(".list-items-container"),n=$('\n      <div class="list-item">\n        <span class="drag-handle">☰</span>\n        <textarea class="variable-content-input" placeholder="输入变量内容"></textarea>\n        <button class="list-item-delete"><i class="fa-solid fa-times"></i></button>\n      </div>\n    ');t.append(n),n.find("textarea").focus()}handleDeleteListItem(e){const t=$(e.currentTarget).closest(".list-item");t.css({"background-color":"rgba(255, 0, 0, 0.2)",transition:"all 0.3s ease"}),setTimeout(()=>{t.css({transform:"scale(0.9)",opacity:"0.7"}),setTimeout(()=>{t.remove()},200)},50)}async handleDeleteVariableCard(e){const t=$(e.currentTarget),n=t.closest(".variable-card").attr("data-variable-id")||"",a=this.model.getActiveVariableType();t.hasClass("object-delete-btn")||this.view.showConfirmDialog("确定要删除变量吗？此操作无法撤销。",async e=>{if(e)try{this.model.removeFromMap(n)&&(await this.model.saveAllVariables(a),this.view.removeVariableCard(n))}catch(e){s().error("[VariableManager] 删除变量失败:",e)}})}async handleAddVariable(){const e=this.model.getActiveVariableType();this.view.showAddVariableDialog((t,n)=>{this.view.addNewVariableCard(e,t,n),this.applyFilters()})}async handleSaveVariableCard(e){const t=$(e.currentTarget).closest(".variable-card"),n=this.model.getActiveVariableType(),a=r.w.inferDataType(this.cardFactory.getVariableFromCard(t));let i;if("object"==a&&this.syncObjectCardData(t),"message"===n){const e=t.attr("data-floor-id");e&&(i=parseInt(e))}let o,c={name:this.view.getVariableCardName(t),value:this.cardFactory.getVariableFromCard(t)?.value,dataType:a,id:t.attr("data-variable-id")||"",...void 0!==i&&{message_id:i}};if(null==c.name||""===c.name.trim())return void toastr.error("变量名不能为空");o="message"===n&&void 0!==i?this.model.getVariablesByMessageId(i).filter(e=>e.name===c.name):this.model.getVariablesByName(c.name);if(o.filter(e=>e.id!==c.id).length>0)toastr.error("变量名重复");else try{if(c.id&&this.model.getVariableById(c.id))this.model.updateInMap(c.id,c.name,c.value,i);else{const e=this.model.addToMap(c.name,c.value,i);c.id=e.id,t.attr("data-variable-id",c.id)}"message"===n&&void 0!==i?await this.model.saveAllVariables(n,i):await this.model.saveAllVariables(n),this.view.addAnimation(t,"variable-changed",()=>{})}catch(e){s().error("[VariableManager] 保存变量失败:",e),toastr.error(`保存变量失败: ${e instanceof Error?e.message:"未知错误"}`)}}async handleClearAll(){const e=this.model.getActiveVariableType();this.view.showConfirmDialog(`确定要清除所有${this.getVariableTypeName(e)}变量吗？此操作不可撤销。`,async t=>{if(t)try{await this.model.clearAllVariables(e);if(this.view.container.find(`#${e}-content .variable-list`).empty(),"message"==e){this.view.container.find(`#${e}-content .floor-variables-container`).empty()}toastr.success(`已清除所有${this.getVariableTypeName(e)}变量`)}catch(t){s().error(`[VariableManager] 清除${e}变量失败:`,t),toastr.error(`清除${this.getVariableTypeName(e)}变量时出错: ${t.message||"未知错误"}`)}})}handleFilterIconClick(){this.view.container.find(".filter-options").toggle()}handleFilterOptionChange(e){const t=$(e.currentTarget),n=t.data("type"),a=t.is(":checked");this.model.updateFilterState(n,a),this.applyFilters()}handleVariableSearch(e){const t=$(e.currentTarget).val();this.model.updateSearchKeyword(t),this.applyFilters()}applyFilters(){const e=this.model.getFilterState(),t=this.model.getSearchKeyword();this.view.applyClientSideFilters(e,t)}handleFloorRangeFilter(){const e=this.view.container.find("#floor-min"),t=this.view.container.find("#floor-max"),n=e.val(),a=t.val(),r=n.trim()?parseInt(n,10):null,i=a.trim()?parseInt(a,10):null;if(n.trim()&&isNaN(r)||a.trim()&&isNaN(i))return void this.view.showFloorFilterError("请输入有效的数字");if(null!==r&&r<0)return void this.view.showFloorFilterError("最小楼层不能小于0");if(null!==i&&i<0)return void this.view.showFloorFilterError("最大楼层不能小于0");if(null!==r&&null!==i&&r>i)return void this.view.showFloorFilterError("最小值不能大于最大值");if(null===r&&null===i)return void this.view.showFloorFilterError("请至少设置最小楼层或最大楼层");this.view.hideFloorFilterError();const s=r??0,o=i??9999;this.applyFloorRangeAndReload(s,o)}async applyFloorRangeAndReload(e,t){try{this.model.updateFloorRange(e,t),this.view.updateFloorRangeInputs(e,t),await this.loadVariables("message")}catch(e){s().error("[VariableManager] 应用楼层范围并重新加载变量失败:",e)}}cleanup(){try{this.syncService.cleanup()}catch(e){s().error("[VariableManager] 清理资源失败:",e)}}syncObjectCardData(e){if("card"===(e.attr("data-view-mode")||"card")){const t=this.cardFactory.getVariableFromCard(e);e.find(".yaml-input").val(l().stringify(t?.value,null,2))}}async handleNestedCardChanged(e){const t=$(e.currentTarget).find(".variable-action-btn.save-btn");t.length>0&&t.trigger("click")}getVariableTypeName(e){switch(e){case"global":return"全局";case"character":return"角色";case"chat":return"聊天";case"message":return"消息";default:return e}}}a()}catch(e){a(e)}})},162:(n,a,r)=>{r.d(a,{oS:()=>_,Gy:()=>y,SP:()=>g,nW:()=>v,xI:()=>b,y7:()=>w});var i=r(7517),s=r(6530),o=r(4365),c=r(9489),l=r(1320),d=r(2361);const p=(e=>{var t={};return r.d(t,e),t})({commonEnumProviders:()=>e.commonEnumProviders,enumIcons:()=>e.enumIcons});const h=(e=>{var t={};return r.d(t,e),t})({SlashCommandEnumValue:()=>t.SlashCommandEnumValue,enumTypes:()=>t.enumTypes});var u=r(8853),f=r(363),m=r.n(f);async function g(e){const t=e.type.toLowerCase(),n=e.mode.toLowerCase();if(!["bgm","ambient"].includes(t)||!["repeat","random","single","stop"].includes(n))return m().warn("WARN: Invalid arguments for /audiomode command"),"";if("bgm"===t){(0,s.P1)("audio.bgm_mode",n);const e={repeat:"fa-repeat",random:"fa-random",single:"fa-redo-alt",stop:"fa-cancel"};$("#audio_bgm_mode_icon").removeClass("fa-repeat fa-random fa-redo-alt fa-cancel"),$("#audio_bgm_mode_icon").addClass(e[n])}else if("ambient"===t){(0,s.P1)("audio.ambient_mode",n);const e={repeat:"fa-repeat",random:"fa-random",single:"fa-redo-alt",stop:"fa-cancel"};$("#audio_ambient_mode_icon").removeClass("fa-repeat fa-random fa-redo-alt fa-cancel"),$("#audio_ambient_mode_icon").addClass(e[n])}return(0,o.saveSettingsDebounced)(),""}async function _(e){const t=e.type.toLowerCase(),n=(e.state||"true").toLowerCase();return t?("bgm"===t?"true"===n?($("#enable_bgm").prop("checked",!0),await(0,i.fX)("bgm")):"false"===n&&($("#enable_bgm").prop("checked",!1),await(0,i.fX)("bgm")):"ambient"===t&&("true"===n?($("#enable_ambient").prop("checked",!0),await(0,i.fX)("ambient")):"false"===n&&($("#enable_ambient").prop("checked",!1),await(0,i.fX)("ambient"))),""):(m().warn("WARN: Missing arguments for /audioenable command"),"")}async function v(e){const t=e.type.toLowerCase(),n=(e.play||"true").toLowerCase();if(!t)return m().warn("WARN: Missing arguments for /audioplaypause command"),"";if("bgm"===t){if("true"===n)await(0,i.Tq)("bgm");else if("false"===n){$("#audio_bgm")[0].pause()}}else if("ambient"===t)if("true"===n)await(0,i.Tq)("ambient");else if("false"===n){$("#audio_ambient")[0].pause()}return""}async function y(e,t){const n=e.type.toLowerCase(),a=(e.play||"true").toLowerCase();if(!n||!t)return m().warn("WARN: Missing arguments for /audioimport command"),"";const r=t.split(",").map(e=>e.trim()).filter(e=>""!==e).filter((e,t,n)=>n.indexOf(e)===t);if(0===r.length)return m().warn("WARN: Invalid or empty URLs provided."),"";o.chat_metadata.variables||(o.chat_metadata.variables={});const l="bgm"===n?"bgmurl":"ambienturl",d=o.chat_metadata.variables[l]||[],p=[...new Set([...r,...d])];if(o.chat_metadata.variables[l]=p,(0,c.saveMetadataDebounced)(),"bgm"===n?(0,i.Mc)("bgm"):"ambient"===n&&(0,i.Mc)("ambient"),"true"===a&&r[0]){const e=r[0];"bgm"===n?((0,s.P1)("audio.bgm_selected",e),await(0,i.YS)("bgm",!0)):"ambient"===n&&((0,s.P1)("audio.ambient_selected",e),await(0,i.YS)("ambient",!0))}return""}async function b(e,t){const n=e.type.toLowerCase();if(!t)return m().warn("WARN: Missing URL for /audioselect command"),"";o.chat_metadata.variables||(o.chat_metadata.variables={});const a="bgm"===n?i.NM:i.$S,r="bgm"===n?"bgmurl":"ambienturl";if(a&&a.includes(t))return"bgm"===n?((0,s.P1)("audio.bgm_selected",t),await(0,i.YS)("bgm",!0)):"ambient"===n&&((0,s.P1)("audio.ambient_selected",t),await(0,i.YS)("ambient",!0)),"";const l=o.chat_metadata.variables[r]||[],d=[...new Set([t,...l])];return o.chat_metadata.variables[r]=d,(0,c.saveMetadataDebounced)(),"bgm"===n?((0,i.Mc)("bgm"),(0,s.P1)("audio.bgm_selected",t),await(0,i.YS)("bgm",!0)):"ambient"===n&&((0,i.Mc)("ambient"),(0,s.P1)("audio.ambient_selected",t),await(0,i.YS)("ambient",!0)),""}function w(){u.SlashCommandParser.addCommandObject(l.SlashCommand.fromProps({name:"audioselect",callback:b,namedArgumentList:[d.SlashCommandNamedArgument.fromProps({name:"type",description:"选择播放器类型 (bgm 或 ambient)",typeList:[d.ARGUMENT_TYPE.STRING],enumList:[new h.SlashCommandEnumValue("bgm",null,h.enumTypes.enum,p.enumIcons.file),new h.SlashCommandEnumValue("ambient",null,h.enumTypes.enum,p.enumIcons.file)],isRequired:!0})],unnamedArgumentList:[new d.SlashCommandArgument("url",[d.ARGUMENT_TYPE.STRING],!0)],helpString:"\n        <div>\n            选择并播放音频。如果音频链接不存在，则先导入再播放。\n        </div>\n        <div>\n            <strong>Example:</strong>\n            <ul>\n                <li>\n                    <pre><code>/audioselect type=bgm https://example.com/song.mp3</code></pre>\n                    选择并播放指定的音乐。\n                </li>\n                <li>\n                    <pre><code>/audioselect type=ambient https://example.com/sound.mp3</code></pre>\n                    选择并播放指定的音效。\n                </li>\n            </ul>\n        </div>\n      "})),u.SlashCommandParser.addCommandObject(l.SlashCommand.fromProps({name:"audioimport",callback:y,namedArgumentList:[d.SlashCommandNamedArgument.fromProps({name:"type",description:"选择导入类型 (bgm 或 ambient)",typeList:[d.ARGUMENT_TYPE.STRING],enumList:[new h.SlashCommandEnumValue("bgm",null,h.enumTypes.enum,p.enumIcons.file),new h.SlashCommandEnumValue("ambient",null,h.enumTypes.enum,p.enumIcons.file)],isRequired:!0}),d.SlashCommandNamedArgument.fromProps({name:"play",description:"导入后是否立即播放第一个链接",typeList:[d.ARGUMENT_TYPE.BOOLEAN],defaultValue:"true",isRequired:!1})],unnamedArgumentList:[new d.SlashCommandArgument("url",[d.ARGUMENT_TYPE.STRING],!0)],helpString:"\n        <div>\n            导入音频或音乐链接，并决定是否立即播放，默认为自动播放。可批量导入链接，使用英文逗号分隔。\n        </div>\n        <div>\n            <strong>Example:</strong>\n            <ul>\n                <li>\n                    <pre><code>/audioimport type=bgm https://example.com/song1.mp3,https://example.com/song2.mp3</code></pre>\n                    导入 BGM 音乐并立即播放第一个链接。\n                </li>\n                <li>\n                    <pre><code>/audioimport type=ambient play=false url=https://example.com/sound1.mp3,https://example.com/sound2.mp3 </code></pre>\n                    导入音效链接 (不自动播放)。\n                </li>\n            </ul>\n        </div>\n      "})),u.SlashCommandParser.addCommandObject(l.SlashCommand.fromProps({name:"audioplay",callback:v,namedArgumentList:[d.SlashCommandNamedArgument.fromProps({name:"type",description:"选择控制的播放器 (bgm 或 ambient)",typeList:[d.ARGUMENT_TYPE.STRING],enumList:[new h.SlashCommandEnumValue("bgm",null,h.enumTypes.enum,p.enumIcons.file),new h.SlashCommandEnumValue("ambient",null,h.enumTypes.enum,p.enumIcons.file)],isRequired:!0}),new d.SlashCommandNamedArgument("play","播放或暂停",[d.ARGUMENT_TYPE.STRING],!0,!1,"true",p.commonEnumProviders.boolean("trueFalse")())],helpString:"\n        <div>\n            控制音乐播放器或音效播放器的播放与暂停。\n        </div>\n        <div>\n            <strong>Example:</strong>\n            <ul>\n                <li>\n                    <pre><code>/audioplay type=bgm</code></pre>\n                    播放当前音乐。\n                </li>\n                <li>\n                    <pre><code>/audioplay type=ambient play=false</code></pre>\n                    暂停当前音效。\n                </li>\n            </ul>\n        </div>\n      "})),u.SlashCommandParser.addCommandObject(l.SlashCommand.fromProps({name:"audioenable",callback:_,namedArgumentList:[d.SlashCommandNamedArgument.fromProps({name:"type",description:"选择控制的播放器 (bgm 或 ambient)",typeList:[d.ARGUMENT_TYPE.STRING],enumList:[new h.SlashCommandEnumValue("bgm",null,h.enumTypes.enum,p.enumIcons.file),new h.SlashCommandEnumValue("ambient",null,h.enumTypes.enum,p.enumIcons.file)],isRequired:!0}),new d.SlashCommandNamedArgument("state","打开或关闭播放器",[d.ARGUMENT_TYPE.STRING],!1,!1,"true",p.commonEnumProviders.boolean("trueFalse")())],helpString:"\n        <div>\n            控制音乐播放器或音效播放器的开启与关闭。\n        </div>\n        <div>\n            <strong>Example:</strong>\n            <ul>\n                <li>\n                    <pre><code>/audioenable type=bgm state=true</code></pre>\n                    打开音乐播放器。\n                </li>\n                <li>\n                    <pre><code>/audioenable type=ambient state=false</code></pre>\n                    关闭音效播放器。\n                </li>\n            </ul>\n        </div>\n    "})),u.SlashCommandParser.addCommandObject(l.SlashCommand.fromProps({name:"audiomode",callback:g,namedArgumentList:[d.SlashCommandNamedArgument.fromProps({name:"type",description:"选择控制的播放器 (bgm 或 ambient)",typeList:[d.ARGUMENT_TYPE.STRING],enumList:[new h.SlashCommandEnumValue("bgm",null,h.enumTypes.enum,p.enumIcons.file),new h.SlashCommandEnumValue("ambient",null,h.enumTypes.enum,p.enumIcons.file)],isRequired:!0}),d.SlashCommandNamedArgument.fromProps({name:"mode",description:"选择播放模式",typeList:[d.ARGUMENT_TYPE.STRING],enumList:[new h.SlashCommandEnumValue("repeat",null,h.enumTypes.enum,p.enumIcons.loop),new h.SlashCommandEnumValue("random",null,h.enumTypes.enum,p.enumIcons.shuffle),new h.SlashCommandEnumValue("single",null,h.enumTypes.enum,p.enumIcons.redo),new h.SlashCommandEnumValue("stop",null,h.enumTypes.enum,p.enumIcons.stop)],isRequired:!0})],helpString:"\n        <div>\n            设置音频播放模式。\n        </div>\n        <div>\n            <strong>Example:</strong>\n            <ul>\n                <li>\n                    <pre><code>/audiomode type=bgm mode=repeat</code></pre>\n                    设置音乐为循环播放模式。\n                </li>\n                <li>\n                    <pre><code>/audiomode type=ambient mode=random</code></pre>\n                    设置音效为随机播放模式。\n                </li>\n                <li>\n                    <pre><code>/audiomode type=bgm mode=single</code></pre>\n                    设置音乐为单曲循环模式。\n                </li>\n                <li>\n                    <pre><code>/audiomode type=ambient mode=stop</code></pre>\n                    设置音效为停止播放模式。\n                </li>\n            </ul>\n        </div>\n    "}))}},190:(e,t,n)=>{var a=new Error;e.exports=new Promise((e,t)=>{if("undefined"!=typeof YAML)return e();n.l("https://fastly.jsdelivr.net/gh/N0VI028/JS-Slash-Runner/lib/yaml.min.js",n=>{if("undefined"!=typeof YAML)return e();var r=n&&("load"===n.type?"missing":n.type),i=n&&n.target&&n.target.src;a.message="Loading script failed.\n("+r+": "+i+")",a.name="ScriptExternalLoadError",a.type=r,a.request=i,t(a)},"YAML")}).then(()=>YAML)},231:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.d(t,{Ye:()=>E,_S:()=>S,eF:()=>c.eF,wl:()=>b});var r=n(5403),i=n(7203),s=n(3766),o=n(6491),c=n(9506),l=n(8020),d=n(6530),p=n(4365),h=n(4249),u=n(3892),f=n(363),m=n.n(f),g=e([l]);l=(g.then?(await g)():g)[0];const _=[p.event_types.CHAT_CHANGED],v=[p.event_types.CHARACTER_DELETED];class y{static instance;scriptManager;uiManager;initialized=!1;sendFormObserver=null;isUpdatingButtons=!1;constructor(){this.scriptManager=o.D.getInstance(),this.uiManager=l.F.getInstance(),this.registerEvents(),this.setupSendFormObserver()}static getInstance(){return y.instance||(y.instance=new y),y.instance}static destroyInstance(){y.instance&&(y.instance.cleanup(),y.instance=void 0,o.D.destroyInstance(),l.F.destroyInstance(),i.i.destroyInstance())}setupSendFormObserver(){this.sendFormObserver=new MutationObserver(e=>{if(this.isUpdatingButtons)return;let t=!1;e.forEach(e=>{if("childList"===e.type&&(e.addedNodes.forEach(e=>{if(e.nodeType===Node.ELEMENT_NODE){const n=e;"qr--bar"===n.id&&n.children.length>0&&(t=!0),(n.classList?.contains("qr--button")||n.classList?.contains("qr--buttons"))&&(t=!0)}}),e.removedNodes.forEach(e=>{if(e.nodeType===Node.ELEMENT_NODE){const n=e;(n.classList?.contains("qr--buttons")||"qr--bar"===n.id)&&(t=!0)}})),"attributes"===e.type&&e.target instanceof Element){const n=e.target;"qr--isEnabled"!==n.id&&"qr--isCombined"!==n.id||(t=!0)}}),t&&(this.sendFormObserver?.debounceTimer&&clearTimeout(this.sendFormObserver.debounceTimer),this.sendFormObserver.debounceTimer=setTimeout(()=>{this.handleSendFormChange()},250))}),this.startObservingSendForm()}startObservingSendForm(){const e=document.getElementById("send_form");e&&this.sendFormObserver?this.sendFormObserver.observe(e,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["checked","disabled"]}):e||setTimeout(()=>{this.startObservingSendForm()},1e3)}handleSendFormChange(){if(this.initialized&&!this.isUpdatingButtons)try{this.isUpdatingButtons=!0,(0,r.tN)()}catch(e){m().error("[ScriptManager] 处理send_form变化时出错:",e)}finally{setTimeout(()=>{this.isUpdatingButtons=!1},100)}}async initialize(){if(!this.initialized)try{await(0,u.loadFileToDocument)(`/scripts/extensions/${d.VE}/src/component/script_repository/public/style.css`,"css"),await this.uiManager.initialize(),this.initialized=!0}catch(e){m().error("[ScriptManager] 初始化失败:",e),this.initialized=!1}}registerEvents(){_.forEach(e=>{p.eventSource.makeFirst(e,this.refreshCharacterRepository.bind(this))}),v.forEach(e=>{p.eventSource.makeFirst(e,e=>(0,i.m)({character:e}))})}async refreshCharacterRepository(){if(!this.initialized)return;const e=[];$("#character-script-list").find(".script-item").each((t,n)=>{const a=$(n).attr("id");if(a){const t=this.scriptManager.getScriptById(a);t&&e.push(t)}}),this.scriptManager.stopScriptsByType(e,c.eF.CHARACTER);const t=i.i.getInstance(),n=this.scriptManager.getGlobalScripts(),a=this.scriptManager.getCharacterScripts();this.scriptManager.refreshCharacterScriptEnabledState(),m().info("[ScriptManager] 刷新角色脚本库"),p.this_chid&&a.length>0&&await this.uiManager.checkEmbeddedScripts(p.this_chid);const r=a.filter(e=>n.some(t=>t.id===e.id));if(r.length>0){m().info(`[ScriptManager] 发现${r.length}个脚本ID冲突`);for(const e of r){const r=n.find(t=>t.id===e.id),i=await(0,h.callGenericPopup)(`全局脚本中已存在 "${r.name}" 脚本，是否关闭冲突脚本？`,h.POPUP_TYPE.TEXT,"",{okButton:"关闭全局",cancelButton:"关闭局部"});e.id=(0,u.uuidv4)(),i?r.enabled&&(await this.scriptManager.stopScript(r,c.eF.GLOBAL),r.enabled=!1,m().info(`[ScriptManager] 关闭全局脚本: ${r.name}`),s.hj.emit(s._9.UI_REFRESH,{action:"script_toggle",script:r,type:c.eF.GLOBAL,enable:!1}),t.saveGlobalScripts(n)):e.enabled&&(e.enabled=!1,await t.saveCharacterScripts(a),m().info(`[ScriptManager] 关闭局部脚本: ${e.name}`))}}s.hj.emit(s._9.UI_REFRESH,{action:"refresh_charact_scripts"}),await this.scriptManager.runScriptsByType(a,c.eF.CHARACTER)}async cleanup(){try{this.sendFormObserver&&(this.sendFormObserver.disconnect(),this.sendFormObserver.debounceTimer&&clearTimeout(this.sendFormObserver.debounceTimer),this.sendFormObserver=null),this.isUpdatingButtons=!1,_.forEach(e=>{p.eventSource.removeListener(e,this.refreshCharacterRepository.bind(this))}),await this.scriptManager.cleanup(),this.uiManager.cleanup(),this.initialized=!1,m().info("[ScriptManager] 清理完成")}catch(e){m().error("[ScriptManager] 清理失败:",e)}}}async function b(){const e=y.getInstance();await e.initialize()}async function w(){y.destroyInstance()}async function E(){await b()}function S(){w()}a()}catch(C){a(C)}})},363:function(e,t,n){var a,r;a=function(){var e=function(){},t="undefined",n=typeof window!==t&&typeof window.navigator!==t&&/Trident\/|MSIE /.test(window.navigator.userAgent),a=["trace","debug","info","warn","error"],r={},i=null;function s(e,t){var n=e[t];if("function"==typeof n.bind)return n.bind(e);try{return Function.prototype.bind.call(n,e)}catch(t){return function(){return Function.prototype.apply.apply(n,[e,arguments])}}}function o(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function c(a){return"debug"===a&&(a="log"),typeof console!==t&&("trace"===a&&n?o:void 0!==console[a]?s(console,a):void 0!==console.log?s(console,"log"):e)}function l(){for(var n=this.getLevel(),r=0;r<a.length;r++){var i=a[r];this[i]=r<n?e:this.methodFactory(i,n,this.name)}if(this.log=this.debug,typeof console===t&&n<this.levels.SILENT)return"No console available for logging"}function d(e){return function(){typeof console!==t&&(l.call(this),this[e].apply(this,arguments))}}function p(e,t,n){return c(e)||d.apply(this,arguments)}function h(e,n){var s,o,c,d=this,h="loglevel";function u(e){var n=(a[e]||"silent").toUpperCase();if(typeof window!==t&&h){try{return void(window.localStorage[h]=n)}catch(e){}try{window.document.cookie=encodeURIComponent(h)+"="+n+";"}catch(e){}}}function f(){var e;if(typeof window!==t&&h){try{e=window.localStorage[h]}catch(e){}if(typeof e===t)try{var n=window.document.cookie,a=encodeURIComponent(h),r=n.indexOf(a+"=");-1!==r&&(e=/^([^;]+)/.exec(n.slice(r+a.length+1))[1])}catch(e){}return void 0===d.levels[e]&&(e=void 0),e}}function m(){if(typeof window!==t&&h){try{window.localStorage.removeItem(h)}catch(e){}try{window.document.cookie=encodeURIComponent(h)+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC"}catch(e){}}}function g(e){var t=e;if("string"==typeof t&&void 0!==d.levels[t.toUpperCase()]&&(t=d.levels[t.toUpperCase()]),"number"==typeof t&&t>=0&&t<=d.levels.SILENT)return t;throw new TypeError("log.setLevel() called with invalid level: "+e)}"string"==typeof e?h+=":"+e:"symbol"==typeof e&&(h=void 0),d.name=e,d.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},d.methodFactory=n||p,d.getLevel=function(){return null!=c?c:null!=o?o:s},d.setLevel=function(e,t){return c=g(e),!1!==t&&u(c),l.call(d)},d.setDefaultLevel=function(e){o=g(e),f()||d.setLevel(e,!1)},d.resetLevel=function(){c=null,m(),l.call(d)},d.enableAll=function(e){d.setLevel(d.levels.TRACE,e)},d.disableAll=function(e){d.setLevel(d.levels.SILENT,e)},d.rebuild=function(){if(i!==d&&(s=g(i.getLevel())),l.call(d),i===d)for(var e in r)r[e].rebuild()},s=g(i?i.getLevel():"WARN");var _=f();null!=_&&(c=g(_)),l.call(d)}(i=new h).getLogger=function(e){if("symbol"!=typeof e&&"string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");var t=r[e];return t||(t=r[e]=new h(e,i.methodFactory)),t};var u=typeof window!==t?window.log:void 0;return i.noConflict=function(){return typeof window!==t&&window.log===i&&(window.log=u),i},i.getLoggers=function(){return r},i.default=i,i},void 0===(r="function"==typeof a?a.call(t,n,t,e):a)||(e.exports=r)},434:(e,t,a)=>{e.exports=(e=>{var t={};return a.d(t,e),t})({flushEphemeralStoppingStrings:()=>n.flushEphemeralStoppingStrings,persona_description_positions:()=>n.persona_description_positions,power_user:()=>n.power_user})},476:(e,t,n)=>{e.exports=(e=>{var t={};return n.d(t,e),t})({MacrosParser:()=>a.MacrosParser,getLastMessageId:()=>a.getLastMessageId})},515:(e,t,n)=>{n.d(t,{j:()=>i,m:()=>r});const a=hljs.highlightElement;function r(){hljs.highlightElement=function(){}}function i(){hljs.highlightElement=a}},1033:(e,t,n)=>{n.d(t,{V:()=>o});var a=n(5330),r=n(9836),i=n(363),s=n.n(i);async function o(e){if(!e.data)return;const t=a.XK[e.data.request];if(!t)return;let n;try{n=await t(e)}catch(t){const n=t;toastr.error(`${(0,a.Jn)(e)}${n.name+": "+n.message}${n.stack?n.stack:""}`),s().error((0,a.Jn)(e),n)}finally{e.source.postMessage({request:e.data.request+"_callback",uid:e.data.uid,result:n},{targetOrigin:"*"})}}(0,r.Ah)()},1054:(e,t,n)=>{n.d(t,{J:()=>a});const a=['"use strict";\nconst SillyTavern = window.parent.SillyTavern.getContext();\nconst TavernHelper = window.parent.TavernHelper;\nfor (const key in TavernHelper) {\n    window[key] = TavernHelper[key];\n}\nconst toastr = window.parent.toastr;\nconst log = window.parent.log;\nconst EjsTemplate = window.parent.EjsTemplate;\n',"\"use strict\";\n// @ts-expect-error\nvar detail;\n(function (detail) {\n    async function make_iframe_promise(message) {\n        return new Promise((resolve, _) => {\n            const uid = Date.now() + Math.random();\n            function handleMessage(event) {\n                if (event.data?.request === message.request + '_callback' && event.data.uid == uid) {\n                    window.removeEventListener('message', handleMessage);\n                    resolve(event.data.result);\n                }\n            }\n            window.addEventListener('message', handleMessage);\n            window.parent.postMessage({\n                uid: uid,\n                ...message,\n            }, '*');\n        });\n    }\n    detail.make_iframe_promise = make_iframe_promise;\n    function format_function_to_string(fn) {\n        const string = fn.toString();\n        const index = string.indexOf('\\n');\n        if (index > -1) {\n            return string.slice(0, index);\n        }\n        else {\n            return string;\n        }\n    }\n    detail.format_function_to_string = format_function_to_string;\n    function get_or_set(map, key, defaulter) {\n        const existing_value = map.get(key);\n        if (existing_value) {\n            return existing_value;\n        }\n        const default_value = defaulter();\n        map.set(key, default_value);\n        return default_value;\n    }\n    detail.get_or_set = get_or_set;\n    function extract(map, key) {\n        const value = map.get(key);\n        if (!value) {\n            return undefined;\n        }\n        map.delete(key);\n        return value;\n    }\n    detail.extract = extract;\n})(detail || (detail = {}));\n","\"use strict\";\n// 命名为 _multimap.ts, 保证它被插入在开头, 虽然最好换一种 iframe 内代码的做法\nclass Multimap {\n    size_ = 0;\n    map = new Map();\n    operator;\n    constructor(operator, iterable) {\n        this.operator = operator;\n        if (iterable) {\n            for (const [key, value] of iterable) {\n                this.put(key, value);\n            }\n        }\n        return this;\n    }\n    get size() {\n        return this.size_;\n    }\n    get(key) {\n        const values = this.map.get(key);\n        if (values) {\n            return this.operator.clone(values);\n        }\n        else {\n            return this.operator.create();\n        }\n    }\n    put(key, value) {\n        let values = this.map.get(key);\n        if (!values) {\n            values = this.operator.create();\n        }\n        if (!this.operator.add(value, values)) {\n            return false;\n        }\n        this.map.set(key, values);\n        this.size_++;\n        return true;\n    }\n    putAll(arg1, arg2) {\n        let pushed = 0;\n        if (arg2) {\n            const key = arg1;\n            const values = arg2;\n            for (const value of values) {\n                this.put(key, value);\n                pushed++;\n            }\n        }\n        else if (arg1 instanceof Multimap) {\n            for (const [key, value] of arg1.entries()) {\n                this.put(key, value);\n                pushed++;\n            }\n        }\n        else {\n            throw new TypeError('unexpected arguments');\n        }\n        return pushed > 0;\n    }\n    has(key) {\n        return this.map.has(key);\n    }\n    hasEntry(key, value) {\n        return this.operator.has(value, this.get(key));\n    }\n    delete(key) {\n        this.size_ -= this.operator.size(this.get(key));\n        return this.map.delete(key);\n    }\n    deleteEntry(key, value) {\n        const current = this.get(key);\n        if (!this.operator.delete(value, current)) {\n            return false;\n        }\n        this.map.set(key, current);\n        this.size_--;\n        return true;\n    }\n    clear() {\n        this.map.clear();\n        this.size_ = 0;\n    }\n    keys() {\n        return this.map.keys();\n    }\n    entries() {\n        const self = this;\n        function* gen() {\n            for (const [key, values] of self.map.entries()) {\n                for (const value of values) {\n                    yield [key, value];\n                }\n            }\n        }\n        return gen();\n    }\n    values() {\n        const self = this;\n        function* gen() {\n            for (const [, value] of self.entries()) {\n                yield value;\n            }\n        }\n        return gen();\n    }\n    forEach(callback, thisArg) {\n        for (const [key, value] of this.entries()) {\n            callback.call(thisArg === undefined ? this : thisArg, value, key, this);\n        }\n    }\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n    asMap() {\n        const ret = new Map();\n        for (const key of this.keys()) {\n            ret.set(key, this.operator.clone(this.get(key)));\n        }\n        return ret;\n    }\n}\nclass ArrayMultimap extends Multimap {\n    constructor(iterable) {\n        super(new ArrayOperator(), iterable);\n    }\n    get [Symbol.toStringTag]() {\n        return 'ArrayMultimap';\n    }\n}\nclass ArrayOperator {\n    create() {\n        return [];\n    }\n    clone(collection) {\n        return collection.slice();\n    }\n    add(value, collection) {\n        collection.push(value);\n        return true;\n    }\n    size(collection) {\n        return collection.length;\n    }\n    delete(value, collection) {\n        const index = collection.indexOf(value);\n        if (index > -1) {\n            collection.splice(index, 1);\n            return true;\n        }\n        return false;\n    }\n    has(value, collection) {\n        return collection.includes(value);\n    }\n}\nclass SetMultimap extends Multimap {\n    constructor(iterable) {\n        super(new SetOperator(), iterable);\n    }\n    get [Symbol.toStringTag]() {\n        return 'SetMultimap';\n    }\n}\nclass SetOperator {\n    create() {\n        return new Set();\n    }\n    clone(collection) {\n        return new Set(collection);\n    }\n    add(value, collection) {\n        const prev = collection.size;\n        collection.add(value);\n        return prev !== collection.size;\n    }\n    size(collection) {\n        return collection.size;\n    }\n    delete(value, collection) {\n        return collection.delete(value);\n    }\n    has(value, collection) {\n        return collection.has(value);\n    }\n}\n","\"use strict\";\nfunction eventOn(event_type, listener) {\n    if (detail.try_get_wrapper(listener, event_type)) {\n        log.warn(`[Event][eventOn] 函数已经在监听 '${event_type}' 事件, 调用无效\\n\\n  ${detail.format_function_to_string(listener)}`);\n        return;\n    }\n    SillyTavern.eventSource.on(event_type, detail.get_or_make_wrapper(listener, event_type, false));\n    log.info(`[Event][eventOn] 函数开始监听 '${event_type}' 事件并将随事件触发\\n\\n  ${detail.format_function_to_string(listener)}`);\n}\nfunction eventOnButton(event_type, listener) {\n    const script_id = getScriptId();\n    if (detail.try_get_wrapper(listener, event_type)) {\n        log.warn(`[Event][eventOnButton](id为${String(script_id)}) 的脚本已经在监听 '${event_type}' 事件, 调用无效\\n\\n  ${detail.format_function_to_string(listener)}`);\n        return;\n    }\n    const event_type_with_script_id = `${String(script_id)}_${event_type}`;\n    SillyTavern.eventSource.on(event_type_with_script_id, detail.get_or_make_wrapper(listener, event_type_with_script_id, false));\n    log.info(`[Event][eventOnButton](script_id为${String(script_id)}) 函数开始监听 '${event_type}' 事件并将随事件触发\\n\\n  ${detail.format_function_to_string(listener)}`);\n}\nfunction eventMakeLast(event_type, listener) {\n    const is_listening = detail.try_get_wrapper(listener, event_type) !== undefined;\n    SillyTavern.eventSource.makeLast(event_type, detail.get_or_make_wrapper(listener, event_type, false));\n    if (is_listening) {\n        log.info(`[Event][eventMakeLast](${getIframeName()}) 函数调整为监听到 '${event_type}' 事件时最后触发\\n\\n  ${detail.format_function_to_string(listener)}`);\n    }\n    else {\n        log.info(`[Event][eventMakeLast](${getIframeName()}) 函数开始监听 '${event_type}' 事件并将随事件最后触发\\n\\n  ${detail.format_function_to_string(listener)}`);\n    }\n}\nfunction eventMakeFirst(event_type, listener) {\n    const is_listening = detail.try_get_wrapper(listener, event_type) !== undefined;\n    SillyTavern.eventSource.makeFirst(event_type, detail.get_or_make_wrapper(listener, event_type, false));\n    if (is_listening) {\n        log.info(`[Event][eventMakeFirst](${getIframeName()}) 函数调整为监听到 '${event_type}' 事件时最先触发\\n\\n  ${detail.format_function_to_string(listener)}`);\n    }\n    else {\n        log.info(`[Event][eventMakeFirst](${getIframeName()}) 函数开始监听 '${event_type}' 事件并将随事件最先触发\\n\\n  ${detail.format_function_to_string(listener)}`);\n    }\n}\nfunction eventOnce(event_type, listener) {\n    if (detail.try_get_wrapper(listener, event_type)) {\n        log.warn(`[Event][eventOnce](${getIframeName()}) 函数已经在监听 '${event_type}' 事件, 调用无效\\n\\n  ${detail.format_function_to_string(listener)}`);\n        return;\n    }\n    SillyTavern.eventSource.once(event_type, detail.get_or_make_wrapper(listener, event_type, true));\n    log.info(`[Event][eventOnce](${getIframeName()}) 函数开始监听下一次 '${event_type}' 事件并仅在该次事件时触发\\n\\n  ${detail.format_function_to_string(listener)}`);\n}\nasync function eventWaitOnce(event_type, listener) {\n    if (!listener) {\n        const do_nothing = () => { };\n        eventOnce(event_type, do_nothing);\n        return await eventWaitOnce(event_type, do_nothing);\n    }\n    const entry = `${event_type}#${listener.toString()}`;\n    return new Promise((resolve, _) => {\n        const uid = Date.now() + Math.random();\n        function handleMessage(event) {\n            if (event.data?.request === 'iframe_event_wait_callback' && event.data.uid == uid) {\n                window.removeEventListener('message', handleMessage);\n                resolve(event.data.result);\n                detail.waiting_event_map.deleteEntry(entry, uid);\n                log.info(`[Event][eventWaitOnce](${getIframeName()}) 等待到函数因 '${event_type}' 事件触发后的执行结果: ${JSON.stringify(event.data.result)}\\n\\n  ${detail.format_function_to_string(listener)}`);\n            }\n        }\n        window.addEventListener('message', handleMessage);\n        detail.waiting_event_map.put(entry, uid);\n        log.info(`[Event][eventWaitOnce](${getIframeName()}) 等待函数被 '${event_type}' 事件触发\\n\\n  ${detail.format_function_to_string(listener)}`);\n    });\n}\nasync function eventEmit(event_type, ...data) {\n    await SillyTavern.eventSource.emit(event_type, ...data);\n    log.info(`[Event][eventEmit](${getIframeName()}) 发送 '${event_type}' 事件, 携带数据: ${JSON.stringify(data)}`);\n}\nfunction eventEmitAndWait(event_type, ...data) {\n    SillyTavern.eventSource.emitAndWait(event_type, ...data);\n    log.info(`[Event][eventEmitAndWait](${getIframeName()}) 发送 '${event_type}' 事件, 携带数据: ${JSON.stringify(data)}`);\n}\nfunction eventRemoveListener(event_type, listener) {\n    const wrapper = detail.try_get_wrapper(listener, event_type);\n    if (!wrapper) {\n        log.warn(`[Event][eventRemoveListener](${getIframeName()}) 函数没有监听 '${event_type}' 事件, 调用无效\\n\\n  ${detail.format_function_to_string(listener)}`);\n        return;\n    }\n    SillyTavern.eventSource.removeListener(event_type, wrapper);\n    detail.remove_wrapper(listener, event_type);\n    log.info(`[Event][eventRemoveListener](${getIframeName()}) 函数不再监听 '${event_type}' 事件\\n\\n  ${detail.format_function_to_string(listener)}`);\n}\nfunction eventClearEvent(event_type) {\n    detail.listener_event_wrapper_map.forEach((event_wrapper_map, _) => {\n        const wrapper = event_wrapper_map.get(event_type);\n        if (wrapper) {\n            SillyTavern.eventSource.removeListener(event_type, wrapper);\n            event_wrapper_map.delete(event_type);\n        }\n    });\n    log.info(`[Event][eventClearEvent](${getIframeName()})所有函数都不再监听 '${event_type}' 事件`);\n}\nfunction eventClearListener(listener) {\n    const event_callback_map = detail.extract(detail.listener_event_wrapper_map, listener);\n    if (event_callback_map) {\n        event_callback_map.forEach((callback, event_type) => {\n            SillyTavern.eventSource.removeListener(event_type, callback);\n        });\n    }\n    log.info(`[Event][eventClearListener](${getIframeName()}) 函数不再监听任何事件\\n\\n  ${detail.format_function_to_string(listener)}`);\n}\nfunction eventClearAll() {\n    detail.listener_event_wrapper_map.forEach((event_wrapper_map, _) => {\n        event_wrapper_map.forEach((wrapper, event_type) => {\n            SillyTavern.eventSource.removeListener(event_type, wrapper);\n        });\n    });\n    detail.listener_event_wrapper_map.clear();\n    log.info(`[Event][eventClearAll](${getIframeName()}) 取消所有函数对所有事件的监听`);\n}\nconst iframe_events = {\n    MESSAGE_IFRAME_RENDER_STARTED: 'message_iframe_render_started',\n    MESSAGE_IFRAME_RENDER_ENDED: 'message_iframe_render_ended',\n    GENERATION_STARTED: 'js_generation_started',\n    STREAM_TOKEN_RECEIVED_FULLY: 'js_stream_token_received_fully',\n    STREAM_TOKEN_RECEIVED_INCREMENTALLY: 'js_stream_token_received_incrementally',\n    GENERATION_ENDED: 'js_generation_ended',\n};\nconst tavern_events = {\n    APP_READY: 'app_ready',\n    EXTRAS_CONNECTED: 'extras_connected',\n    MESSAGE_SWIPED: 'message_swiped',\n    MESSAGE_SENT: 'message_sent',\n    MESSAGE_RECEIVED: 'message_received',\n    MESSAGE_EDITED: 'message_edited',\n    MESSAGE_DELETED: 'message_deleted',\n    MESSAGE_UPDATED: 'message_updated',\n    MESSAGE_FILE_EMBEDDED: 'message_file_embedded',\n    IMPERSONATE_READY: 'impersonate_ready',\n    CHAT_CHANGED: 'chat_id_changed',\n    GENERATION_AFTER_COMMANDS: 'GENERATION_AFTER_COMMANDS',\n    GENERATION_STARTED: 'generation_started',\n    GENERATION_STOPPED: 'generation_stopped',\n    GENERATION_ENDED: 'generation_ended',\n    EXTENSIONS_FIRST_LOAD: 'extensions_first_load',\n    EXTENSION_SETTINGS_LOADED: 'extension_settings_loaded',\n    SETTINGS_LOADED: 'settings_loaded',\n    SETTINGS_UPDATED: 'settings_updated',\n    GROUP_UPDATED: 'group_updated',\n    MOVABLE_PANELS_RESET: 'movable_panels_reset',\n    SETTINGS_LOADED_BEFORE: 'settings_loaded_before',\n    SETTINGS_LOADED_AFTER: 'settings_loaded_after',\n    CHATCOMPLETION_SOURCE_CHANGED: 'chatcompletion_source_changed',\n    CHATCOMPLETION_MODEL_CHANGED: 'chatcompletion_model_changed',\n    OAI_PRESET_CHANGED_BEFORE: 'oai_preset_changed_before',\n    OAI_PRESET_CHANGED_AFTER: 'oai_preset_changed_after',\n    OAI_PRESET_EXPORT_READY: 'oai_preset_export_ready',\n    OAI_PRESET_IMPORT_READY: 'oai_preset_import_ready',\n    WORLDINFO_SETTINGS_UPDATED: 'worldinfo_settings_updated',\n    WORLDINFO_UPDATED: 'worldinfo_updated',\n    CHARACTER_EDITED: 'character_edited',\n    CHARACTER_PAGE_LOADED: 'character_page_loaded',\n    CHARACTER_GROUP_OVERLAY_STATE_CHANGE_BEFORE: 'character_group_overlay_state_change_before',\n    CHARACTER_GROUP_OVERLAY_STATE_CHANGE_AFTER: 'character_group_overlay_state_change_after',\n    USER_MESSAGE_RENDERED: 'user_message_rendered',\n    CHARACTER_MESSAGE_RENDERED: 'character_message_rendered',\n    FORCE_SET_BACKGROUND: 'force_set_background',\n    CHAT_DELETED: 'chat_deleted',\n    CHAT_CREATED: 'chat_created',\n    GROUP_CHAT_DELETED: 'group_chat_deleted',\n    GROUP_CHAT_CREATED: 'group_chat_created',\n    GENERATE_BEFORE_COMBINE_PROMPTS: 'generate_before_combine_prompts',\n    GENERATE_AFTER_COMBINE_PROMPTS: 'generate_after_combine_prompts',\n    GENERATE_AFTER_DATA: 'generate_after_data',\n    GROUP_MEMBER_DRAFTED: 'group_member_drafted',\n    WORLD_INFO_ACTIVATED: 'world_info_activated',\n    TEXT_COMPLETION_SETTINGS_READY: 'text_completion_settings_ready',\n    CHAT_COMPLETION_SETTINGS_READY: 'chat_completion_settings_ready',\n    CHAT_COMPLETION_PROMPT_READY: 'chat_completion_prompt_ready',\n    CHARACTER_FIRST_MESSAGE_SELECTED: 'character_first_message_selected',\n    // TODO: Naming convention is inconsistent with other events\n    CHARACTER_DELETED: 'characterDeleted',\n    CHARACTER_DUPLICATED: 'character_duplicated',\n    STREAM_TOKEN_RECEIVED: 'stream_token_received',\n    FILE_ATTACHMENT_DELETED: 'file_attachment_deleted',\n    WORLDINFO_FORCE_ACTIVATE: 'worldinfo_force_activate',\n    OPEN_CHARACTER_LIBRARY: 'open_character_library',\n    ONLINE_STATUS_CHANGED: 'online_status_changed',\n    IMAGE_SWIPED: 'image_swiped',\n    CONNECTION_PROFILE_LOADED: 'connection_profile_loaded',\n    TOOL_CALLS_PERFORMED: 'tool_calls_performed',\n    TOOL_CALLS_RENDERED: 'tool_calls_rendered',\n};\n//------------------------------------------------------------------------------------------------------------------------\n// @ts-expect-error\nvar detail;\n(function (detail) {\n    detail.listener_event_wrapper_map = new Map();\n    function try_get_wrapper(listener, event_type) {\n        return detail.listener_event_wrapper_map.get(listener)?.get(event_type);\n    }\n    detail.try_get_wrapper = try_get_wrapper;\n    function remove_wrapper(listener, event_type) {\n        detail.listener_event_wrapper_map.get(listener)?.delete(event_type);\n    }\n    detail.remove_wrapper = remove_wrapper;\n    function get_or_make_wrapper(listener, event_type, once) {\n        const default_wrapper = async (...args) => {\n            if (once) {\n                remove_wrapper(listener, event_type);\n            }\n            const result = await listener(...args);\n            log.info(`[Event][callback '${event_type}'](${getIframeName()}) 函数因监听到 '${event_type}' 事件而触发\\n\\n  ${detail.format_function_to_string(listener)}`);\n            const uid = detail.waiting_event_map.get(`${event_type}#${listener.toString()}`)[0];\n            if (uid) {\n                window.postMessage({\n                    request: 'iframe_event_wait_callback',\n                    uid: uid,\n                    result: result,\n                }, '*');\n            }\n            return result;\n        };\n        const default_event_wrapper_map = new Map([[event_type, default_wrapper]]);\n        const event_wrapper = detail.get_or_set(detail.listener_event_wrapper_map, listener, () => default_event_wrapper_map);\n        const wrapper = detail.get_or_set(event_wrapper, event_type, () => default_wrapper);\n        return wrapper;\n    }\n    detail.get_or_make_wrapper = get_or_make_wrapper;\n    detail.waiting_event_map = new ArrayMultimap();\n    $(window).on('unload', eventClearAll);\n})(detail || (detail = {}));\n","\"use strict\";\nfunction getAllVariables() {\n    const is_message_iframe = getIframeName().startsWith('message-iframe');\n    let data = _.merge({}, SillyTavern.extensionSettings.variables.global, SillyTavern.characters[SillyTavern.characterId]?.data?.extensions?.TavernHelper_characterScriptVariables);\n    if (!is_message_iframe) {\n        data = _.merge(data, TavernHelper.getVariables('script', getScriptId()));\n    }\n    data = _.merge(data, SillyTavern.chatMetadata.variables);\n    if (is_message_iframe) {\n        data = _.merge(data, ...SillyTavern.chat\n            .slice(0, getCurrentMessageId())\n            .map((chat_message) => chat_message?.variables?.[chat_message?.swipe_id ?? 0]));\n    }\n    return structuredClone(data);\n}\n//------------------------------------------------------------------------------------------------------------------------\n// 已被弃用的接口, 请尽量按照指示更新它们\n/** @deprecated 这个函数是在事件监听功能之前制作的, 现在请使用 `insertOrAssignVariables` 而用事件监听或条件判断来控制怎么更新 */\nasync function setVariables(message_id, new_or_updated_variables) {\n    let actual_message_id;\n    let actual_variables;\n    if (new_or_updated_variables) {\n        actual_message_id = message_id;\n        actual_variables = new_or_updated_variables;\n    }\n    else {\n        actual_message_id = getCurrentMessageId();\n        actual_variables = message_id;\n    }\n    if (typeof actual_message_id !== 'number' || typeof actual_variables !== 'object') {\n        return;\n    }\n    return detail.make_iframe_promise({\n        request: '[Variables][setVariables]',\n        message_id: actual_message_id,\n        variables: actual_variables,\n    });\n}\n","\"use strict\";\nfunction getIframeName() {\n    return window.frameElement.id;\n}\nfunction getScriptId() {\n    return $(window.frameElement).attr('script-id') ?? 'unknown_script';\n}\nfunction getCurrentMessageId() {\n    return getMessageId(getIframeName());\n}\nfunction getMessageId(iframe_name) {\n    const match = iframe_name.match(/^message-iframe-(\\d+)-\\d+$/);\n    if (!match) {\n        throw Error(`获取 ${iframe_name} 所在楼层 id 时出错: 不要对全局脚本 iframe 调用 getMessageId!`);\n    }\n    return parseInt(match[1].toString());\n}\n"].join("\n")},1320:(e,t,n)=>{e.exports=(e=>{var t={};return n.d(t,e),t})({SlashCommand:()=>r.SlashCommand})},1553:(e,t,n)=>{e.exports=(e=>{var t={};return n.d(t,e),t})({isMobile:()=>i.isMobile})},1613:(e,t,n)=>{n.d(t,{w:()=>a});class a{static inferDataType(e){return Array.isArray(e)?"array":"boolean"==typeof e?"boolean":"number"==typeof e?"number":"object"==typeof e&&null!==e?"object":"string"}static getDefaultValueForType(e){switch(e){case"string":default:return"";case"number":return 0;case"boolean":return!1;case"array":return[];case"object":return{}}}static generateUniqueKey(e,t="newKey"){let n=t,a=1;for(;e.has(n);)n=`${t}${a}`,a++;return n}}},1988:(e,t,n)=>{n.d(t,{S1:()=>f,UN:()=>l,lC:()=>p,qk:()=>d,rT:()=>h,v1:()=>u});var a=n(6491),r=n(7631),i=n(4365),s=n(9489),o=n(363),c=n.n(o);function l({type:e="chat",message_id:t="latest",script_id:n}={}){const o=function({type:e="chat",message_id:t="latest",script_id:n}){switch(e){case"message":if("latest"!==t&&(t<-i.chat.length||t>=i.chat.length))throw Error(`提供的 message_id(${t}) 超出了聊天消息楼层号范围`);return t="latest"===t?-1:t,(0,r.Zm)(t)[0].data;case"chat":{const e=i.chat_metadata;return e.variables||(e.variables={}),e.variables}case"character":return i.characters[i.this_chid]?.data?.extensions?.TavernHelper_characterScriptVariables||{};case"global":return s.extension_settings.variables.global;case"script":if(!n)throw Error("获取脚本变量失败, 未指定 script_id");return a.D.getInstance().getScriptVariables(n)}}({type:e,message_id:t,script_id:n});return c().info(`获取${"message"===e?`'${t}' 消息`:"chat"===e?"聊天":"character"===e?"角色":"script"===e?`'${n}' 脚本`:"全局"}变量表:\n${JSON.stringify(o)}`),structuredClone(o)}async function d(e,{type:t="chat",message_id:n="latest",script_id:o}={}){switch(t){case"message":if("latest"!==n&&(n<-i.chat.length||n>=i.chat.length))throw Error(`提供的 message_id(${n}) 超出了聊天消息楼层号范围`);n="latest"===n?i.chat.length-1:n<0?i.chat.length+n:n,await(0,r.jf)([{message_id:n,data:e}],{refresh:"none"});break;case"chat":_.set(i.chat_metadata,"variables",e),await(0,i.saveMetadata)();break;case"character":if(!i.this_chid)throw new Error("保存变量失败，当前角色为空");await(0,s.writeExtensionField)(i.this_chid,"TavernHelper_characterScriptVariables",e),i.eventSource.emit("character_variables_changed",{variables:e});break;case"global":_.set(s.extension_settings.variables,"global",e),await(0,i.saveSettings)();break;case"script":if(!o)throw Error("保存变量失败, 未指定 script_id");{const t=a.D.getInstance(),n=t.getScriptById(o);if(!n)throw Error(`'${o}' 脚本不存在`);const r=t.scriptData.getScriptType(n);await t.updateScriptVariables(o,e,r)}}c().info(`将${"message"===t?`'${n}' 消息`:"chat"===t?"聊天":"character"===t?"角色":"script"===t?`'${o}' 脚本`:"全局"}变量表替换为:\n${JSON.stringify(e)}`)}async function p(e,{type:t="chat",message_id:n="latest",script_id:a}={}){let r=l({type:t,message_id:n,script_id:a});return r=await e(r),c().info(`对${"message"===t?`'${n}' 消息`:"chat"===t?"聊天":"character"===t?"角色":"script"===t?`'${a}' 脚本`:"全局"}变量表进行更新`),await d(r,{type:t,message_id:n,script_id:a}),r}async function h(e,{type:t="chat",message_id:n="latest",script_id:a}={}){await p(t=>_.merge(t,e),{type:t,message_id:n,script_id:a})}async function u(e,{type:t="chat",message_id:n="latest",script_id:a}={}){await p(t=>_.defaultsDeep(t,e),{type:t,message_id:n,script_id:a})}async function f(e,{type:t="chat",message_id:n="latest",script_id:a}={}){let r=!1;return await p(t=>(r=_.unset(t,e),t),{type:t,message_id:n,script_id:a}),r}},2361:(e,t,n)=>{e.exports=(e=>{var t={};return n.d(t,e),t})({ARGUMENT_TYPE:()=>s.ARGUMENT_TYPE,SlashCommandArgument:()=>s.SlashCommandArgument,SlashCommandNamedArgument:()=>s.SlashCommandNamedArgument})},2445:(e,t,n)=>{n.d(t,{G:()=>r});var a=n(4365);function r(e){let t=(0,a.reloadMarkdownProcessor)().makeHtml(e);return t=`<div style="text-align: left; max-height:80dvh; overflow-y: auto;">${t}</div>`,t}},2557:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.d(t,{W:()=>w});var r=n(9489),i=n(3892),s=n(9),o=n(7636),c=n(7810),l=n(9736),d=n(6530),p=n(363),h=n.n(p),u=e([s,l]);[s,l]=u.then?(await u)():u;const f=`${d.VE}/src/component/variable_manager/public`;let m=null,g=null,_=null,v=null;async function y(){await(0,i.loadFileToDocument)(`/scripts/extensions/${f}/style.css`,"css");const e=$(await(0,r.renderExtensionTemplateAsync)(`${f}`,"index"));_=new o.c,m=new l.G(e),v=new c.h(m,_),g=new s.$(_,m,v,m.cardFactory),m.setController(g),await g.init(e),m?m.render():h().error("[VariableManager] 变量管理器未初始化")}function b(){const e=$('\n  <div id="tavern-helper-variable-container" class="list-group-item flex-container flexGap5 interactable">\n      <div class="fa-solid fa-square-root-variable extensionsMenuExtensionButton" /></div>\n      <span id="tavern-helper-variable-text">变量管理器</span>\n  </div>');e.css("display","flex"),$("#extensionsMenu").append(e),$("#tavern-helper-variable-container").on("click",async function(){await y()})}function w(){b();const e=$("#open-variable-manager");e.length&&e.on("click",async()=>{await y()})}a()}catch(E){a(E)}})},2730:e=>{e.exports=o},3008:(e,t,n)=>{n.a(e,async(e,t)=>{try{var a=n(7517),r=n(3385),i=n(5731),s=n(6912),o=n(8961),c=n(8242),l=n(231),d=n(9506),p=n(2557),h=n(9871),u=n(162),f=n(9880),m=n(4925),g=n(8337),v=n(6530),y=n(4365),b=n(9489),w=n(363),E=n.n(w),S=n(4280),C=e([r,s,l,p]);[r,s,l,p]=C.then?(await C)():C;const T={enabled_extension:!0,render:{...o.FF},script:{...d.y8},audio:{...a.Dt},debug:{enabled:!1}},A=`${v.VE}/src/component`;function x(e){let t=$(e.currentTarget).attr("id");if(void 0!==t)switch(t=t.replace("-settings-title",""),$("#main-settings-title").removeClass("title-item-active"),$("#render-settings-title").removeClass("title-item-active"),$("#script-settings-title").removeClass("title-item-active"),$("#toolbox-settings-title").removeClass("title-item-active"),$("#main-settings-content").hide(),$("#render-settings-content").hide(),$("#script-settings-content").hide(),$("#toolbox-settings-content").hide(),t){case"main":$("#main-settings-title").addClass("title-item-active"),$("#main-settings-content").show();break;case"render":$("#render-settings-title").addClass("title-item-active"),$("#render-settings-content").show();break;case"script":$("#script-settings-title").addClass("title-item-active"),$("#script-settings-content").show();break;case"toolbox":$("#toolbox-settings-title").addClass("title-item-active"),$("#toolbox-settings-content").show()}}async function I(){const e=await(0,b.renderExtensionTemplateAsync)(`${v.VE}`,"index");$("#extensions_settings").append(e);const t=$(await(0,b.renderExtensionTemplateAsync)(`${A}/script_repository/public`,"index"));$("#script-settings-content").append(t);const n=$(await(0,b.renderExtensionTemplateAsync)(`${A}/message_iframe`,"index"));$("#render-settings-content").append(n);const a=$(await(0,b.renderExtensionTemplateAsync)(`${A}/audio`,"index"));$("#toolbox-settings-content").append(a);const r=$(await(0,b.renderExtensionTemplateAsync)(`${A}/reference`,"index"));$("#extension-reference").append(r);const i=$(await(0,b.renderExtensionTemplateAsync)(`${A}/variable_manager/public`,"variable_manager_entry"));$("#toolbox-settings-content").prepend(i)}async function R(){const e=await(0,m.Mj)(m.ch);$(".version").text(`Ver ${e}`);await(0,m.F8)()&&(0,m.JD)(),$("#update-extension").on("click",async()=>await(0,m.YY)())}async function k(){const e=await(0,v.aN)("debug.enabled",T.debug.enabled);$("#debug-mode-toggle").prop("checked",e).on("click",e=>{const t=e.target.checked;(0,v.P1)("debug.enabled",t),t?log.enableAll():log.setLevel("warn")}),e?log.enableAll():log.setLevel("warn")}jQuery(async()=>{await I(),b.extension_settings[v.fh]||(_.set(b.extension_settings,v.fh,T),_.unset(b.extension_settings,v.Zi),(0,m.Qx)(),await(0,y.saveSettings)()),(0,S.x)(),(0,h.c)(),globalThis.log=E(),await k(),$("#main-settings-title").addClass("title-item-active"),$("#main-settings-content").show(),$("#render-settings-content").hide(),$("#script-settings-content").hide(),$("#toolbox-settings-content").hide(),$("#main-settings-title").on("click",e=>x(e)),$("#render-settings-title").on("click",e=>x(e)),$("#script-settings-title").on("click",e=>x(e)),$("#toolbox-settings-title").on("click",e=>x(e)),y.eventSource.once(y.event_types.APP_READY,async()=>{(0,s.D)(),await R(),await(0,a.$s)(),(0,u.y7)(),(0,f.h)(),await(0,l.wl)(),await(0,o.RV)(),(0,i.BC)(),await(0,c.K)(),await(0,r.J)(),(0,p.W)()}),g.N.initAll(".collapsible",{headerSelector:"div:first-child",contentSelector:".collapsible-content",initiallyExpanded:!1,animationDuration:{expand:280,collapse:250}})}),t()}catch(O){t(O)}})},3303:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.d(t,{L:()=>p});var r=n(1613),i=n(3892),s=n(363),o=n.n(s),c=n(190),l=e([c]);c=(l.then?(await l)():l)[0];var d=n.n(c);class p{defaultTypeDialogCallback;constructor(e){this.defaultTypeDialogCallback=e,this.setupGlobalEventDelegation()}setupGlobalEventDelegation(){$(document).on("click",".variable-action-btn.object-save-btn",e=>{e.stopPropagation(),e.stopImmediatePropagation();const t=$(e.currentTarget).closest(".variable-card"),n=this.findTopLevelCard(t);n&&this.saveNestedCardValue(n)}),$(document).on("click",".variable-action-btn.object-delete-btn",e=>{e.stopPropagation(),e.stopImmediatePropagation();const t=$(e.currentTarget).closest(".variable-card"),n=this.findTopLevelCard(t);n&&this.deleteNestedCardValue(n,t)}),$(document).on("click",".variable-action-btn.add-nested-key-btn",async e=>{e.stopPropagation(),e.stopImmediatePropagation();const t=$(e.currentTarget).closest(".variable-card"),n=this.defaultTypeDialogCallback;n?await n(e=>{this.addObjectKey(t,e,this.defaultTypeDialogCallback)}):(o().error("添加卡片出错"),toastr.error("添加卡片出错"))}),$(document).on("click",".variable-action-btn.add-key-btn",async e=>{e.stopPropagation(),e.stopImmediatePropagation();const t=$(e.currentTarget).closest(".variable-card"),n=this.defaultTypeDialogCallback;n?await n(e=>{this.addObjectKey(t,e,this.defaultTypeDialogCallback)}):(o().error("未提供类型选择对话框回调函数"),toastr.error("未提供类型选择对话框回调函数"))})}createCard(e,t,n){let a;const{name:r,value:s,dataType:o,id:c}=e;switch(a=$(`\n      <div class="variable-card" data-type="${o}" data-variable-id="${c}">\n        <div class="variable-card-header">\n          <div class="variable-title-container">\n            <i></i>\n            <input type="text" class="variable-title" value="${r}" placeholder="变量名称">\n          </div>\n          <div class="variable-actions">\n            <div class="variable-action-btn save-btn" title="保存">\n              <i class="fa-regular fa-save"></i>\n            </div>\n            <div class="variable-action-btn delete-btn" title="删除">\n              <i class="fa-regular fa-trash-can"></i>\n            </div>\n          </div>\n        </div>\n        <div class="variable-card-content">\n        </div>\n      </div>\n    `),t&&(a.removeAttr("data-variable-id"),a.find(".variable-action-btn.save-btn").removeClass("save-btn").addClass("object-save-btn").end().find(".variable-action-btn.delete-btn").removeClass("delete-btn").addClass("object-delete-btn")),o){case"array":a.find(".variable-title-container i").addClass("fa-solid fa-list").end().find(".variable-card-content").append('<div class="list-items-container"></div><div class="add-list-item"><i class="fa-solid fa-plus"></i> 添加项目</div>'),this.populateArrayItems(a,s);a.find(".list-items-container").sortable({delay:(0,i.getSortableDelay)(),handle:".drag-handle"});break;case"object":a.find(".variable-title-container i").addClass("fa-regular fa-code"),this.setupObjectCard(a,e,t,n);break;case"boolean":a.find(".variable-title-container i").addClass("fa-regular fa-toggle-on").end().find(".variable-card-content").append(`\n              <div class="boolean-input-container">\n                <div class="boolean-buttons-container">\n                  <div class="boolean-btn ${s?"active":""}" data-value="true">True</div>\n                  <div class="boolean-btn ${s?"":"active"}" data-value="false">False</div>\n                </div>\n              </div>\n            `).find(".boolean-btn").on("click",function(){a.find(".boolean-btn").removeClass("active"),$(this).addClass("active")});break;case"number":a.find(".variable-title-container i").addClass("fa-solid fa-hashtag").end().find(".variable-card-content").append(`<input type="number" class="number-input variable-content-input" value="${s}" step="any">`);break;case"string":a.find(".variable-title-container i").addClass("fa-solid fa-font").end().find(".variable-card-content").append(`<textarea class="string-input variable-content-input" placeholder="输入字符串值">${s}</textarea>`)}return a}getVariableFromCard(e){const t=e.attr("data-variable-id")||"",n=e.find(".variable-title").val(),a=e.attr("data-type");return{id:t,name:n,dataType:a,value:this.extractValueFromCard(e,a)}}extractValueFromCard(e,t){switch(t){case"string":return e.find(".string-input").val();case"number":{const t=e.find(".number-input").val();return t?parseFloat(t):0}case"boolean":return"true"===e.find(".boolean-btn.active").attr("data-value");case"array":{const t=[];return e.find(".list-item").each((e,n)=>{const a=$(n).find(".variable-content-input").val();try{t.push(d().parse(a))}catch{t.push(a)}}),t}case"object":{if("card"===(e.attr("data-view-mode")||"card")){return this.extractObjectFromNestedCards(e)}const t=e.find(".yaml-input").val();if(!t||!t.trim()){return this.extractObjectFromNestedCards(e)}try{return d().parse(t)}catch(e){return o().error("YAML解析错误:",e),toastr.error("YAML解析错误"),{}}}default:return e.find(".variable-content-input").val()}}extractObjectFromNestedCards(e){const t={};let n=e.find("> .variable-card-content > .object-card-view > .nested-cards-container");return 0===n.length&&(n=e.find("> .variable-card-content > .nested-object-container > .nested-cards-container")),0===n.length&&(n=e.find(".nested-cards-container").first()),n.children(".variable-card").each((e,n)=>{const a=$(n),r=a.find(".variable-title").val(),i=a.attr("data-type");if(o().info("[VariableManager] 处理嵌套卡片:",{index:e,key:r,dataType:i,cardHtml:a[0].outerHTML.substring(0,200)+"..."}),r){const e=this.extractValueFromCard(a,i);t[r]=e}else o().warn("[VariableManager] 跳过空键的嵌套卡片:",e)}),o().info("[VariableManager] 最终提取结果:",t),t}renderObjectCardView(e,t,n){const a=e.find(".nested-cards-container");a.empty(),t.value&&"object"==typeof t.value&&Object.entries(t.value).forEach(([e,t])=>{const i={name:e,dataType:r.w.inferDataType(t),value:t,id:""},s=this.createCard(i,!0,n);a.append(s)})}saveNestedCardValue(e){this.syncCardViewToYamlInput(e);const t=this.findTopLevelCard(e);t&&t.trigger("nested-card:changed")}findTopLevelCard(e){if(e.attr("data-variable-id"))return e;const t=e.closest("[data-variable-id]");return t.length>0?t:null}deleteNestedCardValue(e,t){t.remove(),this.syncCardViewToYamlInput(e);const n=this.findTopLevelCard(e);n&&n.trigger("nested-card:changed")}addObjectKey(e,t,n){const a=r.w.getDefaultValueForType(t),i=new Set,s=e.closest(".variable-card");s.find(".nested-cards-container").first().children(".variable-card").each((e,t)=>{const n=$(t).find(".variable-title").val();n&&i.add(n)});const o={name:r.w.generateUniqueKey(i),dataType:t,value:a,id:""},c=this.createCard(o,!0,n);s.find(".variable-card-header").first().siblings(".variable-card-content").first().find(".nested-cards-container").first().prepend(c),c.find(".variable-title").focus().select();const l=this.findTopLevelCard(s);l&&l.is(s)&&this.syncCardViewToYamlInput(s)}setupObjectCard(e,t,n,a){n?(e.find(".variable-card-content").append('\n        <div class="nested-object-container">\n          <div class="nested-cards-container"></div>\n        </div>\n      '),e.find(".variable-actions .object-save-btn").before('\n        <div class="variable-action-btn add-nested-key-btn" title="添加键值对">\n          <i class="fa-regular fa-plus"></i>\n        </div>\n      ')):(e.find(".variable-card-content").append('\n        <textarea class="yaml-input variable-content-input" placeholder="输入YAML对象" style="display: none;"></textarea>\n            <div class="object-card-view">\n              <div class="nested-cards-container"></div>\n            </div>\n      '),e.find(".variable-actions .save-btn").before('\n        <div class="variable-action-btn toggle-view-btn" title="切换到YAML视图">\n          <i class="fa-regular fa-list"></i>\n        </div>\n        <div class="variable-action-btn add-key-btn" title="添加键值对">\n          <i class="fa-regular fa-plus"></i>\n        </div>\n      '),e.find(".variable-actions .delete-btn").after('\n        <div class="variable-action-btn collapse-btn flex" title="折叠">\n          <i class="fa-solid fa-chevron-down"></i>\n        </div>\n      '),e.find(".variable-card-content").addClass("expanded"),e.find(".collapse-btn").addClass("expanded"),e.find(".collapse-btn").on("click",()=>{const t=e.find(".collapse-btn"),n=e.children(".variable-card-content");t.toggleClass("expanded"),n.toggleClass("expanded")}),e.find(".yaml-input").val(d().stringify(t.value,null,2)).end().attr("data-view-mode","card"),e.find(".yaml-input").on("blur change",()=>{"card"===(e.attr("data-view-mode")||"card")&&this.syncYamlInputToCardView(e,a)}),e.find(".toggle-view-btn").on("click",()=>{const t=e,n="yaml"===(t.attr("data-view-mode")||"card")?"card":"yaml";t.attr("data-view-mode",n),"yaml"===n?(t.find(".toggle-view-btn i").removeClass("fa-list").addClass("fa-eye").end().attr("title","切换到卡片视图"),t.find(".variable-action-btn.add-key-btn").hide(),t.find(".yaml-input").show().end().find(".object-card-view").hide(),this.syncCardViewToYamlInput(t)):(t.find(".toggle-view-btn i").removeClass("fa-eye").addClass("fa-list").end().attr("title","切换到YAML视图"),t.find(".variable-action-btn.add-key-btn").show(),t.find(".yaml-input").hide().end().find(".object-card-view").show(),this.syncYamlInputToCardView(t,a))}));try{this.renderObjectCardView(e,t,a)}catch(e){o().error("渲染初始对象卡片视图错误:",e),toastr.error("渲染对象卡片视图错误")}}syncCardViewToYamlInput(e){const t=this.extractObjectFromNestedCards(e);e.find(".yaml-input").val(d().stringify(t,null,2))}syncYamlInputToCardView(e,t){try{const n=e.find(".yaml-input").val();if(n&&n.trim()){const a=d().parse(n),r={id:e.attr("data-variable-id")||"",name:e.find(".variable-title").val(),dataType:"object",value:a};this.renderObjectCardView(e,r,t)}}catch(e){o().error("YAML解析错误:",e),toastr.error("YAML格式错误，无法同步到卡片视图")}}populateArrayItems(e,t){if(!t||0===t.length)return;const n=e.find(".list-items-container");n.empty(),t.forEach(e=>{const t="object"==typeof e?d().stringify(e):String(e),a=$(`\n        <div class="list-item">\n          <span class="drag-handle">☰</span>\n          <textarea class="variable-content-input">${t}</textarea>\n          <div class="list-item-delete"><i class="fa-solid fa-times"></i></div>\n        </div>\n      `);n.append(a)})}}a()}catch(e){a(e)}})},3312:(e,t,n)=>{n.d(t,{eX:()=>c,tf:()=>l});var a=n(6530),r=n(476),i=n(363),s=n.n(i);const o=new Map([["userAvatarPath",a.mv],["charAvatarPath",a.$Z]]);function c(){for(const[e,t]of o.entries())r.MacrosParser.registerMacro(e,t),s().info(`[Macro] 宏 "${e}" 注册成功`)}function l(){for(const e of o.keys())r.MacrosParser.unregisterMacro(e),s().info(`[Macro] 宏 "${e}" 注销成功`)}},3385:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.d(t,{J:()=>E});var r=n(231),i=n(6491),s=n(6530),o=n(4365),c=n(9489),l=n(363),d=n.n(l),p=n(7270),h=e([r]);r=(h.then?(await h)():h)[0];const u=`${s.VE}/src/component/listener`,f={enabled:!1,url:"http://localhost:6621",duration:1e3,enable_echo:!0};let m,g;function v(){g=_.debounce(y,(0,s.CN)("listener.duration"))}async function y(){d().info("[Listener] 已将 iframe 刷新为最新版本");o.characters[o.this_chid]&&await(0,o.saveChatConditional)();const e=i.D.getInstance(),t=e.getGlobalScripts();await e.stopScriptsByType(t,r.eF.GLOBAL),await e.runScriptsByType(t,r.eF.GLOBAL),await(0,o.reloadCurrentChat)()}function b(e){e?$("#online_status_indicator").addClass("success"):$("#online_status_indicator").removeClass("success")}function w(e){m&&(b(!1),m.close()),(0,s.CN)("listener.enabled")&&(0,s.CN)("listener.url")&&(m=(0,p.io)(e),m.on("connect_error",e=>{b(!1),m.active?((0,s.CN)("listener.enable_echo")&&toastr.error(`连接酒馆助手实时监听功能出错, 尝试重连...\n${e.name}: ${e.message}`),d().error(`${e.name}: ${e.message}${e.stack??""}`)):((0,s.CN)("listener.enable_echo")&&toastr.error(`连接酒馆助手实时监听功能出错, 请手动连接重试!\n${e.name}: ${e.message}`),d().error(`${e.name}: ${e.message}${e.stack??""}`))}),m.on("connect",()=>{b(!0),d().info("[Listener] 成功连接至服务器")}),m.on("iframe_updated",()=>{g()}),m.on("disconnect",(e,t)=>{(0,s.CN)("listener.enable_echo")&&toastr.warning(`酒馆助手实时监听器断开连接: ${e}`),b(!1),d().info(`[Listener] 与服务器断开连接: ${e}\n${t}`)}))}async function E(){const e=$(await(0,c.renderExtensionTemplateAsync)(`${u}`,"index"));e.find("#iframe_update_listener_enabled").prop("checked",await(0,s.aN)("listener.enabled",f.enabled)).on("click",async function(){(0,s.P1)("listener.enabled",$(this).prop("checked")),w((0,s.CN)("listener.url"))}),e.find("#iframe_update_listener_enable_echo").prop("checked",await(0,s.aN)("listener.enable_echo",f.enable_echo)).on("click",function(){(0,s.P1)("listener.enable_echo",$(this).prop("checked"))}),e.find("#iframe_update_listener_url").val(await(0,s.aN)("listener.url",f.url)).on("input",async function(){w((0,s.P1)("listener.url",String($(this).val())))}),e.find("#iframe_update_listener_duration").val(await(0,s.aN)("listener.duration",f.duration)).on("input",async function(){(0,s.P1)("listener.duration",Number($(this).val())),v()}),$("#extension-listener").append(e),v(),w((0,s.CN)("listener.url"))}a()}catch(S){a(S)}})},3766:(e,t,n)=>{n.d(t,{_9:()=>a,hj:()=>s});var a,r=n(4365);!function(e){e.SCRIPT_TOGGLE="script_toggle",e.SCRIPT_RUN="script_run",e.SCRIPT_STOP="script_stop",e.SCRIPT_CREATE="script_create",e.SCRIPT_UPDATE="script_update",e.SCRIPT_DELETE="script_delete",e.SCRIPT_MOVE="script_move",e.SCRIPT_EDIT="script_edit",e.FOLDER_CREATE="folder_create",e.FOLDER_EDIT="folder_edit",e.FOLDER_DELETE="folder_delete",e.FOLDER_MOVE="folder_move",e.FOLDER_SCRIPTS_TOGGLE="folder_scripts_toggle",e.TYPE_TOGGLE="type_toggle",e.BUTTON_ADD="button_add",e.BUTTON_REMOVE="button_remove",e.SCRIPT_IMPORT="script_import",e.SCRIPT_EXPORT="script_export",e.ORDER_CHANGED="order_changed",e.UI_REFRESH="ui_refresh",e.UI_LOADED="ui_loaded"}(a||(a={}));class i{static instance;EVENT_NAMESPACE="script_repository_";activeListeners=new Map;constructor(){}static getInstance(){return i.instance||(i.instance=new i),i.instance}getNamespacedEvent(e){return`${this.EVENT_NAMESPACE}${e}`}on(e,t){this.activeListeners.has(e)||this.activeListeners.set(e,new Set),this.activeListeners.get(e)?.add(t);const n=this.getNamespacedEvent(e);r.eventSource.on(n,t)}off(e,t){const n=this.getNamespacedEvent(e);r.eventSource.removeListener(n,t);const a=this.activeListeners.get(e);a&&(a.delete(t),0===a.size&&this.activeListeners.delete(e))}emit(e,t){const n=this.getNamespacedEvent(e);r.eventSource.emit(n,t)}clear(){this.activeListeners.forEach((e,t)=>{const n=this.getNamespacedEvent(t);e.forEach(e=>{r.eventSource.removeListener(n,e)})}),this.activeListeners.clear()}static destroyInstance(){i.instance&&(i.instance.clear(),i.instance=void 0)}}const s=i.getInstance()},3892:(e,t,n)=>{e.exports=(e=>{var t={};return n.d(t,e),t})({Stopwatch:()=>c.Stopwatch,download:()=>c.download,ensureImageFormatSupported:()=>c.ensureImageFormatSupported,findChar:()=>c.findChar,getBase64Async:()=>c.getBase64Async,getCharaFilename:()=>c.getCharaFilename,getSortableDelay:()=>c.getSortableDelay,isDataURL:()=>c.isDataURL,loadFileToDocument:()=>c.loadFileToDocument,showFontAwesomePicker:()=>c.showFontAwesomePicker,uuidv4:()=>c.uuidv4})},4249:(e,t,n)=>{e.exports=(e=>{var t={};return n.d(t,e),t})({POPUP_TYPE:()=>l.POPUP_TYPE,callGenericPopup:()=>l.callGenericPopup})},4280:(e,t,n)=>{n.d(t,{x:()=>i});var a=n(4365),r=n(434);function i(){["auto_fix_generated_markdown","trim_sentences","forbid_external_media","encode_tags"].map(e=>function(e,t){return _.get(r.power_user,e)!==t&&(_.set(r.power_user,e,t),$(`#${e}`).prop("checked",t),!0)}(e,!1)).some(e=>!!e)&&(0,a.saveSettingsDebounced)()}},4365:(e,t,n)=>{e.exports=(e=>{var t={};return n.d(t,e),t})({MAX_INJECTION_DEPTH:()=>d.MAX_INJECTION_DEPTH,activateSendButtons:()=>d.activateSendButtons,addOneMessage:()=>d.addOneMessage,baseChatReplace:()=>d.baseChatReplace,characters:()=>d.characters,chat:()=>d.chat,chat_metadata:()=>d.chat_metadata,cleanUpMessage:()=>d.cleanUpMessage,countOccurrences:()=>d.countOccurrences,deactivateSendButtons:()=>d.deactivateSendButtons,eventSource:()=>d.eventSource,event_types:()=>d.event_types,extension_prompt_roles:()=>d.extension_prompt_roles,extension_prompt_types:()=>d.extension_prompt_types,getBiasStrings:()=>d.getBiasStrings,getCharacterCardFields:()=>d.getCharacterCardFields,getCurrentChatId:()=>d.getCurrentChatId,getExtensionPromptByName:()=>d.getExtensionPromptByName,getExtensionPromptRoleByName:()=>d.getExtensionPromptRoleByName,getMaxContextSize:()=>d.getMaxContextSize,getOneCharacter:()=>d.getOneCharacter,getPastCharacterChats:()=>d.getPastCharacterChats,getRequestHeaders:()=>d.getRequestHeaders,getThumbnailUrl:()=>d.getThumbnailUrl,isOdd:()=>d.isOdd,messageFormatting:()=>d.messageFormatting,name1:()=>d.name1,name2:()=>d.name2,reloadCurrentChat:()=>d.reloadCurrentChat,reloadMarkdownProcessor:()=>d.reloadMarkdownProcessor,saveCharacterDebounced:()=>d.saveCharacterDebounced,saveChatConditional:()=>d.saveChatConditional,saveMetadata:()=>d.saveMetadata,saveSettings:()=>d.saveSettings,saveSettingsDebounced:()=>d.saveSettingsDebounced,setExtensionPrompt:()=>d.setExtensionPrompt,setGenerationProgress:()=>d.setGenerationProgress,showSwipeButtons:()=>d.showSwipeButtons,stopGeneration:()=>d.stopGeneration,substituteParams:()=>d.substituteParams,substituteParamsExtended:()=>d.substituteParamsExtended,system_message_types:()=>d.system_message_types,this_chid:()=>d.this_chid,updateMessageBlock:()=>d.updateMessageBlock,user_avatar:()=>d.user_avatar})},4925:(e,t,n)=>{n.d(t,{F8:()=>T,JD:()=>A,Mj:()=>E,Oj:()=>k,Qx:()=>O,YY:()=>I,ch:()=>g});var a=n(6530),r=n(2445),i=n(4365),s=n(9489),o=n(5983),c=n(4249),l=n(363),d=n.n(l);const p="gitlab.com",h="novi028/JS-Slash-Runner",u="main",f="manifest.json",m="CHANGELOG.md",g=`/scripts/extensions/${a.VE}/manifest.json`;let _,v,y;async function b(e){const t="string"==typeof h&&h.includes("/")?encodeURIComponent(h):h,n=encodeURIComponent(e),a=`https://${p}/api/v4/projects/${t}/repository/files/${n}/raw?ref=${u}`,r={"Cache-Control":"no-cache"};try{const e=await fetch(a,{method:"GET",headers:r});if(!e.ok){let t="";try{t=await e.text()}catch(e){}throw new Error(`[TavernHelper] 无法获取 GitLab 文件: ${e.status} ${e.statusText}. URL: ${a}. Response: ${t}`)}return(await e.text()).trim()}catch(e){throw d().error("[TavernHelper] 获取 GitLab 文件内容时出错:",e),e}}function w(e){try{const t=JSON.parse(e);if(t&&"string"==typeof t.version)return t.version;throw new Error("[TavernHelper] 在 JSON 数据中未找到有效的 'version' 字段 (必须是字符串类型)")}catch(e){if(d().error("[TavernHelper] 解析版本文件内容时出错:",e),e instanceof SyntaxError)throw new Error(`[TavernHelper] 无法将文件内容解析为 JSON: ${e.message}`);throw new Error(`[TavernHelper] 无法从文件内容中解析版本号: ${e instanceof Error?e.message:String(e)}`)}}async function E(e){return _=w(await C(e)),_}function S(e,t){const n=e.split("-")[0].split("+")[0],a=t.split("-")[0].split("+")[0],r=n.split(".").map(Number),i=a.split(".").map(Number);for(let n=0;n<3;n++){const a=r[n]||0,s=i[n]||0;if(isNaN(a)||isNaN(s))return d().warn(`[TavernHelper] 版本号 "${e}" 或 "${t}" 包含非数字部分，可能导致比较不准确。`),0;if(a>s)return 1;if(a<s)return-1}return 0}async function C(e){try{const t=await fetch(e);if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);return await t.text()}catch(t){throw d().error(`读取文件 ${e} 失败:`,t),t}}async function T(){try{v=w(await b(f));const e=S(v,_);return e>0?(d().info(`[TavernHelper] 需要更新！最新版本 ${v} > 当前版本 ${_}`),!0):0===e?(d().info(`[TavernHelper] 当前版本 ${_} 已是最新。`),!1):(d().warn(`[TavernHelper] 当前版本 ${_} 比远程版本 ${v} 还新？`),!1)}catch(e){return d().error("[TavernHelper] 版本检查失败:",e),!1}}function A(){$("#tavern-helper-extension-container .inline-drawer-header b").append('\n    <span style="color: red; font-size: 12px; font-weight: bold;">\n      New!\n    </span>\n  '),$("#version-update-text").closest(".flex-container .alignItemsCenter").append(`\n      <div style='background-color: var(--SmartThemeQuoteColor);border-radius: 50px;padding: 0 5px;height: 50%; font-size: calc(var(--mainFontSize) * 0.7);'>\n        最新：Ver ${v}\n      </div>\n    `)}function x(e,t,n){const a=[...e.matchAll(/(?:^|\n)(?:#{1,3}\s*|\[)([0-9]+\.[0-9]+\.[0-9]+)(?:\]|\s|$)/g)];if(0===a.length)return void toastr.error("无法找到版本日志。");let i="";if(S(n,t)<=0){const t=a.find(e=>e[1]===n);if(!t)return void toastr.error("获取更新日志失败");const r=t.index,s=a.find(e=>e.index>r),o=s?s.index:e.length;i=e.substring(r,o).trim()}else{const r=a.find(e=>e[1]===t);if(!r)return void toastr.error(`无法找到版本 ${t} 的日志。`);const s=a.find(e=>e[1]===n);if(!s)return void toastr.error(`无法找到版本 ${n} 的日志。`);const o=r.index,c=s.index;i=e.substring(o,c).trim()}return(0,r.G)(i)}async function I(){y||await async function(){toastr.info("获取更新日志中……");const e=await b(m);void 0===v&&(v=w(await b(f)));void 0===_&&(_=w(await C(g)));const t=x(e,_,v);if(!t)return void toastr.error("无法获取更新日志");y=t}();await(0,c.callGenericPopup)(y,c.POPUP_TYPE.CONFIRM,"",{okButton:"更新",cancelButton:"取消"})&&(toastr.info("更新中……"),await k())}async function R(e,t){const n=await async function(e,t){return await fetch("/api/extensions/delete",{method:"POST",headers:(0,i.getRequestHeaders)(),body:JSON.stringify({extensionName:e,global:t})})}(e,t);return n.ok?async function(e,t){return await fetch("/api/extensions/install",{method:"POST",headers:(0,i.getRequestHeaders)(),body:JSON.stringify({url:e,global:t})})}(e,t):n}async function k(){const e="global"===function(e){const t=Object.keys(s.extensionTypes).find(t=>t===e||t.startsWith("third-party")&&t.endsWith(e));return t?s.extensionTypes[t]:"local"}(a.Zi),t=()=>{toastr.success("成功更新酒馆助手, 准备刷新页面以生效..."),d().info("成功更新酒馆助手, 准备刷新页面以生效..."),setTimeout(()=>location.reload(),3e3)},n=await async function(e,t){return await fetch("/api/extensions/update",{method:"POST",headers:(0,i.getRequestHeaders)(),body:JSON.stringify({extensionName:e,global:t})})}(a.Zi,e);if(n.ok)return(await n.json()).isUpToDate?(toastr.success("酒馆助手已是最新版本, 无需更新"),d().info("酒馆助手已是最新版本, 无需更新")):t(),!0;const r=await R(a.Zi,e);if(!r.ok){const e=await r.text();return toastr.error(e||r.statusText,o.t`更新酒馆助手失败`,{timeOut:5e3}),d().error(`更新酒馆助手失败: ${e}`),!1}return t(),!0}async function O(){let e=await b(m);e+="\n\n*前端助手旧版配置已清除，请重新配置扩展设置*";const t=x(e,"3.0.0","3.0.0");if(t){const e=t.replace(/<h2([^>]*)>([^<]*)<\/h2>/g,"<h2$1>酒馆助手 $2</h2>");await(0,c.callGenericPopup)(e,c.POPUP_TYPE.TEXT)}}},5022:(e,t,n)=>{n.d(t,{Bg:()=>r,E6:()=>i,Un:()=>o,jh:()=>c,qh:()=>s});var a=n(6530);function r(e){if(!(0,a.CN)("render.render_hide_style"))return;if(e.prev(".code-toggle-button").length>0)return;const t=$('<div class="code-toggle-button" title="关闭[酒馆助手-渲染器-渲染优化]以取消此折叠功能">显示代码块</div>');t.on("click",function(){e.is(":visible")?(e.hide(),$(this).text("显示代码块")):(e.show(),$(this).text("隐藏代码块"))}),e.before(t)}function i(){if(!(0,a.CN)("render.render_hide_style")||(0,a.CN)("render.render_enabled"))return;const e=$("#chat");e.length&&e.find(".mes .mes_block .mes_text, .mes .mes_block .mes_reasoning_details").each(function(e,t){!function(e){e.find(".code-toggle-button").length>0||0===e.find("pre").length||e.find("pre").each(function(e,t){const n=$(t);n.find("code").length&&r(n)})}($(t))})}function s(e){$(`div[mesid="${e}"] .mes_text .code-toggle-button`).each(function(){$(this).off("click").remove()})}function o(){!function(){const e="hidden-code-block-styles";let t=document.getElementById(e);t||(t=document.createElement("style"),t.setAttribute("type","text/css"),t.setAttribute("id",e),document.head.appendChild(t)),t.innerHTML="\n      pre {\n        display: none;\n      }\n      .code-toggle-button {\n        display: inline-block;\n        margin: 5px 0;\n        padding: 5px 10px;\n        background-color: rgba(0, 0, 0, 0.1);\n        border-radius: 4px;\n        cursor: pointer;\n        font-size: 0.9em;\n        user-select: none;\n        transition: background-color 0.3s;\n      }\n      .code-toggle-button:hover {\n        background-color: rgba(0, 0, 0, 0.2);\n      }\n      .popup:has(#qr--modalEditor) .popup-content > #qr--modalEditor > #qr--main > .qr--modal-messageContainer > #qr--modal-messageHolder > #qr--modal-message {\n        color: var(--SmartThemeEmColor) !important;\n      }\n    "}(),i()}function c(){!function(){const e=document.getElementById("hidden-code-block-styles");e&&e.remove()}(),$(".code-toggle-button").each(function(){$(this).off("click").remove()}),$("pre").css("display","block")}},5330:(e,t,n)=>{function a(e){return`${e.data.request}(${function(e){const t=e.source;return t.frameElement?.id}(e)}) `}n.d(t,{Jn:()=>a,XK:()=>r,cB:()=>i});const r={};function i(e,t){r[e]=t}},5403:(e,t,n)=>{n.d(t,{Ks:()=>h,ol:()=>_,tN:()=>g});var a=n(6491),r=n(4365),i=n(363),s=n.n(i);let o=!1,c=!1;class l{id;name;visible;scriptId;constructor(e,t,n=!0){this.id=`${t}_${e}`,this.name=e,this.scriptId=t,this.visible=n}remove(){$(`#${this.id}`).remove()}}class d extends l{constructor(e,t,n=!0){super(e,t,n)}render(){return`<div class="qr--button menu_button interactable" id="${this.id}">${this.name}</div>`}bindEvents(){$(`#${this.id}`).on("click",()=>{r.eventSource.emit(this.id),s().info(`[ScriptManager] 点击按钮：${this.id}`)})}}class p{static createButton(e,t,n,a=!0){return new d(t,n,a)}}class h{buttons=[];isUpdating=!1;migrateButtonsToNewContainer(e,t){const n=$(`#${e} .qr--button`),a=$(`#${t}`);n.length&&a.length&&(n.detach().appendTo(a),this.buttons.forEach(e=>{e.bindEvents()}))}getScriptContainerId(e){return`script_container_${e}`}createButtonsFromScripts(e,t,n,a){if(!this.isUpdating){this.isUpdating=!0;try{this.clearButtons();const r=n&&e.some(e=>e.enabled&&e.buttons&&e.buttons.length>0&&e.buttons.some(e=>e.visible)),i=a&&t.some(e=>e.enabled&&e.buttons&&e.buttons.length>0&&e.buttons.some(e=>e.visible));if(!r&&!i)return;n&&r&&this.addScriptButtons(e),a&&i&&this.addScriptButtons(t)}finally{setTimeout(()=>{this.isUpdating=!1},100)}}}addScriptButtons(e){e.forEach(e=>{if(e.enabled&&e.buttons&&e.buttons.length>0){const t=e.buttons.filter(e=>e.visible).map(t=>p.createButton("script",t.name,e.id,t.visible));t.length>0&&this.addButtonsGroup(t,e.id)}})}addButtonsGroup(e,t){if(0===e.length)return;const n=this.getScriptContainerId(t);$(`#${n}`).remove();let a=`<div id="${n}" class="qr--buttons th--button">`;e.forEach(e=>{this.buttons=this.buttons.filter(t=>t.id!==e.id),this.buttons.push(e),a+=e.render()}),a+="</div>";const r=$("#send_form").find("#qr--bar");if(0!==r.length){if(c){const e=r.find(".qr--buttons.qr--color").first();e.length>0?(e.append(a),s().info(`[ScriptManager] 按钮添加到combined容器: ${t}`)):(r.append(a),s().info(`[ScriptManager] 按钮添加到qr--bar（无combined容器）: ${t}`))}else r.append(a),s().info(`[ScriptManager] 按钮添加到qr--bar: ${t}`);e.forEach(e=>e.bindEvents())}else s().warn("[ScriptManager] qr--bar容器不存在，无法添加按钮")}addButtonsForScript(e){if(!e.buttons||0===e.buttons.length)return;const t=e.buttons.filter(e=>e.visible).map(t=>p.createButton("script",t.name,e.id,t.visible));t.length>0&&this.addButtonsGroup(t,e.id)}removeButtonsByScriptId(e){const t=this.getScriptContainerId(e);$(`#${t}`).remove()}clearButtons(){this.buttons.forEach(e=>e.remove()),this.buttons=[]}cleanup(){this.clearButtons(),this.isUpdating=!1}}const u=new h;function f(){const e=$("#qr--isEnabled");o=e.length>0&&e.prop("checked");const t=$("#send_form");if(0===t.length)return;0===$("#send_form #qr--bar").length&&(t.find("#qr--bar").length||(t.append('<div class="flex-container flexGap5" id="qr--bar"></div>'),s().info("[ScriptManager] 创建qr--bar容器（qr未启用或不存在）")))}function m(){const e=$("#qr--isCombined");if(c=e.length>0&&e.prop("checked"),!o){const e=$("#send_form #qr--bar").first();if(e.length>0&&c){e.find(".qr--buttons.qr--color").length>0||e.find(".qr--buttons.qr--color").length||(e.append('<div class="qr--buttons qr--color"></div>'),s().info("[ScriptManager] 创建combined按钮容器"))}}}function g(){f(),m(),function(){const e=a.D.getInstance(),t=e.getGlobalScripts(),n=e.getCharacterScripts(),r=e.isGlobalScriptEnabled,i=e.isCharacterScriptEnabled;u.createButtonsFromScripts(t,n,r,i)}()}function _(){f(),m()}},5731:(e,t,n)=>{n.d(t,{BC:()=>l,Fz:()=>s,PN:()=>d,lh:()=>p});var a=n(4365),r=n(9489);const i=[{regex:/\{\{get_global_variable::(.*?)\}\}/gi,replace:(e,t,n)=>{const a=r.extension_settings.variables.global;return JSON.stringify(_.get(a,n,null))}},{regex:/\{\{get_chat_variable::(.*?)\}\}/gi,replace:(e,t,n)=>{const r=a.chat_metadata.variables;return JSON.stringify(_.get(r,n,null))}},{regex:/\{\{get_message_variable::(.*?)\}\}/gi,replace:(e,t,n)=>{const r=(void 0!==e.message_id?a.chat.slice(0,e.message_id+1):a.chat).map(e=>_.get(e,["variables",e.swipe_id??0])).findLast(e=>void 0!==e)??{};return JSON.stringify(_.get(r,n,null))}}];function s(e,t){i.push({regex:e,replace:t})}function o(e){if(!e.dryRun)for(const t of e.chat)for(const e of i)t.content=t.content.replace(e.regex,(n,...a)=>e.replace({role:t.role},n,...a))}function c(e){const t=$(`div.mes[mesid="${e}"]`),n=t.find(".mes_text");if(0===n.length||!i.some(e=>e.regex.test(n.text())))return;const a=n=>{for(const a of i)n=n.replace(a.regex,(n,...r)=>a.replace({message_id:Number(e),role:"true"===t.attr("is_user")?"user":"assistant"},n,...r));return n};n.html((e,t)=>a(t)),n.find("code").filter((e,t)=>i.some(e=>e.regex.test($(t).text()))).text((e,t)=>a(t))}function l(){$("div.mes").each((e,t)=>{c($(t).attr("mesid"))})}function d(){a.eventSource.on(a.event_types.CHAT_COMPLETION_PROMPT_READY,o),a.eventSource.on(a.event_types.CHARACTER_MESSAGE_RENDERED,c),a.eventSource.on(a.event_types.USER_MESSAGE_RENDERED,c),a.eventSource.on(a.event_types.MESSAGE_UPDATED,c),a.eventSource.on(a.event_types.MESSAGE_SWIPED,c)}function p(){a.eventSource.removeListener(a.event_types.CHAT_COMPLETION_PROMPT_READY,o),a.eventSource.removeListener(a.event_types.CHARACTER_MESSAGE_RENDERED,c),a.eventSource.removeListener(a.event_types.USER_MESSAGE_RENDERED,c),a.eventSource.removeListener(a.event_types.MESSAGE_UPDATED,c),a.eventSource.removeListener(a.event_types.MESSAGE_SWIPED,c)}},5913:(e,t,n)=>{function a(){globalThis.toastr=toastr}n.d(t,{I:()=>a})},5983:(e,t,n)=>{e.exports=(e=>{var t={};return n.d(t,e),t})({t:()=>p.t})},6491:(e,t,n)=>{n.d(t,{D:()=>m});var a=n(7723),r=n(7203),i=n(3766),s=n(9506),o=n(9897),c=n(9854),l=n(6530),d=n(4249),p=n(3892),h=n(363),u=n.n(h);class f{async runScript(e,t){const n=t===s.eF.GLOBAL?"全局":"局部";try{const t=$("iframe").filter((t,n)=>$(n).attr("script-id")===e.id)[0];t&&await(0,a.Ru)(t);const r=this.createScriptHtml(e),i=$("<iframe>",{style:"display: none;",id:`tavern-helper-script-${e.name}`,srcdoc:r,"script-id":e.id});i.on("load",()=>{u().info(`[ScriptManager] 启用${n}脚本["${e.name}"]`)}),$("body").append(i)}catch(t){throw u().error(`[ScriptManager] ${n}脚本启用失败:["${e.name}"]`,t),toastr.error(`${n}脚本启用失败:["${e.name}"]`),t}}async stopScript(e,t){const n=t===s.eF.GLOBAL?"全局":"局部",r=$("iframe").filter((t,n)=>$(n).attr("script-id")===e.id)[0];r&&((0,a.Ru)(r),u().info(`[ScriptManager] 禁用${n}脚本["${e.name}"]`))}createScriptHtml(e){return`\n      <html>\n      <head>\n        ${c.A}\n        <script>\n          (function ($) {\n            var original$ = $;\n            window.$ = function (selector, context) {\n              if (context === undefined || context === null) {\n                if (window.parent && window.parent.document) {\n                  context = window.parent.document;\n                } else {\n                  log.warn('无法访问 window.parent.document，将使用当前 iframe 的 document 作为上下文。');\n                  context = window.document;\n                }\n              }\n              return original$(selector, context);\n            };\n          })(jQuery);\n        <\/script>\n        <script src="${o.S.get("iframe_client")}"><\/script>\n      </head>\n      <body>\n        <script type="module">\n          ${e.content}\n        <\/script>\n      </body>\n      </html>\n    `}async clearAllScriptsIframe(){const e=$('iframe[id^="tavern-helper-script-"]');for(const t of e)await(0,a.Ru)(t)}}class m{static instance;scriptData;executor;constructor(){this.scriptData=r.i.getInstance(),this.executor=new f,this.registerEventListeners()}static getInstance(){return m.instance||(m.instance=new m),m.instance}static destroyInstance(){m.instance&&(m.instance=void 0)}registerEventListeners(){i.hj.on(i._9.SCRIPT_TOGGLE,async e=>{const{script:t,type:n,enable:a,userInput:r=!0}=e;await this.toggleScript(t,n,a,r)}),i.hj.on(i._9.TYPE_TOGGLE,async e=>{const{type:t,enable:n,userInput:a=!0}=e;await this.toggleScriptType(t,n,a)}),i.hj.on(i._9.SCRIPT_IMPORT,async e=>{const{file:t,type:n}=e;await this.importScript(t,n)}),i.hj.on(i._9.SCRIPT_DELETE,async e=>{const{scriptId:t,type:n}=e;await this.deleteScript(t,n)}),i.hj.on(i._9.SCRIPT_CREATE,async e=>{const{script:t,type:n}=e;await this.createScript(t,n)}),i.hj.on(i._9.SCRIPT_UPDATE,async e=>{const{script:t,type:n}=e;await this.updateScript(t,n)}),i.hj.on(i._9.SCRIPT_MOVE,async e=>{const{script:t,fromType:n}=e;await this.moveScript(t,n)}),i.hj.on(i._9.FOLDER_MOVE,async e=>{const{folderId:t,fromType:n}=e;await this.moveFolder(t,n)}),i.hj.on(i._9.ORDER_CHANGED,async e=>{const{data:t,type:n}=e;await this.saveOrder(t,n)}),i.hj.on(i._9.UI_LOADED,async()=>{if(!(0,l.CN)("enabled_extension"))return;const e=this.scriptData.getGlobalScripts(),t=this.scriptData.getCharacterScripts();this.scriptData.isGlobalScriptEnabled?await this.runScriptsByType(e,s.eF.GLOBAL):u().info("[ScriptManager] 全局脚本类型未启用，跳过运行全局脚本"),this.scriptData.isCharacterScriptEnabled?await this.runScriptsByType(t,s.eF.CHARACTER):u().info("[ScriptManager] 角色脚本类型未启用，跳过运行角色脚本")})}async toggleScript(e,t,n,a=!0){a&&(e.enabled=n,await this.scriptData.saveScript(e,t));try{if(n){if(t===s.eF.GLOBAL&&!this.scriptData.isGlobalScriptEnabled)return void u().info(`[script_manager] 全局脚本类型未启用，跳过启用脚本["${e.name}"]`);if(t===s.eF.CHARACTER&&!this.scriptData.isCharacterScriptEnabled)return void u().info(`[script_manager] 角色脚本类型未启用，跳过启用脚本["${e.name}"]`);await this.runScript(e,t)}else await this.stopScript(e,t);i.hj.emit(i._9.UI_REFRESH,{action:"script_toggle",script:e,type:t,enable:n})}catch(t){u().error(`[ScriptManager] 切换脚本状态失败: ${e.name}`,t),toastr.error(`切换脚本状态失败: ${e.name}`)}}async toggleScriptType(e,t,n=!0){n&&await this.scriptData.updateScriptTypeEnableState(e,t);try{const n=e===s.eF.GLOBAL?this.scriptData.getGlobalScripts():this.scriptData.getCharacterScripts();t?await this.runScriptsByType(n,e):await this.stopScriptsByType(n,e),i.hj.emit(i._9.UI_REFRESH,{action:"type_toggle",type:e,enable:t})}catch(t){u().error(`[ScriptManager] 切换脚本类型状态失败: ${e}`,t),toastr.error(`切换脚本类型状态失败: ${e}`)}}async runScript(e,t){(0,l.CN)("enabled_extension")?(t!==s.eF.GLOBAL||this.scriptData.isGlobalScriptEnabled)&&(t!==s.eF.CHARACTER||this.scriptData.isCharacterScriptEnabled)&&(await this.executor.runScript(e,t),e.buttons&&e.buttons.length>0&&i.hj.emit(i._9.BUTTON_ADD,{script:e})):toastr.error("[ScriptManager] 扩展未启用")}async stopScript(e,t){await this.executor.stopScript(e,t),e.buttons&&e.buttons.length>0&&i.hj.emit(i._9.BUTTON_REMOVE,{scriptId:e.id})}async runScriptsByType(e,t){if(!(0,l.CN)("enabled_extension"))return void toastr.error("[ScriptManager] 酒馆助手未启用，无法运行脚本");if(t===s.eF.GLOBAL&&!this.scriptData.isGlobalScriptEnabled)return;if(t===s.eF.CHARACTER&&!this.scriptData.isCharacterScriptEnabled)return;const n=e.filter(e=>e.enabled);for(const e of n)await this.executor.runScript(e,t),e.buttons&&e.buttons.length>0&&i.hj.emit(i._9.BUTTON_ADD,{script:e})}async stopScriptsByType(e,t){const n=e.filter(e=>e.enabled);for(const e of n)await this.executor.stopScript(e,t),e.buttons&&e.buttons.length>0&&i.hj.emit(i._9.BUTTON_REMOVE,{scriptId:e.id})}async importScript(e,t){try{if(e.name.toLowerCase().endsWith(".zip"))await this.importFromZip(e,t);else{if(!e.name.toLowerCase().endsWith(".json"))throw new Error("不支持的文件格式，请选择 .json 或 .zip 文件");{const n=await this.readFileAsText(e),a=JSON.parse(n);Array.isArray(a)?await this.importMultipleItems(a,t):await this.importSingleScript(a,t)}}}catch(e){u().error("[ScriptManager] 导入失败:",e),toastr.error(`导入失败: ${e instanceof Error?e.message:"无效的文件格式"}`)}}async importFromZip(e,t){window.JSZip||(u().info("import jszip"),await Promise.resolve().then(n.bind(n,2730)));const a=new JSZip,r=await a.loadAsync(e);let o=0,c=0;const l=new Set,d=[];for(const e in r.files){if(!r.files[e].dir&&e.endsWith(".json")){const t=e.split("/");1===t.length?d.push(e):2===t.length&&l.add(t[0])}}for(const e of d){const n=r.files[e],a=await n.async("string"),i=JSON.parse(a);if(i.name&&"content"in i){const e=new s.Nh({...i,enabled:!1});await this.handleScriptImport(e,t),o++}}for(const e of l){o+=await this.importFolderFromZipNew(r,e,t),c++}i.hj.emit(i._9.UI_REFRESH,{action:t===s.eF.GLOBAL?"refresh_global_scripts":"refresh_charact_scripts"}),toastr.success(`成功导入 ${o} 个脚本和 ${c} 个文件夹`)}async importFolderFromZipNew(e,t,n){const a=n===s.eF.GLOBAL?this.scriptData.getGlobalRepositoryItems():this.scriptData.getCharacterRepositoryItems();let r=t,i=1;for(;a.some(e=>"folder"===e.type&&e.name===r);)r=`${t}_${i}`,i++;const o=await this.scriptData.createFolder(r,n);let c=0;for(const a in e.files){const r=e.files[a];if(!r.dir&&a.startsWith(`${t}/`)&&a.endsWith(".json")){const e=await r.async("string"),t=JSON.parse(e);if(t.name&&"content"in t){const e=new s.Nh({...t,enabled:!1});await this.handleScriptImport(e,n),await this.scriptData.moveScriptToFolder(e.id,o,n),c++}}}return c}async importSingleScript(e,t){if(!e.name||!("content"in e))throw new Error("无效的脚本数据");const n=new s.Nh({...e,enabled:!1});await this.handleScriptImport(n,t),toastr.success(`脚本 '${n.name}' 导入成功。`)}async importMultipleItems(e,t){for(const n of e)if("folder"===n.type)await this.importFolder(n,t);else if("script"===n.type){const e=new s.Nh({...n.value,enabled:!1});await this.handleScriptImport(e,t)}else if(n.name&&"content"in n){const e=new s.Nh({...n,enabled:!1});await this.handleScriptImport(e,t)}i.hj.emit(i._9.UI_REFRESH,{action:t===s.eF.GLOBAL?"refresh_global_scripts":"refresh_charact_scripts"}),toastr.success("导入成功")}async importFolder(e,t){if(!e.name||!Array.isArray(e.value))throw new Error("无效的文件夹数据");const n=t===s.eF.GLOBAL?this.scriptData.getGlobalRepositoryItems():this.scriptData.getCharacterRepositoryItems();let a=e.name,r=1;for(;n.some(e=>"folder"===e.type&&e.name===a);)a=`${e.name}_${r}`,r++;const i=await this.scriptData.createFolder(a,t);for(const n of e.value){const e=new s.Nh({...n,enabled:!1});await this.handleScriptImport(e,t),await this.scriptData.moveScriptToFolder(e.id,i,t)}}async handleScriptImport(e,t){const n=this.scriptData.getGlobalScripts(),a=this.scriptData.getCharacterScripts(),r=n.find(t=>t.id===e.id),i=a.find(t=>t.id===e.id);let o,c;if(r?(o=r,c=s.eF.GLOBAL):i&&(o=i,c=s.eF.CHARACTER),o&&c){switch(await this.handleScriptIdConflict(e,o,t,"import")){case"new":e.id=(0,p.uuidv4)(),await this.createScript(e,t);break;case"override":await this.deleteScript(o.id,c),await this.createScript(e,t);break;case"cancel":return}}else await this.createScript(e,t)}readFileAsText(e){return new Promise((t,n)=>{const a=new FileReader;a.onload=e=>t(e.target?.result),a.onerror=e=>n(e),a.readAsText(e)})}async createScript(e,t){await this.scriptData.saveScript(e,t),i.hj.emit(i._9.UI_REFRESH,{action:"script_create",script:e,type:t})}async updateScript(e,t){await this.scriptData.saveScript(e,t),i.hj.emit(i._9.UI_REFRESH,{action:"script_update",script:e,type:t})}async saveOrder(e,t){t===s.eF.GLOBAL?await this.scriptData.saveGlobalRepositoryItems(e):await this.scriptData.saveCharacterRepositoryItems(e),this.scriptData.loadScripts()}async deleteScript(e,t){const n=this.scriptData.getScriptById(e);if(!n)throw new Error("[ScriptManager] 脚本不存在");await this.stopScript(n,t),await this.scriptData.deleteScript(e,t),i.hj.emit(i._9.UI_REFRESH,{action:"script_delete",scriptId:e,type:t})}async moveScript(e,t){await this.stopScript(e,t);const n=(0,s.RU)(t),a=this.scriptData.getScriptById(e.id),r=a?this.scriptData.getScriptType(a):null;if(a&&r===n){switch(await this.handleScriptIdConflict(e,a,n,"move")){case"new":e.id=(0,p.uuidv4)();break;case"override":await this.deleteScript(a.id,n);break;case"cancel":return}}await this.scriptData.moveItemToOtherType({type:"script",id:e.id,value:e},t),i.hj.emit(i._9.UI_REFRESH,{action:"script_move",script:e,fromType:t,targetType:n}),e.enabled&&(n===s.eF.GLOBAL&&this.scriptData.isGlobalScriptEnabled||n===s.eF.CHARACTER&&this.scriptData.isCharacterScriptEnabled)&&await this.runScript(e,n)}refreshCharacterScriptData(){this.scriptData.getCharacterScripts()}getGlobalScripts(){return this.scriptData.getGlobalScripts()}getCharacterScripts(){return this.scriptData.getCharacterScripts()}refreshCharacterScriptEnabledState(){this.scriptData.refreshCharacterScriptEnabledState()}get isGlobalScriptEnabled(){return this.scriptData.isGlobalScriptEnabled}get isCharacterScriptEnabled(){return this.scriptData.isCharacterScriptEnabled}getScriptById(e){return this.scriptData.getScriptById(e)}getGlobalRepositoryItems(){return this.scriptData.getGlobalRepositoryItems()}getCharacterRepositoryItems(){return this.scriptData.getCharacterRepositoryItems()}async createFolder(e,t,n,a){return await this.scriptData.createFolder(e,t,n,a)}async editFolder(e,t,n,a,r){await this.scriptData.editFolder(e,t,n,a,r)}async deleteFolder(e,t){await this.scriptData.deleteFolder(e,t)}async moveScriptToFolder(e,t,n){await this.scriptData.moveScriptToFolder(e,t,n)}async moveFolder(e,t){const n=(0,s.RU)(t);await this.scriptData.moveItemToOtherType({type:"folder",id:e},t),i.hj.emit(i._9.UI_REFRESH,{action:"folder_move",folderId:e,fromType:t,targetType:n})}getFolderScripts(e,t){return this.scriptData.getFolderScripts(e,t)}getRootScripts(e){return this.scriptData.getRootScripts(e)}getFolders(e){return this.scriptData.getFolders(e)}async toggleFolderScripts(e,t,n){try{await this.scriptData.toggleFolderScripts(e,t,n);const a=this.scriptData.getFolderScripts(e,t);if(t===s.eF.GLOBAL?this.scriptData.isGlobalScriptEnabled:this.scriptData.isCharacterScriptEnabled)for(const e of a)n&&e.enabled?await this.runScript(e,t):n||await this.stopScript(e,t);i.hj.emit(i._9.UI_REFRESH,{action:"folder_scripts_toggle",folderId:e,type:t,enable:n})}catch(t){u().error(`[ScriptManager] 批量切换文件夹脚本状态失败: ${e}`,t),toastr.error("批量切换文件夹脚本状态失败")}}getFolderScriptsState(e,t){return this.scriptData.getFolderScriptsState(e,t)}getScriptButton(e){const t=this.scriptData.getScriptById(e);return t?t.buttons:(u().warn(`[ScriptManager] 脚本不存在: ${e}`),[])}async setScriptButton(e,t){await this.scriptData.saveScript(e,t),e.enabled&&(i.hj.emit(i._9.BUTTON_REMOVE,{scriptId:e.id}),e.buttons&&e.buttons.length>0&&i.hj.emit(i._9.BUTTON_ADD,{script:e}))}getScriptVariables(e){const t=this.scriptData.getScriptById(e);return t?t.data||{}:(u().warn(`[ScriptManager] 脚本不存在: ${e}`),{})}async updateScriptVariables(e,t,n){const a=this.scriptData.getScriptById(e);return a?(a.data=t,await this.scriptData.saveScript(a,n),u().info(`[ScriptManager] 已更新脚本变量: ${a.name}`),!0):(u().warn(`[ScriptManager] 脚本不存在: ${e}`),!1)}getAllScriptVariables(e){const t=e===s.eF.GLOBAL?this.scriptData.getGlobalScripts():this.scriptData.getCharacterScripts(),n=new Map;return t.forEach(e=>{e.data&&Object.keys(e.data).length>0&&n.set(e.id,e.data)}),n}async cleanup(){await this.executor.clearAllScriptsIframe()}async handleScriptIdConflict(e,t,n,a="import"){const r=this.scriptData.getScriptType(t)===s.eF.GLOBAL?"全局脚本":"角色脚本",i=n===s.eF.GLOBAL?"全局脚本":"角色脚本";let o;o="import"===a?`要导入的脚本 '${e.name}' 与${r}库中的 '${t.name}' id 相同，是否要继续操作？`:`要移动到${i}库的脚本 '${e.name}' 与目标库中的 '${t.name}' id 相同，是否要继续操作？`;let c="cancel";switch(await(0,d.callGenericPopup)(o,d.POPUP_TYPE.TEXT,"",{okButton:"覆盖原脚本",cancelButton:"取消",customButtons:["新建脚本"]})){case 0:c="cancel";break;case 1:c="override";break;case 2:c="new"}return c}}},6521:(e,t,n)=>{n.d(t,{AV:()=>s,lO:()=>c,nw:()=>o});var a=n(4365),r=n(363),i=n.n(r);function s(e){const t=(0,a.substituteParamsExtended)(e);return i().info(`替换字符串中的宏, 字符串: '${e}', 结果: '${t}'`),t}function o(){return Number(s("{{lastMessageId}}"))}function c(e){const t=e=>{throw toastr.error(`${e.stack?e.stack:e.name+": "+e.message}`),e};return(...n)=>{try{const a=e(...n);return a instanceof Promise?a.catch(e=>{t(e)}):a}catch(e){return t(e)}}}},6523:(e,t,n)=>{n.d(t,{$X:()=>v,L_:()=>c,Oy:()=>m,TE:()=>d,XT:()=>p,a5:()=>f,aw:()=>g,il:()=>l});var a=n(4365),r=n(9489),i=n(8628),s=n(363),o=n.n(s);function c(e,t,n,{depth:a,character_name:r}={}){return(0,i.getRegexedString)(e,{user_input:i.regex_placement.USER_INPUT,ai_output:i.regex_placement.AI_OUTPUT,slash_command:i.regex_placement.SLASH_COMMAND,world_info:i.regex_placement.WORLD_INFO,reasoning:i.regex_placement.REASONING}[t],{characterOverride:r,isMarkdown:"display"===n,isPrompt:"prompt"===n,depth:a})}function l(){return r.extension_settings?.character_allowed_regex?.includes(a.characters?.[a.this_chid]?.avatar)}function d(){return r.extension_settings.regex??[]}function p(){return a.characters[a.this_chid]?.data?.extensions?.regex_scripts??[]}function h(e,t){return{id:e.id,script_name:e.scriptName,enabled:!e.disabled,run_on_edit:e.runOnEdit,scope:t,find_regex:e.findRegex,replace_string:e.replaceString,source:{user_input:e.placement.includes(i.regex_placement.USER_INPUT),ai_output:e.placement.includes(i.regex_placement.AI_OUTPUT),slash_command:e.placement.includes(i.regex_placement.SLASH_COMMAND),world_info:e.placement.includes(i.regex_placement.WORLD_INFO)},destination:{display:e.markdownOnly,prompt:e.promptOnly},min_depth:"number"==typeof e.minDepth?e.minDepth:null,max_depth:"number"==typeof e.maxDepth?e.maxDepth:null}}function u(e){return{id:e.id,scriptName:e.script_name,disabled:!e.enabled,runOnEdit:e.run_on_edit,findRegex:e.find_regex,replaceString:e.replace_string,trimStrings:[],placement:[...e.source.user_input?[i.regex_placement.USER_INPUT]:[],...e.source.ai_output?[i.regex_placement.AI_OUTPUT]:[],...e.source.slash_command?[i.regex_placement.SLASH_COMMAND]:[],...e.source.world_info?[i.regex_placement.WORLD_INFO]:[]],substituteRegex:0,minDepth:e.min_depth,maxDepth:e.max_depth,markdownOnly:e.destination.display,promptOnly:e.destination.prompt}}function f(){const e=l();return o().info("查询到局部正则"+(e?"被启用":"被禁用")),e}function m({scope:e="all",enable_state:t="all"}={}){if(!["all","enabled","disabled"].includes(t))throw Error(`提供的 enable_state 无效, 请提供 'all', 'enabled' 或 'disabled', 你提供的是: ${t}`);if(!["all","global","character"].includes(e))throw Error(`提供的 scope 无效, 请提供 'all', 'global' 或 'character', 你提供的是: ${e}`);let n=[];return"all"!==e&&"global"!==e||(n=[...n,...d().map(e=>h(e,"global"))]),"all"!==e&&"character"!==e||(n=[...n,...p().map(e=>h(e,"character"))]),"all"!==t&&(n=n.filter(e=>e.enabled===("enabled"===t))),structuredClone(n)}async function g(e,{scope:t="all"}){if(!["all","global","character"].includes(t))throw Error(`提供的 scope 无效, 请提供 'all', 'global' 或 'character', 你提供的是: ${t}`);const n=e.filter(e=>""==e.script_name);if(n.length>0)throw Error(`不能将酒馆正则的名称设置为空字符串:\n${JSON.stringify(n.map(e=>e.id))}`);const[i,s]=_.partition(e,e=>"global"===e.scope).map(e=>e.map(u)),c=a.characters[a.this_chid];"all"!==t&&"global"!==t||(r.extension_settings.regex=i),"all"!==t&&"character"!==t||c&&(a.characters[a.this_chid].data.extensions.regex_scripts=s,await(0,r.writeExtensionField)(a.this_chid,"regex_scripts",s)),await(0,a.saveSettings)(),c&&await(0,a.saveChatConditional)(),await(0,a.reloadCurrentChat)(),o().info(`替换酒馆正则${"all"===t||"global"===t?`, 全局正则:\n${JSON.stringify(i)}`:""}${"all"===t||"character"===t?`, 局部正则:\n${JSON.stringify(s)}`:""}`)}async function v(e,{scope:t="all"}={}){let n=m({scope:t});return n=await e(n),o().info(`对${{all:"全部",global:"全局",character:"局部"}[t]}变量表进行更新`),await g(n,{scope:t}),n}},6530:(e,t,n)=>{n.d(t,{$Z:()=>d,CN:()=>p,P1:()=>h,R:()=>c,VE:()=>o,Zi:()=>i,aN:()=>u,fh:()=>s,mv:()=>l});var a=n(4365),r=n(9489);const i="JS-Slash-Runner",s="TavernHelper",o=`third-party/${i}`,c="/characters/",l=()=>`./User Avatars/${a.user_avatar}`,d=()=>{const e=(0,a.getThumbnailUrl)("avatar",a.characters[a.this_chid]?.avatar||a.characters[a.this_chid]?.name||""),t=e.substring(e.lastIndexOf("=")+1);return c+t};function p(e,t=void 0){return _.get(r.extension_settings[s],e,t)}function h(e,t){return _.set(r.extension_settings[s],e,t),(0,a.saveSettingsDebounced)(),t}async function u(e,t){return _.has(r.extension_settings[s],e)?p(e):h(e,t)}},6912:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.d(t,{D:()=>C});var r=n(3312),i=n(5731),s=n(5022),o=n(7723),c=n(515),l=n(8652),d=n(231),p=n(5913),h=n(1054),u=n(1033),f=n(9836),m=n(9897),g=n(6530),_=n(4365),v=e([d]);d=(v.then?(await v)():v)[0];const y=async()=>{await(0,o.LV)(),(0,s.E6)()},b=async()=>{(0,i.BC)()},w=e=>{const t=parseInt(e,10);(0,o.v2)(t)},E=e=>{const t=parseInt(e,10);(0,f.Ji)(),(0,o.hk)(t),(0,g.CN)("render.render_hide_style")&&(0,s.E6)()},S=e=>{const t=parseInt(e,10);(0,f.Km)(t)};function C(){const e=(0,g.CN)("enabled_extension");e&&T(!1,!0),$("#extension-enable-toggle").prop("checked",e).on("change",function(e){T(!0,$(e.currentTarget).prop("checked"))})}async function T(e=!0,t=!0){e&&(0,g.P1)("enabled_extension",t),t?($("#extension-status-icon").css("color","green").next().text("扩展已启用"),m.S.set("iframe_client",h.J),m.S.set("viewport_adjust_script",o.hL),m.S.set("tampermonkey_script",o.rp),(0,r.eX)(),(0,p.I)(),(0,i.PN)(),(0,l.m)(),(0,d.Ye)(),e&&(0,g.CN)("render.rendering_optimize")&&(0,c.m)(),e&&(0,g.CN)("render.render_hide_style")&&(0,s.Un)(),window.addEventListener("message",u.V),_.eventSource.on(_.event_types.CHAT_CHANGED,b),_.eventSource.on("chatLoaded",y),o.Vl.forEach(e=>{_.eventSource.on(e,w)}),f.NU.forEach(e=>{_.eventSource.on(e,S)}),_.eventSource.on(_.event_types.MESSAGE_DELETED,E),e&&void 0!==_.this_chid&&await(0,_.reloadCurrentChat)()):($("#extension-status-icon").css("color","red").next().text("扩展已禁用"),m.S.delete("iframe_client"),m.S.delete("viewport_adjust_script"),m.S.delete("tampermonkey_script"),(0,r.tf)(),(0,i.lh)(),(0,l.O)(),(0,d._S)(),(0,g.CN)("render.rendering_optimize")&&(0,c.j)(),(0,g.CN)("render.render_hide_style")&&(0,s.jh)(),window.removeEventListener("message",u.V),_.eventSource.removeListener(_.event_types.CHAT_CHANGED,b),_.eventSource.removeListener("chatLoaded",y),o.Vl.forEach(e=>{_.eventSource.removeListener(e,w)}),f.NU.forEach(e=>{_.eventSource.removeListener(e,S)}),_.eventSource.removeListener(_.event_types.MESSAGE_DELETED,E),e&&void 0!==_.this_chid&&await(0,_.reloadCurrentChat)()),(0,_.saveSettingsDebounced)()}a()}catch(A){a(A)}})},7203:(e,t,n)=>{n.d(t,{i:()=>d,m:()=>p});var a=n(9506),r=n(6530),i=n(4365),s=n(9489),o=n(3892),c=n(363),l=n.n(c);class d{static instance;globalScripts=[];characterScripts=[];_isGlobalScriptEnabled=!1;_isCharacterScriptEnabled=!1;constructor(){this.loadScripts()}get isGlobalScriptEnabled(){return this._isGlobalScriptEnabled}get isCharacterScriptEnabled(){return this._isCharacterScriptEnabled}static getInstance(){return d.instance||(d.instance=new d),d.instance}static destroyInstance(){d.instance&&(d.instance=void 0)}checkCharacterScriptEnabled(){const e=(0,r.CN)("script.characters_with_scripts")||[],t=i.characters?.[i.this_chid]?.avatar;return e?.includes(t)||!1}loadScripts(){const e=(0,r.CN)("script.scriptsRepository")||[];this.globalScripts=this.migrateAndExtractScripts(e,a.eF.GLOBAL);const t=i.characters[i.this_chid]?.data?.extensions?.TavernHelper_scripts||[];this.characterScripts=this.migrateAndExtractScripts(t,a.eF.CHARACTER),this._isGlobalScriptEnabled=(0,r.CN)("script.global_script_enabled")??!1,this._isCharacterScriptEnabled=this.checkCharacterScriptEnabled()}migrateAndExtractScripts(e,t){if(!Array.isArray(e))return[];let n=!1;const r=[];for(const t of e)(0,a.Kh)(t)?(n=!0,r.push({type:"script",value:new a.Nh(t)})):(0,a.Hl)(t)?"script"===t.type?r.push({type:"script",value:new a.Nh(t.value)}):"folder"===t.type&&r.push({type:"folder",id:t.id,name:t.name,icon:t.icon||"fa-folder",color:t.color||document.documentElement.style.getPropertyValue("--SmartThemeBodyColor"),value:Array.isArray(t.value)?t.value.map(e=>new a.Nh(e)):[]}):l().warn("[ScriptManager] 无法解析的脚本数据:",t);return n&&this.saveMigratedRepository(r,t),(0,a.oI)(r)}async saveMigratedRepository(e,t){try{if(t===a.eF.GLOBAL)(0,r.P1)("script.scriptsRepository",e);else{if(!i.this_chid)return void l().warn("[ScriptManager] 无法保存角色脚本迁移数据：当前角色为空");await(0,s.writeExtensionField)(i.this_chid,"TavernHelper_scripts",e)}l().info(`[ScriptManager] 成功迁移${t}脚本数据结构`)}catch(e){l().error(`[ScriptManager] 迁移${t}脚本数据失败:`,e)}}getGlobalScripts(){return this.globalScripts}getCharacterScripts(){const e=i.characters[i.this_chid]?.data?.extensions?.TavernHelper_scripts||[];return this.characterScripts=this.migrateAndExtractScripts(e,a.eF.CHARACTER),this.characterScripts}getScriptById(e){let t=this.globalScripts.find(t=>t.id===e);return t||(t=this.characterScripts.find(t=>t.id===e),t||void 0)}async saveScript(e,t){if(!e.name||""===e.name.trim())throw new Error("[ScriptManager] 保存失败，脚本名称为空");const n=t===a.eF.GLOBAL?(0,r.CN)("script.scriptsRepository")||[]:i.characters[i.this_chid]?.data?.extensions?.TavernHelper_scripts||[],s=this.ensureRepositoryStructure(n);let o=-1,c=!1,l=-1;for(let t=0;t<s.length;t++){const n=s[t];if("script"===n.type&&n.value.id===e.id){o=t;break}if("folder"===n.type){const a=n.value.findIndex(t=>t.id===e.id);if(-1!==a){c=!0,l=t,o=a;break}}}if(c&&l>=0){s[l].value[o]=e}else o>=0?s[o].value=e:s.push({type:"script",value:e});t===a.eF.GLOBAL?await this.saveGlobalRepositoryItems(s):await this.saveCharacterRepositoryItems(s),this.loadScripts()}ensureRepositoryStructure(e){const t=[];for(const n of e)if((0,a.Kh)(n))t.push({type:"script",value:new a.Nh(n)});else if((0,a.Hl)(n))"script"===n.type?t.push({type:"script",value:new a.Nh(n.value)}):"folder"===n.type&&(n.icon||(n.icon="fa-folder"),n.color||(n.color=document.documentElement.style.getPropertyValue("--SmartThemeBodyColor")),t.push({...n,value:Array.isArray(n.value)?n.value.map(e=>new a.Nh(e)):[]}));else try{t.push({type:"script",value:new a.Nh(n)})}catch(e){l().warn("[ScriptManager] 无法解析的脚本数据:",n,e)}return t}async saveGlobalScripts(e){(0,r.P1)("script.scriptsRepository",e),this.globalScripts=e}async saveCharacterScripts(e){if(!i.this_chid)throw new Error("[ScriptManager] 保存失败，当前角色为空");await(0,s.writeExtensionField)(i.this_chid,"TavernHelper_scripts",e),this.characterScripts=e}async saveGlobalRepositoryItems(e){(0,r.P1)("script.scriptsRepository",e),this.globalScripts=(0,a.oI)(e)}async saveCharacterRepositoryItems(e){if(!i.this_chid)throw new Error("[ScriptManager] 保存失败，当前角色为空");await(0,s.writeExtensionField)(i.this_chid,"TavernHelper_scripts",e),this.characterScripts=(0,a.oI)(e)}async deleteScript(e,t){const n=t===a.eF.GLOBAL?this.getGlobalRepositoryItems():this.getCharacterRepositoryItems();let r=!1;for(let t=0;t<n.length;t++){const a=n[t];if("script"===a.type&&a.value.id===e){n.splice(t,1),r=!0;break}if("folder"===a.type){const t=a.value,n=t.findIndex(t=>t.id===e);if(-1!==n){t.splice(n,1),r=!0;break}}}if(!r)throw new Error("[ScriptManager] 删除脚本失败，脚本不存在");t===a.eF.GLOBAL?await this.saveGlobalRepositoryItems(n):await this.saveCharacterRepositoryItems(n)}async updateScriptTypeEnableState(e,t){if(e===a.eF.GLOBAL)(0,r.P1)("script.global_script_enabled",t),this._isGlobalScriptEnabled=t;else{const e=(0,r.CN)("script.characters_with_scripts")||[],n=i.characters?.[i.this_chid]?.avatar;if(t)n&&!e.includes(n)&&e.push(n);else{const t=e.indexOf(n);-1!==t&&e.splice(t,1)}(0,r.P1)("script.characters_with_scripts",e),this._isCharacterScriptEnabled=t}}getScriptType(e){return this.globalScripts.some(t=>t.id===e.id)?a.eF.GLOBAL:a.eF.CHARACTER}refreshCharacterScriptEnabledState(){this._isCharacterScriptEnabled=this.checkCharacterScriptEnabled()}getGlobalRepositoryItems(){const e=(0,r.CN)("script.scriptsRepository")||[];return this.ensureRepositoryStructure(e)}getCharacterRepositoryItems(){const e=i.characters[i.this_chid]?.data?.extensions?.TavernHelper_scripts||[];return this.ensureRepositoryStructure(e)}async createFolder(e,t,n,r){if(!e||""===e.trim())throw new Error("[ScriptManager] 文件夹名称不能为空");const i=t===a.eF.GLOBAL?this.getGlobalRepositoryItems():this.getCharacterRepositoryItems();if(i.find(t=>"folder"===t.type&&t.name===e.trim()))throw new Error("[ScriptManager] 文件夹名称已存在");const s=(0,o.uuidv4)(),c={type:"folder",id:s,name:e.trim(),icon:n||"fa-folder",color:r||document.documentElement.style.getPropertyValue("--SmartThemeBodyColor"),value:[]};return i.unshift(c),t===a.eF.GLOBAL?await this.saveGlobalRepositoryItems(i):await this.saveCharacterRepositoryItems(i),s}async editFolder(e,t,n,r,i){if(!t||""===t.trim())throw new Error("[ScriptManager] 文件夹名称不能为空");const s=n===a.eF.GLOBAL?this.getGlobalRepositoryItems():this.getCharacterRepositoryItems(),o=s.findIndex(t=>"folder"===t.type&&t.id===e);if(-1===o)throw new Error("[ScriptManager] 文件夹不存在");if(s.find(n=>"folder"===n.type&&n.name===t.trim()&&n.id!==e))throw new Error("[ScriptManager] 文件夹名称已存在");s[o].name=t.trim(),void 0!==r&&(s[o].icon=r),void 0!==i&&(s[o].color=i),n===a.eF.GLOBAL?await this.saveGlobalRepositoryItems(s):await this.saveCharacterRepositoryItems(s)}async deleteFolder(e,t){const n=t===a.eF.GLOBAL?this.getGlobalRepositoryItems():this.getCharacterRepositoryItems(),r=n.findIndex(t=>"folder"===t.type&&t.id===e);if(-1===r)throw new Error("[ScriptManager] 文件夹不存在");n.splice(r,1),t===a.eF.GLOBAL?await this.saveGlobalRepositoryItems(n):await this.saveCharacterRepositoryItems(n)}async moveItemToOtherType(e,t){const n=(0,a.RU)(t),r=t===a.eF.GLOBAL?this.getGlobalRepositoryItems():this.getCharacterRepositoryItems();let i=!1,s=null;if("script"===e.type){for(let t=0;t<r.length;t++){const n=r[t];if("script"===n.type&&n.value.id===e.id){s=n,r.splice(t,1),i=!0;break}if("folder"===n.type){const t=n.value,a=t.findIndex(t=>t.id===e.id);if(-1!==a){s={type:"script",value:t[a]},t.splice(a,1),i=!0;break}}}if(!i)throw new Error("[ScriptManager] 移动脚本失败，脚本不存在")}else if("folder"===e.type){const t=r.findIndex(t=>"folder"===t.type&&t.id===e.id);if(-1===t)throw new Error("[ScriptManager] 文件夹不存在");s=r[t],r.splice(t,1),i=!0}if(!i||!s)throw new Error("[ScriptManager] 移动失败，项目不存在");const o=n===a.eF.GLOBAL?this.getGlobalRepositoryItems():this.getCharacterRepositoryItems();if("folder"===s.type){if(o.find(e=>"folder"===e.type&&e.name===s.name))throw toastr.error("目标位置已存在同名文件夹"),new Error("[ScriptManager] 目标位置已存在同名文件夹");o.unshift(s)}else o.push(s);t===a.eF.GLOBAL?(await this.saveGlobalRepositoryItems(r),await this.saveCharacterRepositoryItems(o)):(await this.saveCharacterRepositoryItems(r),await this.saveGlobalRepositoryItems(o))}async moveScriptToFolder(e,t,n){const r=n===a.eF.GLOBAL?this.getGlobalRepositoryItems():this.getCharacterRepositoryItems();let i=null;for(let t=0;t<r.length;t++){const n=r[t];if("script"===n.type&&n.value.id===e){i=n.value,r.splice(t,1);break}if("folder"===n.type){const t=n.value,a=t.findIndex(t=>t.id===e);if(-1!==a){i=t[a],t.splice(a,1);break}}}if(!i)throw new Error("[ScriptManager] 脚本不存在");if(null===t)r.push({type:"script",value:i});else{const e=r.find(e=>"folder"===e.type&&e.id===t);if(!e)throw new Error("[ScriptManager] 目标文件夹不存在");const n=e.value;if(n.find(e=>e.name===i.name))throw new Error("[ScriptManager] 文件夹中已存在同名脚本");n.push(i)}n===a.eF.GLOBAL?await this.saveGlobalRepositoryItems(r):await this.saveCharacterRepositoryItems(r)}getFolderScripts(e,t){const n=(t===a.eF.GLOBAL?this.getGlobalRepositoryItems():this.getCharacterRepositoryItems()).find(t=>"folder"===t.type&&t.id===e);return n?n.value:[]}getRootScripts(e){return(e===a.eF.GLOBAL?this.getGlobalRepositoryItems():this.getCharacterRepositoryItems()).filter(e=>"script"===e.type).map(e=>e.value)}getFolders(e){return(e===a.eF.GLOBAL?this.getGlobalRepositoryItems():this.getCharacterRepositoryItems()).filter(e=>"folder"===e.type)}async toggleFolderScripts(e,t,n){const r=t===a.eF.GLOBAL?this.getGlobalRepositoryItems():this.getCharacterRepositoryItems(),i=r.find(t=>"folder"===t.type&&t.id===e);if(!i)throw new Error("[ScriptManager] 文件夹不存在");const s=i.value;let o=!1;for(const e of s)e.enabled!==n&&(e.enabled=n,o=!0);o&&(t===a.eF.GLOBAL?await this.saveGlobalRepositoryItems(r):await this.saveCharacterRepositoryItems(r))}getFolderScriptsState(e,t){const n=this.getFolderScripts(e,t);if(0===n.length)return"none";return 0===n.filter(e=>e.enabled).length?"none":"all"}}async function p({character:e}){const t=e?.character?.avatar,n=(0,r.CN)("script.characters_with_scripts")||[];if(t&&(localStorage.removeItem(`AlertScript_${t}`),n?.includes(t))){const e=n.indexOf(t);-1!==e&&(n.splice(e,1),(0,r.P1)("script.characters_with_scripts",n))}}},7270:(e,t,n)=>{n.d(t,{io:()=>Te});var a={};n.r(a),n.d(a,{Decoder:()=>ve,Encoder:()=>ge,PacketType:()=>me,protocol:()=>fe});const r=Object.create(null);r.open="0",r.close="1",r.ping="2",r.pong="3",r.message="4",r.upgrade="5",r.noop="6";const i=Object.create(null);Object.keys(r).forEach(e=>{i[r[e]]=e});const s={type:"error",data:"parser error"},o="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),c="function"==typeof ArrayBuffer,l=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,d=({type:e,data:t},n,a)=>o&&t instanceof Blob?n?a(t):p(t,a):c&&(t instanceof ArrayBuffer||l(t))?n?a(t):p(new Blob([t]),a):a(r[e]+(t||"")),p=(e,t)=>{const n=new FileReader;return n.onload=function(){const e=n.result.split(",")[1];t("b"+(e||""))},n.readAsDataURL(e)};function h(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let u;const f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",m="undefined"==typeof Uint8Array?[]:new Uint8Array(256);for(let e=0;e<64;e++)m[f.charCodeAt(e)]=e;const g="function"==typeof ArrayBuffer,_=(e,t)=>{if("string"!=typeof e)return{type:"message",data:y(e,t)};const n=e.charAt(0);if("b"===n)return{type:"message",data:v(e.substring(1),t)};return i[n]?e.length>1?{type:i[n],data:e.substring(1)}:{type:i[n]}:s},v=(e,t)=>{if(g){const n=(e=>{let t,n,a,r,i,s=.75*e.length,o=e.length,c=0;"="===e[e.length-1]&&(s--,"="===e[e.length-2]&&s--);const l=new ArrayBuffer(s),d=new Uint8Array(l);for(t=0;t<o;t+=4)n=m[e.charCodeAt(t)],a=m[e.charCodeAt(t+1)],r=m[e.charCodeAt(t+2)],i=m[e.charCodeAt(t+3)],d[c++]=n<<2|a>>4,d[c++]=(15&a)<<4|r>>2,d[c++]=(3&r)<<6|63&i;return l})(e);return y(n,t)}return{base64:!0,data:e}},y=(e,t)=>"blob"===t?e instanceof Blob?e:new Blob([e]):e instanceof ArrayBuffer?e:e.buffer,b=String.fromCharCode(30);function w(){return new TransformStream({transform(e,t){!function(e,t){o&&e.data instanceof Blob?e.data.arrayBuffer().then(h).then(t):c&&(e.data instanceof ArrayBuffer||l(e.data))?t(h(e.data)):d(e,!1,e=>{u||(u=new TextEncoder),t(u.encode(e))})}(e,n=>{const a=n.length;let r;if(a<126)r=new Uint8Array(1),new DataView(r.buffer).setUint8(0,a);else if(a<65536){r=new Uint8Array(3);const e=new DataView(r.buffer);e.setUint8(0,126),e.setUint16(1,a)}else{r=new Uint8Array(9);const e=new DataView(r.buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(a))}e.data&&"string"!=typeof e.data&&(r[0]|=128),t.enqueue(r),t.enqueue(n)})}})}let E;function S(e){return e.reduce((e,t)=>e+t.length,0)}function C(e,t){if(e[0].length===t)return e.shift();const n=new Uint8Array(t);let a=0;for(let r=0;r<t;r++)n[r]=e[0][a++],a===e[0].length&&(e.shift(),a=0);return e.length&&a<e[0].length&&(e[0]=e[0].slice(a)),n}function $(e){if(e)return function(e){for(var t in $.prototype)e[t]=$.prototype[t];return e}(e)}$.prototype.on=$.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},$.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},$.prototype.off=$.prototype.removeListener=$.prototype.removeAllListeners=$.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,a=this._callbacks["$"+e];if(!a)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var r=0;r<a.length;r++)if((n=a[r])===t||n.fn===t){a.splice(r,1);break}return 0===a.length&&delete this._callbacks["$"+e],this},$.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],a=1;a<arguments.length;a++)t[a-1]=arguments[a];if(n){a=0;for(var r=(n=n.slice(0)).length;a<r;++a)n[a].apply(this,t)}return this},$.prototype.emitReserved=$.prototype.emit,$.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},$.prototype.hasListeners=function(e){return!!this.listeners(e).length};const T="function"==typeof Promise&&"function"==typeof Promise.resolve?e=>Promise.resolve().then(e):(e,t)=>t(e,0),A="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function x(e,...t){return t.reduce((t,n)=>(e.hasOwnProperty(n)&&(t[n]=e[n]),t),{})}const I=A.setTimeout,R=A.clearTimeout;function k(e,t){t.useNativeTimers?(e.setTimeoutFn=I.bind(A),e.clearTimeoutFn=R.bind(A)):(e.setTimeoutFn=A.setTimeout.bind(A),e.clearTimeoutFn=A.clearTimeout.bind(A))}function O(e){return"string"==typeof e?function(e){let t=0,n=0;for(let a=0,r=e.length;a<r;a++)t=e.charCodeAt(a),t<128?n+=1:t<2048?n+=2:t<55296||t>=57344?n+=3:(a++,n+=4);return n}(e):Math.ceil(1.33*(e.byteLength||e.size))}function L(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class M extends Error{constructor(e,t,n){super(e),this.description=t,this.context=n,this.type="TransportError"}}class P extends ${constructor(e){super(),this.writable=!1,k(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,n){return super.emitReserved("error",new M(e,t,n)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState&&this.write(e)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){const t=_(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){const e=this.opts.hostname;return-1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){const t=function(e){let t="";for(let n in e)e.hasOwnProperty(n)&&(t.length&&(t+="&"),t+=encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t}(e);return t.length?"?"+t:""}}class N extends P{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";const t=()=>{this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(e++,this.once("pollComplete",function(){--e||t()})),this.writable||(e++,this.once("drain",function(){--e||t()}))}else t()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){((e,t)=>{const n=e.split(b),a=[];for(let e=0;e<n.length;e++){const r=_(n[e],t);if(a.push(r),"error"===r.type)break}return a})(e,this.socket.binaryType).forEach(e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){const e=()=>{this.write([{type:"close"}])};"open"===this.readyState?e():this.once("open",e)}write(e){this.writable=!1,((e,t)=>{const n=e.length,a=new Array(n);let r=0;e.forEach((e,i)=>{d(e,!1,e=>{a[i]=e,++r===n&&t(a.join(b))})})})(e,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=L()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let D=!1;try{D="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(e){}const F=D;function B(){}class G extends N{constructor(e){if(super(e),"undefined"!=typeof location){const t="https:"===location.protocol;let n=location.port;n||(n=t?"443":"80"),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||n!==e.port}}doWrite(e,t){const n=this.request({method:"POST",data:e});n.on("success",t),n.on("error",(e,t)=>{this.onError("xhr post error",e,t)})}doPoll(){const e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(e,t)=>{this.onError("xhr poll error",e,t)}),this.pollXhr=e}}class V extends ${constructor(e,t,n){super(),this.createRequest=e,k(this,n),this._opts=n,this._method=n.method||"GET",this._uri=t,this._data=void 0!==n.data?n.data:null,this._create()}_create(){var e;const t=x(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;const n=this._xhr=this.createRequest(t);try{n.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){n.setDisableHeaderCheck&&n.setDisableHeaderCheck(!0);for(let e in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&n.setRequestHeader(e,this._opts.extraHeaders[e])}}catch(e){}if("POST"===this._method)try{n.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}try{n.setRequestHeader("Accept","*/*")}catch(e){}null===(e=this._opts.cookieJar)||void 0===e||e.addCookies(n),"withCredentials"in n&&(n.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(n.timeout=this._opts.requestTimeout),n.onreadystatechange=()=>{var e;3===n.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(n.getResponseHeader("set-cookie"))),4===n.readyState&&(200===n.status||1223===n.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof n.status?n.status:0)},0))},n.send(this._data)}catch(e){return void this.setTimeoutFn(()=>{this._onError(e)},0)}"undefined"!=typeof document&&(this._index=V.requestsCount++,V.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=B,e)try{this._xhr.abort()}catch(e){}"undefined"!=typeof document&&delete V.requests[this._index],this._xhr=null}}_onLoad(){const e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}if(V.requestsCount=0,V.requests={},"undefined"!=typeof document)if("function"==typeof attachEvent)attachEvent("onunload",j);else if("function"==typeof addEventListener){addEventListener("onpagehide"in A?"pagehide":"unload",j,!1)}function j(){for(let e in V.requests)V.requests.hasOwnProperty(e)&&V.requests[e].abort()}const U=function(){const e=H({xdomain:!1});return e&&null!==e.responseType}();function H(e){const t=e.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!t||F))return new XMLHttpRequest}catch(e){}if(!t)try{return new(A[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(e){}}const q="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class Y extends P{get name(){return"websocket"}doOpen(){const e=this.uri(),t=this.opts.protocols,n=q?{}:x(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,n)}catch(e){return this.emitReserved("error",e)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],a=t===e.length-1;d(n,this.supportsBinary,e=>{try{this.doWrite(n,e)}catch(e){}a&&T(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=L()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}const W=A.WebSocket||A.MozWebSocket;const z={websocket:class extends Y{createSocket(e,t,n){return q?new W(e,t,n):t?new W(e,t):new W(e)}doWrite(e,t){this.ws.send(t)}},webtransport:class extends P{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{this.onClose()}).catch(e=>{this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{const t=function(e,t){E||(E=new TextDecoder);const n=[];let a=0,r=-1,i=!1;return new TransformStream({transform(o,c){for(n.push(o);;){if(0===a){if(S(n)<1)break;const e=C(n,1);i=!(128&~e[0]),r=127&e[0],a=r<126?3:126===r?1:2}else if(1===a){if(S(n)<2)break;const e=C(n,2);r=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),a=3}else if(2===a){if(S(n)<8)break;const e=C(n,8),t=new DataView(e.buffer,e.byteOffset,e.length),i=t.getUint32(0);if(i>Math.pow(2,21)-1){c.enqueue(s);break}r=i*Math.pow(2,32)+t.getUint32(4),a=3}else{if(S(n)<r)break;const e=C(n,r);c.enqueue(_(i?e:E.decode(e),t)),a=0}if(0===r||r>e){c.enqueue(s);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),n=e.readable.pipeThrough(t).getReader(),a=w();a.readable.pipeTo(e.writable),this._writer=a.writable.getWriter();const r=()=>{n.read().then(({done:e,value:t})=>{e||(this.onPacket(t),r())}).catch(e=>{})};r();const i={type:"open"};this.query.sid&&(i.data=`{"sid":"${this.query.sid}"}`),this._writer.write(i).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],a=t===e.length-1;this._writer.write(n).then(()=>{a&&T(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}},polling:class extends G{constructor(e){super(e);const t=e&&e.forceBase64;this.supportsBinary=U&&!t}request(e={}){return Object.assign(e,{xd:this.xd},this.opts),new V(H,this.uri(),e)}}},J=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,K=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function X(e){if(e.length>8e3)throw"URI too long";const t=e,n=e.indexOf("["),a=e.indexOf("]");-1!=n&&-1!=a&&(e=e.substring(0,n)+e.substring(n,a).replace(/:/g,";")+e.substring(a,e.length));let r=J.exec(e||""),i={},s=14;for(;s--;)i[K[s]]=r[s]||"";return-1!=n&&-1!=a&&(i.source=t,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i.pathNames=function(e,t){const n=/\/{2,9}/g,a=t.replace(n,"/").split("/");"/"!=t.slice(0,1)&&0!==t.length||a.splice(0,1);"/"==t.slice(-1)&&a.splice(a.length-1,1);return a}(0,i.path),i.queryKey=function(e,t){const n={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,t,a){t&&(n[t]=a)}),n}(0,i.query),i}const Z="function"==typeof addEventListener&&"function"==typeof removeEventListener,Q=[];Z&&addEventListener("offline",()=>{Q.forEach(e=>e())},!1);class ee extends ${constructor(e,t){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"==typeof e&&(t=e,e=null),e){const n=X(e);t.hostname=n.host,t.secure="https"===n.protocol||"wss"===n.protocol,t.port=n.port,n.query&&(t.query=n.query)}else t.host&&(t.hostname=X(t.host).host);k(this,t),this.secure=null!=t.secure?t.secure:"undefined"!=typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(e=>{const t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(e){let t={},n=e.split("&");for(let e=0,a=n.length;e<a;e++){let a=n[e].split("=");t[decodeURIComponent(a[0])]=decodeURIComponent(a[1])}return t}(this.opts.query)),Z&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},Q.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(e){const t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);const n=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new this._transportsByName[e](n)}_open(){if(0===this.transports.length)return void this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);const e=this.opts.rememberUpgrade&&ee.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";const t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){this.readyState="open",ee.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const t=new Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const e=this._getWritablePackets();this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let t=0;t<this.writeBuffer.length;t++){const n=this.writeBuffer[t].data;if(n&&(e+=O(n)),t>0&&e>this._maxPayload)return this.writeBuffer.slice(0,t);e+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const e=Date.now()>this._pingTimeoutTime;return e&&(this._pingTimeoutTime=0,T(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,n){return this._sendPacket("message",e,t,n),this}send(e,t,n){return this._sendPacket("message",e,t,n),this}_sendPacket(e,t,n,a){if("function"==typeof t&&(a=t,t=void 0),"function"==typeof n&&(a=n,n=null),"closing"===this.readyState||"closed"===this.readyState)return;(n=n||{}).compress=!1!==n.compress;const r={type:e,data:t,options:n};this.emitReserved("packetCreate",r),this.writeBuffer.push(r),a&&this.once("flush",a),this.flush()}close(){const e=()=>{this._onClose("forced close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},n=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?n():e()}):this.upgrading?n():e()),this}_onError(e){if(ee.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Z&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const e=Q.indexOf(this._offlineEventListener);-1!==e&&Q.splice(e,1)}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}ee.protocol=4;class te extends ee{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}_probe(e){let t=this.createTransport(e),n=!1;ee.priorWebsocketSuccess=!1;const a=()=>{n||(t.send([{type:"ping",data:"probe"}]),t.once("packet",e=>{if(!n)if("pong"===e.type&&"probe"===e.data){if(this.upgrading=!0,this.emitReserved("upgrading",t),!t)return;ee.priorWebsocketSuccess="websocket"===t.name,this.transport.pause(()=>{n||"closed"!==this.readyState&&(l(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())})}else{const e=new Error("probe error");e.transport=t.name,this.emitReserved("upgradeError",e)}}))};function r(){n||(n=!0,l(),t.close(),t=null)}const i=e=>{const n=new Error("probe error: "+e);n.transport=t.name,r(),this.emitReserved("upgradeError",n)};function s(){i("transport closed")}function o(){i("socket closed")}function c(e){t&&e.name!==t.name&&r()}const l=()=>{t.removeListener("open",a),t.removeListener("error",i),t.removeListener("close",s),this.off("close",o),this.off("upgrading",c)};t.once("open",a),t.once("error",i),t.once("close",s),this.once("close",o),this.once("upgrading",c),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{n||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){const t=[];for(let n=0;n<e.length;n++)~this.transports.indexOf(e[n])&&t.push(e[n]);return t}}class ne extends te{constructor(e,t={}){const n="object"==typeof e?e:t;(!n.transports||n.transports&&"string"==typeof n.transports[0])&&(n.transports=(n.transports||["polling","websocket","webtransport"]).map(e=>z[e]).filter(e=>!!e)),super(e,n)}}const ae="function"==typeof ArrayBuffer,re=Object.prototype.toString,ie="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===re.call(Blob),se="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===re.call(File);function oe(e){return ae&&(e instanceof ArrayBuffer||(e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer)(e))||ie&&e instanceof Blob||se&&e instanceof File}function ce(e,t){if(!e||"object"!=typeof e)return!1;if(Array.isArray(e)){for(let t=0,n=e.length;t<n;t++)if(ce(e[t]))return!0;return!1}if(oe(e))return!0;if(e.toJSON&&"function"==typeof e.toJSON&&1===arguments.length)return ce(e.toJSON(),!0);for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&ce(e[t]))return!0;return!1}function le(e){const t=[],n=e.data,a=e;return a.data=de(n,t),a.attachments=t.length,{packet:a,buffers:t}}function de(e,t){if(!e)return e;if(oe(e)){const n={_placeholder:!0,num:t.length};return t.push(e),n}if(Array.isArray(e)){const n=new Array(e.length);for(let a=0;a<e.length;a++)n[a]=de(e[a],t);return n}if("object"==typeof e&&!(e instanceof Date)){const n={};for(const a in e)Object.prototype.hasOwnProperty.call(e,a)&&(n[a]=de(e[a],t));return n}return e}function pe(e,t){return e.data=he(e.data,t),delete e.attachments,e}function he(e,t){if(!e)return e;if(e&&!0===e._placeholder){if("number"==typeof e.num&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}if(Array.isArray(e))for(let n=0;n<e.length;n++)e[n]=he(e[n],t);else if("object"==typeof e)for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]=he(e[n],t));return e}const ue=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],fe=5;var me;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(me||(me={}));class ge{constructor(e){this.replacer=e}encode(e){return e.type!==me.EVENT&&e.type!==me.ACK||!ce(e)?[this.encodeAsString(e)]:this.encodeAsBinary({type:e.type===me.EVENT?me.BINARY_EVENT:me.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id})}encodeAsString(e){let t=""+e.type;return e.type!==me.BINARY_EVENT&&e.type!==me.BINARY_ACK||(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),t}encodeAsBinary(e){const t=le(e),n=this.encodeAsString(t.packet),a=t.buffers;return a.unshift(n),a}}function _e(e){return"[object Object]"===Object.prototype.toString.call(e)}class ve extends ${constructor(e){super(),this.reviver=e}add(e){let t;if("string"==typeof e){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");t=this.decodeString(e);const n=t.type===me.BINARY_EVENT;n||t.type===me.BINARY_ACK?(t.type=n?me.EVENT:me.ACK,this.reconstructor=new ye(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else{if(!oe(e)&&!e.base64)throw new Error("Unknown type: "+e);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");t=this.reconstructor.takeBinaryData(e),t&&(this.reconstructor=null,super.emitReserved("decoded",t))}}decodeString(e){let t=0;const n={type:Number(e.charAt(0))};if(void 0===me[n.type])throw new Error("unknown packet type "+n.type);if(n.type===me.BINARY_EVENT||n.type===me.BINARY_ACK){const a=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);const r=e.substring(a,t);if(r!=Number(r)||"-"!==e.charAt(t))throw new Error("Illegal attachments");n.attachments=Number(r)}if("/"===e.charAt(t+1)){const a=t+1;for(;++t;){if(","===e.charAt(t))break;if(t===e.length)break}n.nsp=e.substring(a,t)}else n.nsp="/";const a=e.charAt(t+1);if(""!==a&&Number(a)==a){const a=t+1;for(;++t;){const n=e.charAt(t);if(null==n||Number(n)!=n){--t;break}if(t===e.length)break}n.id=Number(e.substring(a,t+1))}if(e.charAt(++t)){const a=this.tryParse(e.substr(t));if(!ve.isPayloadValid(n.type,a))throw new Error("invalid payload");n.data=a}return n}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(e){return!1}}static isPayloadValid(e,t){switch(e){case me.CONNECT:return _e(t);case me.DISCONNECT:return void 0===t;case me.CONNECT_ERROR:return"string"==typeof t||_e(t);case me.EVENT:case me.BINARY_EVENT:return Array.isArray(t)&&("number"==typeof t[0]||"string"==typeof t[0]&&-1===ue.indexOf(t[0]));case me.ACK:case me.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class ye{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){const e=pe(this.reconPack,this.buffers);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function be(e,t,n){return e.on(t,n),function(){e.off(t,n)}}const we=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Ee extends ${constructor(e,t,n){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,n&&n.auth&&(this.auth=n.auth),this._opts=Object.assign({},n),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const e=this.io;this.subs=[be(e,"open",this.onopen.bind(this)),be(e,"packet",this.onpacket.bind(this)),be(e,"error",this.onerror.bind(this)),be(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var n,a,r;if(we.hasOwnProperty(e))throw new Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;const i={type:me.EVENT,data:t,options:{}};if(i.options.compress=!1!==this.flags.compress,"function"==typeof t[t.length-1]){const e=this.ids++,n=t.pop();this._registerAckCallback(e,n),i.id=e}const s=null===(a=null===(n=this.io.engine)||void 0===n?void 0:n.transport)||void 0===a?void 0:a.writable,o=this.connected&&!(null===(r=this.io.engine)||void 0===r?void 0:r._hasPingExpired());return this.flags.volatile&&!s||(o?(this.notifyOutgoingListeners(i),this.packet(i)):this.sendBuffer.push(i)),this.flags={},this}_registerAckCallback(e,t){var n;const a=null!==(n=this.flags.timeout)&&void 0!==n?n:this._opts.ackTimeout;if(void 0===a)return void(this.acks[e]=t);const r=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&this.sendBuffer.splice(t,1);t.call(this,new Error("operation has timed out"))},a),i=(...e)=>{this.io.clearTimeoutFn(r),t.apply(this,e)};i.withError=!0,this.acks[e]=i}emitWithAck(e,...t){return new Promise((n,a)=>{const r=(e,t)=>e?a(e):n(t);r.withError=!0,t.push(r),this.emit(e,...t)})}_addToQueue(e){let t;"function"==typeof e[e.length-1]&&(t=e.pop());const n={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((e,...a)=>{if(n!==this._queue[0])return;return null!==e?n.tryCount>this._opts.retries&&(this._queue.shift(),t&&t(e)):(this._queue.shift(),t&&t(null,...a)),n.pending=!1,this._drainQueue()}),this._queue.push(n),this._drainQueue()}_drainQueue(e=!1){if(!this.connected||0===this._queue.length)return;const t=this._queue[0];t.pending&&!e||(t.pending=!0,t.tryCount++,this.flags=t.flags,this.emit.apply(this,t.args))}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){"function"==typeof this.auth?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:me.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(t=>String(t.id)===e)){const t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,new Error("socket has been disconnected"))}})}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case me.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case me.EVENT:case me.BINARY_EVENT:this.onevent(e);break;case me.ACK:case me.BINARY_ACK:this.onack(e);break;case me.DISCONNECT:this.ondisconnect();break;case me.CONNECT_ERROR:this.destroy();const t=new Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){const t=e.data||[];null!=e.id&&t.push(this.ack(e.id)),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length){const t=this._anyListeners.slice();for(const n of t)n.apply(this,e)}super.emit.apply(this,e),this._pid&&e.length&&"string"==typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){const t=this;let n=!1;return function(...a){n||(n=!0,t.packet({type:me.ACK,id:e,data:a}))}}onack(e){const t=this.acks[e.id];"function"==typeof t&&(delete this.acks[e.id],t.withError&&e.data.unshift(null),t.apply(this,e.data))}onconnect(e,t){this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:me.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){const t=this._anyListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){const t=this._anyOutgoingListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const t=this._anyOutgoingListeners.slice();for(const n of t)n.apply(this,e.data)}}}function Se(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}Se.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=1&Math.floor(10*t)?e+n:e-n}return 0|Math.min(e,this.max)},Se.prototype.reset=function(){this.attempts=0},Se.prototype.setMin=function(e){this.ms=e},Se.prototype.setMax=function(e){this.max=e},Se.prototype.setJitter=function(e){this.jitter=e};class Ce extends ${constructor(e,t){var n;super(),this.nsps={},this.subs=[],e&&"object"==typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.opts=t,k(this,t),this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(n=t.randomizationFactor)&&void 0!==n?n:.5),this.backoff=new Se({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this._readyState="closed",this.uri=e;const r=t.parser||a;this.encoder=new r.Encoder,this.decoder=new r.Decoder,this._autoConnect=!1!==t.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(t=this.backoff)||void 0===t||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(t=this.backoff)||void 0===t||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(t=this.backoff)||void 0===t||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(~this._readyState.indexOf("open"))return this;this.engine=new ne(this.uri,this.opts);const t=this.engine,n=this;this._readyState="opening",this.skipReconnect=!1;const a=be(t,"open",function(){n.onopen(),e&&e()}),r=t=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},i=be(t,"error",r);if(!1!==this._timeout){const e=this._timeout,n=this.setTimeoutFn(()=>{a(),r(new Error("timeout")),t.close()},e);this.opts.autoUnref&&n.unref(),this.subs.push(()=>{this.clearTimeoutFn(n)})}return this.subs.push(a),this.subs.push(i),this}connect(e){return this.open(e)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const e=this.engine;this.subs.push(be(e,"ping",this.onping.bind(this)),be(e,"data",this.ondata.bind(this)),be(e,"error",this.onerror.bind(this)),be(e,"close",this.onclose.bind(this)),be(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(e){this.onclose("parse error",e)}}ondecoded(e){T(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){this.emitReserved("error",e)}socket(e,t){let n=this.nsps[e];return n?this._autoConnect&&!n.active&&n.connect():(n=new Ee(this,e,t),this.nsps[e]=n),n}_destroy(e){const t=Object.keys(this.nsps);for(const e of t){if(this.nsps[e].active)return}this._close()}_packet(e){const t=this.encoder.encode(e);for(let n=0;n<t.length;n++)this.engine.write(t[n],e.options)}cleanup(){this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var n;this.cleanup(),null===(n=this.engine)||void 0===n||n.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const t=this.backoff.duration();this._reconnecting=!0;const n=this.setTimeoutFn(()=>{e.skipReconnect||(this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open(t=>{t?(e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):e.onreconnect()}))},t);this.opts.autoUnref&&n.unref(),this.subs.push(()=>{this.clearTimeoutFn(n)})}}onreconnect(){const e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}const $e={};function Te(e,t){"object"==typeof e&&(t=e,e=void 0);const n=function(e,t="",n){let a=e;n=n||"undefined"!=typeof location&&location,null==e&&(e=n.protocol+"//"+n.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?n.protocol+e:n.host+e),/^(https?|wss?):\/\//.test(e)||(e=void 0!==n?n.protocol+"//"+e:"https://"+e),a=X(e)),a.port||(/^(http|ws)$/.test(a.protocol)?a.port="80":/^(http|ws)s$/.test(a.protocol)&&(a.port="443")),a.path=a.path||"/";const r=-1!==a.host.indexOf(":")?"["+a.host+"]":a.host;return a.id=a.protocol+"://"+r+":"+a.port+t,a.href=a.protocol+"://"+r+(n&&n.port===a.port?"":":"+a.port),a}(e,(t=t||{}).path||"/socket.io"),a=n.source,r=n.id,i=n.path,s=$e[r]&&i in $e[r].nsps;let o;return t.forceNew||t["force new connection"]||!1===t.multiplex||s?o=new Ce(a,t):($e[r]||($e[r]=new Ce(a,t)),o=$e[r]),n.query&&!t.query&&(t.query=n.queryKey),o.socket(n.path,t)}Object.assign(Te,{Manager:Ce,Socket:Ee,io:Te,connect:Te})},7517:(e,t,n)=>{n.d(t,{$S:()=>m,$s:()=>G,Dt:()=>y,Mc:()=>w,NM:()=>f,Tq:()=>O,YS:()=>b,fX:()=>C});var a=n(8337),r=n(6530),i=n(4365),s=n(9489),o=n(4249),c=n(1553),l=n(3892),d=n(363),p=n.n(d);let h,u,f=[],m=[],g=!0,_=!0;const v=`${r.VE}/src/component/audio`,y={audio_enabled:!0,bgm_enabled:!0,ambient_enabled:!0,bgm_mode:"repeat",bgm_muted:!1,bgm_volume:50,bgm_selected:null,bgm_current_time:0,ambient_mode:"stop",ambient_muted:!1,ambient_volume:50,ambient_selected:null,ambient_current_time:0,audio_cooldown:0};async function b(e="bgm",t=!1){if(!(0,r.CN)("audio.audio_enabled"))return;if(!("bgm"===e?(0,r.CN)("audio.bgm_enabled"):(0,r.CN)("audio.ambient_enabled")))return;const n="bgm"===e?g:_,a=`#audio_${e}`;if(!t&&""!=$(a).attr("src")&&!n)return;let s="";const o=await E(e);if(t)s="bgm"===e?(0,r.CN)("audio.bgm_selected")||o[0]:(0,r.CN)("audio.ambient_selected")||o[0];else{s=function(e,t,n){if(!t||0===t.length)return"";switch(e){case"repeat":default:return t[0];case"single":return n||t[0];case"random":{const e=t.filter(e=>e!==n);if(0===e.length)return t[0];return e[Math.floor(Math.random()*e.length)]}case"stop":return""}}("bgm"===e?(0,r.CN)("audio.bgm_mode"):(0,r.CN)("audio.ambient_mode"),o,"bgm"===e?(0,r.CN)("audio.bgm_selected"):(0,r.CN)("audio.ambient_selected"))}if(!s)return;const c=$(a)[0];if("ambient"===e){const e=c.src.split("?")[0],t=s.split("?")[0];if(decodeURIComponent(e)===decodeURIComponent(t)&&!n)return}else if(decodeURIComponent(c.src)===decodeURIComponent(s)&&!n)return;if("bgm"===e?g=!1:_=!1,"bgm"===e)c.src=s,c.load(),await new Promise(e=>{const t=()=>{c.removeEventListener("canplaythrough",t),e()};c.readyState>=HTMLMediaElement.HAVE_ENOUGH_DATA?e():c.addEventListener("canplaythrough",t)}),await O(e);else{const t=function(e){if(!e)return"";const t=(new Date).getTime(),n=e.includes("?")?"&":"?";return`${e}${n}_=${t}`}(s);c.src=t,c.load(),await new Promise(e=>{const t=()=>{c.removeEventListener("canplaythrough",t),e()};c.readyState>=HTMLMediaElement.HAVE_ENOUGH_DATA?e():c.addEventListener("canplaythrough",t)}),await O(e)}"bgm"===e?(0,r.P1)("audio.bgm_selected",s):(0,r.P1)("audio.ambient_selected",s);const l=$(`#audio_${e}_select`);l.val()!==s&&l.val(s),(0,i.saveSettingsDebounced)()}async function w(e="bgm"){if(!(0,r.CN)(`audio.${e}_enabled`))return;const t=$(`#audio_${e}_select`);t.empty(),"bgm"===e?f=await E("bgm"):m=await E("ambient");const n="bgm"===e?f:m;let a="bgm"===e?(0,r.CN)("audio.bgm_selected"):(0,r.CN)("audio.ambient_selected");if(n&&n.length>0){n.includes(a)||(p().warn(`[Audio] 当前选择的音频 ${a} 不在列表中，自动选择列表第一个音频`),a=n[0],"bgm"===e?(0,r.P1)("audio.bgm_selected",a):(0,r.P1)("audio.ambient_selected",a),(0,i.saveSettingsDebounced)());(Array.isArray(n)?n:n.split(",").map(e=>e.trim())).forEach(e=>{const n=e.replace(/^.*[\\\/]/,"").replace(/\.[^/.]+$/,"");t.append(new Option(n,e))}),t.val(a)}else p().warn(`[Audio] 暂无可用的 ${e.toUpperCase()} 资源`)}async function E(e="bgm"){const t="bgm"===e?"bgmurl":"ambienturl";return i.chat_metadata.variables?.[t]||[]}function S(e,t="enable"){const n=$("#audio_enabled").prop("checked")&&"enable"===t;[`#audio_${e}_play_pause`,`#audio_${e}_mute`,`#audio_${e}_mode`,`#audio_${e}_select`,`#audio_${e}_volume_slider`].forEach(e=>{$(e).prop("disabled",!n)})}async function C(e="bgm"){const t=$(`#enable_${e}`).prop("checked");(0,r.P1)(`audio.${e}_enabled`,t),t?(S(e,"enable"),await b(e,!1)):($(`#audio_${e}`)[0].pause(),S(e,"disable"))}function T(e){$(`#audio_${e}`).on("ended",async function(){"bgm"===e?g=!0:_=!0;"stop"!==(0,r.CN)(`audio.${e}_mode`)&&await b(e,!1)})}function A(e){const t=$(this);e.preventDefault(),e.stopPropagation();const n=e.deltaY/20;let a=Number(t.val())-n;a<0?a=0:a>100&&(a=100),t.val(a).trigger("input")}function x(e,t){const n=$(`#${e}`),a=$(`#${t}`);let r;(0,c.isMobile)()&&(a.on("touchstart",function(e){r=setTimeout(()=>{n.css("display","block")},500)}),a.on("touchend",function(e){clearTimeout(r)}),$(document).on("click",function(e){a.is(e.target)||0!==a.has(e.target).length||n.is(e.target)||0!==n.has(e.target).length||n.css("display","none")}))}async function I(){w("bgm"),w("ambient")}async function R(e){const t=$(await(0,s.renderExtensionTemplateAsync)(`${v}`,"audio_url_manager"));t.prepend('\n    <style>\n      #saved_audio_url.empty::after {\n        content: "暂无音频";\n        color: #999;\n        margin-top: 20px;\n        font-size: 12px;\n      }\n    </style>\n  ');const n=t.find("#saved_audio_url").empty(),a=$(await(0,s.renderExtensionTemplateAsync)(`${v}`,"audio_url_template"));i.chat_metadata.variables||(i.chat_metadata.variables={});let c=i.chat_metadata.variables[e];if(c)try{0===c.length&&n.addClass("empty")}catch(t){return p().error(`[Audio] Failed to parse ${e}:`,t),null}else c=[],n.addClass("empty");const d={};let h=[...c];function u(e,t){const r=a.clone();let i;if(t.includes("/")){const e=t.split("/");i=e[e.length-1]||e[e.length-2]}else i=t;const s=i.replace(/\./g,"-");r.attr("id",s),r.find(".audio_url_name").text(i),r.find(".audio_url_name").attr("data-url",t),r.find(".edit_existing_url").on("click",async function(){const e=r.find(".audio_url_name").attr("data-url");if(!e)return void p().error("[Audio] No URL found for this element.");const t=await(0,o.callGenericPopup)("",o.POPUP_TYPE.INPUT,e);if(!t)return;const n=t.split("/").pop(),a=n.replace(/\./g,"-");r.attr("id",a),r.find(".audio_url_name").text(n),r.find(".audio_url_name").attr("data-url",t),d[e]=t}),r.find(".delete_url").on("click",async function(){await(0,o.callGenericPopup)("确认要删除此链接?",o.POPUP_TYPE.CONFIRM)&&(r.remove(),0===n.find(".audio_url_name").length&&n.addClass("empty"))}),e.append(r)}c.forEach(e=>{u(n,e)}),t.find("#import_button").on("click",async function(){const t=await async function(){const e=await(0,o.callGenericPopup)("输入要导入的网络音频链接（每行一个）",o.POPUP_TYPE.INPUT,"");if(!e)return p().debug("[Audio] URL import cancelled"),null;const t=e.trim().split("\n").map(e=>e.trim()).filter(e=>""!==e);return Array.from(new Set(t))}();t?(n.removeClass("empty"),t.forEach(e=>{u(n,e)})):p().debug(`[Audio] ${e} URL导入已取消`)}),n.sortable({delay:(0,l.getSortableDelay)(),handle:".drag-handle",stop:function(){h=[],n.find(".audio_url_name").each(function(){const e=$(this).attr("data-url");e&&h.push(e)})}});if(await(0,o.callGenericPopup)(t,o.POPUP_TYPE.CONFIRM,"",{okButton:"确认",cancelButton:"取消"})){const t=[];n.find(".audio_url_name").each(function(){const e=$(this).attr("data-url");e&&t.push(e)});const a=(0,r.CN)("audio.bgm_selected"),o=(0,r.CN)("audio.ambient_selected");if("bgmurl"===e&&a&&!t.includes(a)){$("#audio_bgm")[0].pause(),g=!0}else if("ambienturl"===e&&o&&!t.includes(o)){$("#audio_ambient")[0].pause(),_=!0}i.chat_metadata.variables[e]=t,(0,s.saveMetadataDebounced)(),"bgmurl"===e?w("bgm"):"ambienturl"===e&&w("ambient")}}async function k(e=!0,t=!0){if(t&&(u=e,(0,r.P1)("audio.audio_enabled",u)),e){if($("#audio-player-content").removeClass("audio-disabled-mask"),void 0===h&&(h=(0,r.CN)("enabled_extension")),!h)return;S("bgm","enable"),S("ambient","enable");const e=await E("bgm"),t=await E("ambient");if(e.length>0){const e=$("#audio_bgm")[0];try{await e.play()}catch(e){throw new Error("[Audio] 播放音乐失败：没有提供有效源")}}if(t.length>0){const e=$("#audio_ambient")[0];try{await e.play()}catch(e){throw new Error("[Audio] 播放音效失败：没有提供有效源")}}}else $("#audio-player-content").addClass("audio-disabled-mask"),$("#audio_bgm")[0].pause(),$("#audio_ambient")[0].pause(),S("bgm","disable"),S("ambient","disable")}async function O(e){if(!(0,r.CN)("enabled_extension")||!(0,r.CN)("audio.audio_enabled")||!(0,r.CN)(`audio.${e}_enabled`))return;const t=$(`#audio_${e}`)[0],n=$(`#audio_${e}_play_pause_icon`);if(t.error&&4===t.error.code){p().warn(`The ${e} element has no supported sources. Trying to reload selected audio from dropdown...`);const n=$(`#audio_${e}_select`).val();if(!n)return void p().error(`No audio selected for ${e}`);t.src=n,t.load()}try{await t.play(),n.removeClass("fa-play"),n.addClass("fa-pause")}catch(t){p().error(`[Audio] 播放 ${e} 音频时出错:`,t)}}async function L(e){const t=[{mode:"repeat",icon:"fa-repeat"},{mode:"random",icon:"fa-random"},{mode:"single",icon:"fa-redo-alt"},{mode:"stop",icon:"fa-cancel"}],n=(t.findIndex(t=>t.mode===(0,r.CN)(`audio.${e}_mode`))+1)%t.length;(0,r.P1)(`audio.${e}_mode`,t[n].mode),$(`#audio_${e}_mode_icon`).removeClass("fa-repeat fa-random fa-redo-alt fa-cancel"),$(`#audio_${e}_mode_icon`).addClass(t[n].icon)}async function M(e){(0,r.P1)(`audio.${e}_selected`,$(`#audio_${e}_select`).val()),await b(e,!0)}async function P(){(0,r.P1)("audio.audio_cooldown",~~$("#audio_cooldown").val())}async function N(e){(0,r.P1)(`audio.${e}_volume`,~~$(`#audio_${e}_volume_slider`).val()),$(`#audio_${e}`).prop("volume",.01*(0,r.CN)(`audio.${e}_volume`)),$(`#audio_${e}_volume`).text((0,r.CN)(`audio.${e}_volume`))}async function D(e){(0,r.P1)(`audio.${e}_muted`,!(0,r.CN)(`audio.${e}_muted`)),$(`#audio_${e}_mute_icon`).toggleClass("fa-volume-high"),$(`#audio_${e}_mute_icon`).toggleClass("fa-volume-mute"),$(`#audio_${e}`).prop("muted",!$(`#audio_${e}`).prop("muted")),$(`#audio_${e}_mute`).toggleClass("redOverlayGlow")}async function F(e){if(!(0,r.CN)("audio.audio_enabled"))return;const t=$(`#audio_${e}`)[0],n=$(`#audio_${e}_play_pause_icon`);t.paused?await O(e):(t.pause(),n.removeClass("fa-pause"),n.addClass("fa-play"))}function B(e){$(`#audio_${e}`).hide(),(0,r.CN)(`audio.${e}_muted`)?($(`#audio_${e}_mute_icon`).removeClass("fa-volume-high"),$(`#audio_${e}_mute_icon`).addClass("fa-volume-mute"),$(`#audio_${e}_mute`).addClass("redOverlayGlow"),$(`#audio_${e}`).prop("muted",!0)):($(`#audio_${e}_mute_icon`).addClass("fa-volume-high"),$(`#audio_${e}_mute_icon`).removeClass("fa-volume-mute"),$(`#audio_${e}_mute`).removeClass("redOverlayGlow"),$(`#audio_${e}`).prop("muted",!1)),$(`#enable_${e}`).prop("checked",(0,r.CN)(`audio.${e}_enabled`));const t=$(`#audio_${e}`)[0],n=$(`#audio_${e}_play_pause_icon`);t&&t.paused?(n.removeClass("fa-pause"),n.addClass("fa-play")):t&&!t.paused&&(n.removeClass("fa-play"),n.addClass("fa-pause")),w(e),function(e){const t=$(`#audio_${e}`),n=$(`#audio_${e}_progress_slider`);t.on("timeupdate",function(){if(!isNaN(this.duration)){const e=this.currentTime/this.duration*100;n.val(e)}const e=(0,r.CN)("audio.audio_cooldown"),t=this.duration-this.currentTime;if(e>0&&t<=e&&!this.isFadingOut){const t=this.volume/(10*e);this.isFadingOut=!0;const n=setInterval(()=>{this.volume>0?this.volume=Math.max(0,this.volume-t):(clearInterval(n),this.isFadingOut=!1)},100)}}),t.on("play",function(){const t=(0,r.CN)("audio.audio_cooldown"),n=$(`#audio_${e}_volume_slider`).val()/100;if(t<=0)return void(this.volume=n);this.volume=0;const a=n/(10*t),i=setInterval(()=>{this.volume<n?this.volume=Math.min(n,this.volume+a):clearInterval(i)},100)}),t.on("loadedmetadata",function(){isNaN(this.duration)||n.attr("max",100)}),n.on("input",function(){const e=$(this).val();isNaN(t[0].duration)||(t[0].currentTime=e/100*t[0].duration)})}(e)}async function G(){await(0,l.loadFileToDocument)(`/scripts/extensions/${r.VE}/src/component/audio/style.css`,"css"),u=(0,r.CN)("audio.audio_enabled"),k(u,!1),$("#audio-enable-toggle").prop("checked",u).on("click",e=>k(e.target.checked,!0)),a.N.initAll("#audio-player-header",{headerSelector:"#audio-player-header",contentSelector:"#audio-player-content",initiallyExpanded:!0,animationDuration:{expand:280,collapse:250}}),B("bgm"),B("ambient");const e=["bgm","ambient"];e.forEach(e=>{$(`#enable_${e}`).on("click",()=>C(e)),((e,t)=>{t.forEach(({selector:t,event:n,handler:a})=>{$(`#${t}`).on(n,()=>a(e))})})(e,[{selector:`enable_${e}`,event:"click",handler:C},{selector:`audio_${e}_mode`,event:"click",handler:L},{selector:`audio_${e}_mute`,event:"click",handler:D},{selector:`audio_${e}_volume_slider`,event:"input",handler:N},{selector:`audio_${e}_select`,event:"change",handler:M},{selector:`audio_${e}_play_pause`,event:"click",handler:F}]),$("#audio_cooldown").on("input",P).val((0,r.CN)("audio.audio_cooldown")),T("bgm"),T("ambient");const t=$(`#audio_${e}_volume_slider`).get(0);t&&t.addEventListener("wheel",A,{passive:!1})}),$("#audio_refresh_assets").on("click",async()=>{await I()}),x("bgm-volume-control","audio_bgm_mute_icon"),x("ambient-volume-control","audio_ambient_mute_icon");const t={bgm:"bgmurl",ambient:"ambienturl"};e.forEach(e=>{$(`#${e}_manager_button`).on("click",async()=>{await R(t[e]),await I()})});const n=$("#audio_bgm")[0],i=$("#audio_ambient")[0],s=(e,t)=>{const n=$(t);e.addEventListener("play",()=>n.removeClass("fa-play").addClass("fa-pause")),e.addEventListener("pause",()=>n.removeClass("fa-pause").addClass("fa-play"))};s(n,"#audio_bgm_play_pause_icon"),s(i,"#audio_ambient_play_pause_icon")}i.eventSource.on(i.event_types.CHAT_CHANGED,async()=>{const e=$("#audio_bgm")[0],t=$("#audio_ambient")[0];e&&!e.paused&&e.pause(),t&&!t.paused&&t.pause(),await I(),p().info("[Audio] 聊天已更改，音频资源刷新完成")})},7631:(e,t,n)=>{n.d(t,{AH:()=>l,Ab:()=>d,Gg:()=>p,NN:()=>c,Zm:()=>s,jf:()=>o});var a=n(4365),r=n(363),i=n.n(r);function s(e,{role:t="all",hide_state:n="all",include_swipes:r=!1}={}){const s=function(e,t,n){let a,r;if(e.match(/^(-?\d+)$/)){const t=Number(e);a=r=t<0?n+t+1:t}else{const t=e.match(/^(-?\d+)-(-?\d+)$/);if(!t)return null;[a,r]=_.sortBy([t[1],t[2]].map(e=>Number(e)).map(e=>e<0?n+e+1:e))}return isNaN(a)||isNaN(r)||a>r||a<t||r>n?null:{start:a,end:r}}((0,a.substituteParamsExtended)(e.toString()),0,a.chat.length-1);if(!s)throw Error(`提供的消息范围 range 无效: ${e}`);if(!["all","system","assistant","user"].includes(t))throw Error(`提供的 role 无效, 请提供 'all', 'system', 'assistant' 或 'user', 你提供的是: ${t}`);if(!["all","hidden","unhidden"].includes(n))throw Error(`提供的 hide_state 无效, 请提供 'all', 'hidden' 或 'unhidden', 你提供的是: ${n}`);const{start:o,end:c}=s,l=e=>{const s=a.chat[e];if(!s)return i().warn(`没找到第 ${e} 楼的消息`),null;const o=(c=s,c.extra?.type===a.system_message_types.NARRATOR?c.is_user?"unknown":"system":c.is_user?"user":"assistant");var c;if("all"!==t&&o!==t)return i().debug(`筛去了第 ${e} 楼的消息因为它的身份不是 ${t}`),null;if("all"!==n&&"hidden"===n!==s.is_system)return i().debug(`筛去了第 ${e} 楼的消息因为它${"hidden"===n?"":"没"} 被隐藏`),null;const l=s?.swipe_id??0,d=s?.swipes??[s.mes],p=s?.variables??[{}],h=s?.swipes_info??[s?.extra??{}],u=h[l],f=p[l];return r?{message_id:e,name:s.name,role:o,is_hidden:s.is_system,swipe_id:l,swipes:d,swipes_data:p,swipes_info:h}:{message_id:e,name:s.name,role:o,is_hidden:s.is_system,message:s.mes,data:f,extra:u,swipe_id:l,swipes:d,swipes_data:p}},d=_.range(o,c+1).map(e=>l(e)).filter(e=>null!==e);return i().info(`获取${o==c?`第 ${o} `:` ${o}-${c} `}楼的消息, 选项: ${JSON.stringify({role:t,hide_state:n,include_swipes:r})} `),structuredClone(d)}async function o(e,{refresh:t="affected"}={}){await Promise.all(e.map(async e=>{const t=a.chat[e.message_id];if(void 0!==t)if(void 0!==e?.name&&_.set(t,"name",e.name),void 0!==e?.role&&_.set(t,"is_user","user"===e.role),void 0!==e?.is_hidden&&_.set(t,"is_system",e.is_hidden),(e=>_.has(e,"message")||_.has(e,"data"))(e))void 0!==e?.message&&(_.set(t,"mes",e.message),void 0!==t?.swipes&&_.set(t,["swipes",t.swipe_id],e.message)),void 0!==e?.data&&(void 0===t?.variables&&_.set(t,"variables",_.times(t.swipes?.length??1,_.constant({}))),_.set(t,["variables",t.swipe_id??0],e.data),a.eventSource.emit("message_variables_changed",{message_id:e.message_id,variables:e.data})),void 0!==e?.extra&&(void 0===t?.swipes_info&&_.set(t,"swipes_info",_.times(t.swipes?.length??1,_.constant({}))),_.set(t,"extra",e?.extra),_.set(t,["swipes_info",t.swipe_id??0],e?.extra));else if(void 0!==e?.swipe_id||void 0!==e?.swipes||void 0!==e?.swipes_data||void 0!==e?.swipes_info){_.set(e,"swipe_id",e.swipe_id??t.swipe_id??0),_.set(e,"swipes",e.swipes??t.swipes??[t.mes]),_.set(e,"swipes_data",e.swipes_data??t.variables??[{}]),_.set(e,"swipes_info",e.swipes_info??t.swipes_info??[{}]);const n=_.max([e.swipes?.length,e.swipes_data?.length,e.swipes_info?.length])??1;e.swipes.length=n,e.swipes_data.length=n,e.swipes_info.length=n,_.set(t,"swipes",e.swipes),_.set(t,"variables",e.swipes_data),_.set(t,"swipes_info",e.swipes_info),_.set(t,"swipe_id",e.swipe_id),_.set(t,"mes",t.swipes[t.swipe_id]),_.set(t,"extra",t.swipes_info[t.swipe_id]),a.eventSource.emit("message_variables_changed",{message_id:e.message_id,variables:e.swipes_data[e.swipe_id]})}})),await(0,a.saveChatConditional)(),"all"===t?await(0,a.reloadCurrentChat)():"affected"===t&&await Promise.all(e.map(e=>(async e=>{const t=$(`div.mes[mesid = "${e}"]`);if(!t)return;const n=a.chat[e];n.swipes&&t.find(".swipes-counter").text(`${n.swipe_id+1}​/​${n.swipes.length}`),t.find(".mes_text").empty().append((0,a.messageFormatting)(n.mes,n.name,n.is_system,n.is_user,e)),await a.eventSource.emit(n.is_user?a.event_types.USER_MESSAGE_RENDERED:a.event_types.CHARACTER_MESSAGE_RENDERED,e)})(e.message_id))),i().info(`修改第 '${e.map(e=>e.message_id).join(", ")}' 楼的消息, 选项: ${JSON.stringify({refresh:t})}`)}async function c(e,{insert_at:t="end",refresh:n="all"}={}){if("end"!==t&&((t=t<0?a.chat.length+t:t)<0||t>a.chat.length))throw Error(`提供的 insert_at 无效, 请提供一个在 '0' 到 '${a.chat.length}' 之间的整数, 你提供的是: '${t}'`);const r=await Promise.all(e.map(async e=>{const t={};return void 0!==e?.name?_.set(t,"name",e.name):"user"===e.role?_.set(t,"name",a.name1):_.set(t,"name",a.name2),_.set(t,"is_user","user"===e.role),_.set(t,"is_system",e.is_hidden??!1),_.set(t,"mes",e.message),_.set(t,["variables",0],e.data??{}),t}));"end"===t?a.chat.push(...r):a.chat.splice(t,0,...r),await(0,a.saveChatConditional)(),"affected"===n&&"end"===t?r.forEach(e=>(0,a.addOneMessage)(e)):await(0,a.reloadCurrentChat)(),i().info(`在${"end"===t?"最后":`第 ${t} 楼前`}创建 ${e.length} 条消息, 选项: ${JSON.stringify({insert_at:t,refresh:n})}`)}async function l(e,{refresh:t="all"}={}){e=e.map(e=>e<0?a.chat.length+e:e).filter(e=>e>=0&&e<a.chat.length),_.pullAt(a.chat,e),await(0,a.saveChatConditional)(),"all"===t&&await(0,a.reloadCurrentChat)(),i().info(`删除第 '${e.join(", ")}' 楼的消息, 选项: ${JSON.stringify({refresh:t})}`)}async function d(e,t,n,{refresh:r="all"}={}){const s=a.chat.splice(t,n-t);a.chat.splice(e,0,...s),await(0,a.saveChatConditional)(),"all"===r&&await(0,a.reloadCurrentChat)(),i().info(`旋转第 '[${e}, ${t}) [${t}, ${n})' 楼的消息, 选项: ${JSON.stringify({refresh:r})}`)}async function p(e,t,{swipe_id:n="current",refresh:r="display_and_render_current"}={}){if(e="string"==typeof e?{message:e}:e,"number"!=typeof n&&"current"!==n)throw Error(`提供的 swipe_id 无效, 请提供 'current' 或序号, 你提供的是: ${n} `);if(!["none","display_current","display_and_render_current","all"].includes(r))throw Error(`提供的 refresh 无效, 请提供 'none', 'display_current', 'display_and_render_current' 或 'all', 你提供的是: ${r} `);const s=a.chat.at(t);if(!s)return void i().warn(`未找到第 ${t} 楼的消息`);const o=s.swipe_id??0,c="current"==n?o:n,l="none"!=r?c:o,d=e.message??(s.swipes?s.swipes[c]:void 0)??s.mes,p=(()=>{if("current"===n)return!1;if(0==n||s.swipes&&n<s.swipes.length)return!0;s.swipes||(s.swipe_id=0,s.swipes=[s.mes],s.variables=[{}]);for(let e=s.swipes.length;e<=n;++e)s.swipes.push(""),s.variables.push({});return!0})();(()=>{const t=(0,a.substituteParamsExtended)(d);e.data&&(s.variables||(s.variables=[]),s.variables[c]=e.data),s.swipes&&(s.swipes[c]=t,s.swipe_id=l),l===c&&(s.mes=t)})(),await(0,a.saveChatConditional)(),"all"==r?await(0,a.reloadCurrentChat)():await(async e=>{const n=$(`div.mes[mesid = "${t}"]`);n&&(e&&n.find(".swipes-counter").text(`${l+1}​/​${s.swipes.length}`),"none"!=r&&(n.find(".mes_text").empty().append((0,a.messageFormatting)(d,s.name,s.is_system,s.is_user,t)),"display_and_render_current"===r&&await a.eventSource.emit(s.is_user?a.event_types.USER_MESSAGE_RENDERED:a.event_types.CHARACTER_MESSAGE_RENDERED,t)))})(p),i().info(`设置第 ${t} 楼消息, 选项: ${JSON.stringify({swipe_id:n,refresh:r})}, 设置前使用的消息页: ${o}, 设置的消息页: ${c}, 现在使用的消息页: ${l} `)}},7636:(e,t,n)=>{n.d(t,{c:()=>c});var a=n(1613),r=n(1988),i=n(3892),s=n(363),o=n.n(s);class c{currentVariables=null;variableIdMap=new Map;activeVariableType="global";filterState={string:!0,array:!0,boolean:!0,number:!0,object:!0};searchKeyword="";floorMinRange=null;floorMaxRange=null;constructor(){}createVariableItem(e,t,n){const r=(0,i.uuidv4)(),s={name:e,value:t,dataType:a.w.inferDataType(t),id:r,...void 0!==n&&{message_id:n}};return this.variableIdMap.set(r,s),s}getCurrentMapVariables(){return this.currentVariables?[...this.currentVariables]:[]}addToMap(e,t,n){const a=this.createVariableItem(e,t,n);return this.currentVariables||(this.currentVariables=[]),this.currentVariables.push(a),a}removeFromMap(e){if(!this.currentVariables)return!1;const t=this.currentVariables.findIndex(t=>t.id===e);return t>=0&&(this.currentVariables.splice(t,1),this.variableIdMap.delete(e),!0)}updateInMap(e,t,n,r){if(!this.currentVariables)return!1;const i=this.currentVariables.find(t=>t.id===e);return!!i&&(i.name=t,i.value=n,i.dataType=a.w.inferDataType(n),void 0!==r&&(i.message_id=r),this.variableIdMap.set(e,i),!0)}clearVariableIdMap(){this.variableIdMap.clear()}getVariableById(e){return this.variableIdMap.get(e)}convertTavernVariablesToItems(e,t){return Object.entries(e).map(([e,n])=>this.createVariableItem(e,n,t))}convertItemsToTavernVariables(e,t){const n={};for(const a of e)void 0!==t?a.message_id===t&&(n[a.name]=a.value):n[a.name]=a.value;return n}async loadFromTavern(e){this.clearVariableIdMap();try{if(this.activeVariableType=e,"message"===e){this.currentVariables=[];const[e,t]=this.getFloorRange();if(null!==e||null!==t){const n=t??e??0;for(let t=e??0;t<=n;t++)try{const e=this.getFloorVariables(t),n=this.convertTavernVariablesToItems(e,t);this.currentVariables.push(...n)}catch(e){o().warn(`[VariableModel] 加载第${t}层变量失败:`,e)}}}else{const t=(0,r.UN)({type:e});this.currentVariables=this.convertTavernVariablesToItems(t)}return this.currentVariables}catch(t){return o().error(`[VariableManager] 加载${e}变量失败:`,t),[]}}async saveAllVariables(e,t){if(this.currentVariables){if("message"===e){const[e,n]=this.getFloorRange();if(null===e||null===n)return void o().warn("[VariableModel] 保存message变量失败: 未设置有效的楼层范围");if(void 0!==t&&t>=e&&t<=n){const e=this.currentVariables.filter(e=>e.message_id===t),n=this.convertItemsToTavernVariables(e,t);try{await(0,r.qk)(n,{type:"message",message_id:t}),o().info(`[VariableManager] 成功保存第${t}层变量`)}catch(e){o().error(`[VariableManager] 保存第${t}层变量失败:`,e)}}else for(let t=e;t<=n;t++){const e=this.currentVariables.filter(e=>e.message_id===t),n=this.convertItemsToTavernVariables(e,t);try{await(0,r.qk)(n,{type:"message",message_id:t})}catch(e){o().error(`[VariableManager] 保存第${t}层变量失败:`,e)}}}else{const t=this.convertItemsToTavernVariables(this.currentVariables);await(0,r.qk)(t,{type:e})}o().info("[VariableManager] 保存变量成功")}else o().warn("[VariableModel] 当前没有变量数据，跳过保存")}getActiveVariableType(){return this.activeVariableType}async updateListOrder(e,t,n,a){if(e===this.activeVariableType&&this.currentVariables){const r=this.currentVariables.find(e=>e.name===t);if(r&&Array.isArray(r.value)){if(r.value=n,a){const e=this.variableIdMap.get(a);e&&(e.value=n,this.variableIdMap.set(a,e))}await this.saveAllVariables(e,r.message_id)}}}async clearAllVariables(e){if(e===this.activeVariableType&&(this.currentVariables=[],this.clearVariableIdMap()),"message"===e){const[e,t]=this.getFloorRange();if(null===e||null===t)return void o().warn("[VariableModel] 清除message变量失败: 未设置有效的楼层范围");for(let n=e;n<=t;n++)try{const e={};await(0,r.qk)(e,{type:"message",message_id:n})}catch(e){o().error(`[VariableModel] 清除第${n}层变量失败:`,e)}}else{const t={};await(0,r.qk)(t,{type:e})}}updateFilterState(e,t){this.filterState[e]=t}getFilterState(){return this.filterState}updateSearchKeyword(e){this.searchKeyword=e}getSearchKeyword(){return this.searchKeyword}updateFloorRange(e,t){null!==e&&(e=Math.max(0,e)),null!==t&&(t=Math.max(0,t)),null!==e&&null!==t&&e>t&&([e,t]=[t,e]),this.floorMinRange=e,this.floorMaxRange=t,"message"===this.activeVariableType&&(this.currentVariables=null)}getFloorRange(){return[this.floorMinRange,this.floorMaxRange]}getFloorVariables(e){try{return(0,r.UN)({type:"message",message_id:e})||{}}catch(t){return o().error(`获取第${e}层变量失败:`,t),{}}}findVariableId(e,t){for(const n of this.variableIdMap.keys()){const a=this.variableIdMap.get(n);if(a&&a.name===e){if(void 0===t)return n;if(a.message_id===t)return n}}}getVariablesByMessageId(e){return this.currentVariables?this.currentVariables.filter(t=>t.message_id===e):[]}getVariablesWithoutMessageId(){return this.currentVariables?this.currentVariables.filter(e=>void 0===e.message_id):[]}getVariablesByName(e){return this.currentVariables?this.currentVariables.filter(t=>t.name===e):[]}findVariableByName(e,t){if(this.currentVariables)for(const n of this.currentVariables)if(n.name===e){if(void 0===t)return n;if(n.message_id===t)return n}}syncCurrentVariables(e,t,n){this.currentVariables||(this.currentVariables=[]),t.forEach(e=>{const t=this.currentVariables.findIndex(t=>t.id===e);t>=0&&(this.currentVariables.splice(t,1),this.variableIdMap.delete(e))}),e.forEach(e=>{this.currentVariables.push(e),this.variableIdMap.set(e.id,e)}),n.forEach(e=>{const t=this.currentVariables.findIndex(t=>t.id===e.id);t>=0&&(this.currentVariables[t]=e,this.variableIdMap.set(e.id,e))})}}},7723:(e,t,n)=>{n.d(t,{Yk:()=>A,ZX:()=>E,Ru:()=>T,Dl:()=>b,Vl:()=>w,LV:()=>S,hk:()=>I,v2:()=>C,eV:()=>x,rp:()=>f,r5:()=>g,hL:()=>u});var a=n(5022);function r(e){let t="";return $(e).contents().each(function(){this.nodeType===Node.TEXT_NODE?t+=this.textContent:this.nodeType===Node.ELEMENT_NODE&&(t+=r(this))}),t}var i=n(9897),s=n(9854),o=n(6530),c=n(4365),l=n(9489);let d=null;const p={FULL:"FULL",PARTIAL:"PARTIAL"};async function h(e=p.FULL,t=null){if(!(0,o.CN)("enabled_extension")||!(0,o.CN)("render.render_enabled"))return;const n=(0,l.getContext)(),d=n.chat.length,h=(0,o.CN)("render.render_depth")??0,u=h>0?h:d,f=[...Array(d).keys()].slice(-u);let g=[];const _=[...Array(d).keys()].filter(e=>!f.includes(e));if(e===p.FULL)g=f;else if(e===p.PARTIAL&&null!==t){if(!f.includes(t))return;g=[t]}for(const e of _){const t=n.chat[e],r=$(`[id^="message-iframe-${e}-"]`);if(r.length>0&&(r.each(function(){T(this)}),(0,c.updateMessageBlock)(e,t)),(0,o.CN)("render.render_hide_style")){$(`.mes[mesid="${e}"] .mes_block .mes_reasoning_details, .mes[mesid="${e}"] .mes_block .mes_text`).each(function(){const e=$(this).find("pre");e.length&&(0,a.Bg)(e)})}}for(const e of g){const t=$(`.mes[mesid="${e}"]`);if(!t.length){log.debug(`未找到 mesid: ${e} 对应的消息元素。`);continue}const n=t.find("pre");if(!n.length)continue;let l=1;n.each(function(){let t=r(this);if(t.includes("<body")&&t.includes("</body>")){const n=/<!--\s*disable-default-loading\s*-->/.test(t),r=/min-height:\s*[^;]*vh/.test(t),d=/\d+vh/.test(t);(r||d)&&(t=m(t));const p=r||d;let h=$("<div>").css({position:"relative",width:"100%"});const u=$("<iframe>").attr({id:`message-iframe-${e}-${l}`,loading:"lazy"}).css({margin:"5px auto",border:"none",width:"100%"});l++,p&&u.attr("data-needs-vh","true");let f=null;if(!n){const e=$("<div>").addClass("iframe-loading-overlay").html('\n                <div class="iframe-loading-content">\n                  <i class="fa-solid fa-spinner fa-spin"></i>\n                  <span class="loading-text">Loading...</span>\n                </div>');f=setTimeout(()=>{const t=e.find(".loading-text");t.length&&t.text("如加载时间过长，请检查网络")},1e4),h.append(e)}h.append(u);const g=`\n            <html>\n            <head>\n              <style>\n              ${p?`:root{--viewport-height:${window.innerHeight}px;}`:""}\n              html,body{margin:0;padding:0;overflow:hidden!important;max-width:100%!important;box-sizing:border-box}\n              .user_avatar,.user-avatar{background-image:url('${(0,o.mv)()}')}\n              .char_avatar,.char-avatar{background-image:url('${(0,o.$Z)()}')}\n              </style>\n              ${s.A}\n              <script src="${i.S.get("iframe_client")}"><\/script>\n            </head>\n            <body>\n              ${t}\n              ${p?`<script src="${i.S.get("viewport_adjust_script")}"><\/script>`:""}\n\n              ${(0,o.CN)("render.tampermonkey_compatibility")?`<script src="${i.S.get("tampermonkey_script")}"><\/script>`:""}\n            </body>\n            </html>\n          `;u.attr("srcdoc",g),u.on("load",function(){if(v(this),h=$(this).parent(),h.length){const e=h.find(".iframe-loading-overlay");e.length&&(e.css("opacity","0"),setTimeout(()=>e.remove(),300))}"true"===$(this).attr("data-needs-vh")&&this.contentWindow?.postMessage({request:"updateViewportHeight",newHeight:window.innerHeight},"*"),c.eventSource.emitAndWait("message_iframe_render_ended",this.id),(0,o.CN)("render.render_hide_style")&&(0,a.qh)(e),f&&clearTimeout(f)}),c.eventSource.emitAndWait("message_iframe_render_started",u.attr("id")),$(this).replaceWith(h)}else(0,a.Bg)($(this))})}}const u='\n$(window).on("message", function (event) {\n    if (event.originalEvent.data.request === "updateViewportHeight") {\n        const newHeight = event.originalEvent.data.newHeight;\n        $("html").css("--viewport-height", newHeight + "px");\n    }\n});\n',f="\nclass AudioManager {\n  constructor() {\n    this.currentlyPlaying = null;\n  }\n  handlePlay(audio) {\n    if (this.currentlyPlaying && this.currentlyPlaying !== audio) {\n      this.currentlyPlaying.pause();\n    }\n    window.parent.postMessage({\n      type: 'audioPlay',\n      iframeId: window.frameElement.id\n    }, '*');\n\n    this.currentlyPlaying = audio;\n  }\n  stopAll() {\n    if (this.currentlyPlaying) {\n      this.currentlyPlaying.pause();\n      this.currentlyPlaying = null;\n    }\n  }\n}\nconst audioManager = new AudioManager();\n$('.qr-button').on('click', function() {\n  const buttonName = $(this).text().trim();\n  window.parent.postMessage({ type: 'buttonClick', name: buttonName }, '*');\n});\n$('.st-text').each(function() {\n  $(this).on('input', function() {\n    window.parent.postMessage({ type: 'textInput', text: $(this).val() }, '*');\n  });\n  $(this).on('change', function() {\n    window.parent.postMessage({ type: 'textInput', text: $(this).val() }, '*');\n  });\n  const textarea = this;\n  const observer = new MutationObserver((mutations) => {\n    mutations.forEach((mutation) => {\n      if (mutation.type === 'attributes' && mutation.attributeName === 'value') {\n        window.parent.postMessage({ type: 'textInput', text: $(textarea).val() }, '*');\n      }\n    });\n  });\n  observer.observe(textarea, { attributes: true });\n});\n$('.st-send-button').on('click', function() {\n  window.parent.postMessage({ type: 'sendClick' }, '*');\n});\n$('.st-audio').on('play', function() {\n  audioManager.handlePlay(this);\n});\n$(window).on('message', function(event) {\n  if (event.originalEvent.data.type === 'stopAudio' &&\n    event.originalEvent.data.iframeId !== window.frameElement.id) {\n    audioManager.stopAll();\n  }\n});\n";function m(e){const t=window.innerHeight;let n=e.replace(/((?:document\.body\.style\.minHeight|\.style\.minHeight|setProperty\s*\(\s*['"]min-height['"])\s*[=,]\s*['"`])([^'"`]*?)(['"`])/g,(e,n,a,r)=>{if(a.includes("vh")){return n+a.replace(/(\d+(?:\.\d+)?)vh/g,e=>{const n=parseFloat(e);return 100===n?`var(--viewport-height, ${t}px)`:`calc(var(--viewport-height, ${t}px) * ${n/100})`})+r}return e});return n=n.replace(/min-height:\s*([^;]*vh[^;]*);/g,e=>`${e.replace(/(\d+(?:\.\d+)?)vh/g,e=>{const n=parseFloat(e);return 100===n?`var(--viewport-height, ${t}px)`:`calc(var(--viewport-height, ${t}px) * ${n/100})`})};`),n=n.replace(/style\s*=\s*["']([^"']*min-height:\s*[^"']*vh[^"']*?)["']/gi,(e,n)=>{const a=n.replace(/min-height:\s*([^;]*vh[^;]*)/g,e=>e.replace(/(\d+(?:\.\d+)?)vh/g,e=>{const n=parseFloat(e);return 100===n?`var(--viewport-height, ${t}px)`:`calc(var(--viewport-height, ${t}px) * ${n/100})`}));return e.replace(n,a)}),n}function g(){$(window).on("resize",function(){if($('iframe[data-needs-vh="true"]').length){const e=window.innerHeight;$('iframe[data-needs-vh="true"]').each(function(){const t=this;t.contentWindow&&t.contentWindow.postMessage({request:"updateViewportHeight",newHeight:e},"*")})}})}function _(e){const t=$(e);if(!t.length||!t[0].contentWindow||!t[0].contentWindow.document.body)return;const n=t[0].contentWindow.document,a=n.body.offsetHeight,r=n.documentElement.offsetHeight,i=Math.max(a,r),s=parseFloat(t.css("height"))||0;Math.abs(s-i)>5&&(t.css("height",i+"px"),"true"===t.attr("data-needs-vh")&&e.contentWindow&&e.contentWindow.postMessage({request:"updateViewportHeight",newHeight:window.innerHeight},"*"))}function v(e){const t=$(e);if(t.length&&t[0].contentWindow&&t[0].contentWindow.document.body)try{const n=t[0].contentWindow.document.body,a=(window._sharedResizeObserver||(window._observedElements=new Map,window._sharedResizeObserver=new ResizeObserver(e=>{for(const t of e){const e=t.target,n=window._observedElements?.get(e);if(n){const{iframe:e}=n;_(e)}}})),window._sharedResizeObserver);if(window._observedElements)for(const[t,n]of window._observedElements.entries())if(n.iframe===e){a.unobserve(t),window._observedElements.delete(t);break}window._observedElements?.set(n,{iframe:e}),a.observe(n),_(e)}catch(e){log.error("[Render] 设置 iframe 内容观察时出错:",e)}}function y(e){if("buttonClick"===e.data.type){const t=e.data.name;$(".qr--button.menu_button").each(function(){$(this).find(".qr--button-label").text().trim()===t&&$(this).trigger("click")})}else if("textInput"===e.data.type){const t=jQuery("#send_textarea");t.length&&t.val(e.data.text).trigger("input").trigger("change")}else if("sendClick"===e.data.type){const e=jQuery("#send_but");e.length&&e.trigger("click")}}async function b(e,t=!0){t&&(0,o.P1)("render.tampermonkey_compatibility",e),(0,o.CN)("enabled_extension")&&(e?d||(d=y,window.addEventListener("message",d),function(){let e=null;window.addEventListener("message",function(t){if("audioPlay"===t.data.type){const n=t.data.iframeId;e&&e!==n&&$("iframe").each(function(){const e=this;e.contentWindow&&e.contentWindow.postMessage({type:"stopAudio",iframeId:n},"*")}),e=n}})}()):d&&(window.removeEventListener("message",d),d=null),await E())}const w=[c.event_types.CHARACTER_MESSAGE_RENDERED,c.event_types.USER_MESSAGE_RENDERED,c.event_types.MESSAGE_UPDATED,c.event_types.MESSAGE_SWIPED];async function E(){await A(),void 0!==c.this_chid&&(await(0,c.reloadCurrentChat)(),await S())}async function S(){await h(p.FULL),log.info("[Render] 渲染所有iframe")}async function C(e){const t=parseInt($("#render-depth").val(),10),n=(0,l.getContext)().chat.length;if(t>0){if(e<n-t)return}await h(p.PARTIAL,e),log.info("[Render] 渲染"+e+"号消息的iframe")}function T(e){const t=$(e);if(t.length){if(window._sharedResizeObserver&&window._observedElements)for(const[t,n]of window._observedElements.entries())if(n.iframe===e){window._sharedResizeObserver.unobserve(t),window._observedElements.delete(t);break}t.remove(),0===window._observedElements?.size&&window._sharedResizeObserver&&(window._sharedResizeObserver.disconnect(),window._sharedResizeObserver=void 0,log.info("[Render] 所有iframe已移除，停止观察"))}}async function A(){$('iframe[id^="message-iframe"]').each(function(){T(this)})}function x(){const e=new MutationObserver(e=>{e.forEach(e=>{e.removedNodes.length&&e.removedNodes.forEach(e=>{if(e instanceof HTMLIFrameElement)T(e);else if(e instanceof HTMLElement){const t=e.querySelectorAll("iframe");t.length&&t.forEach(e=>{T(e)})}})})});return e.observe(document.body,{childList:!0,subtree:!0}),e}function I(e){const t=(0,l.getContext)(),n=parseInt($("#render-depth").val(),10),a=t.chat.length,r=e-1,i=e=>t.chat[e]??{},s=e=>{const t=$('[id^="message-iframe-'+e+'-"]');return t.length>0?t.get(0):null},o=e=>/```[\s\S]*?```/.test(e.mes);if(0===n){const e=i(r),t=o(e),n=s(r);if(!t&&!n)return;T(n),(0,c.updateMessageBlock)(r,e),C(r)}else{let e=a-n;e<0&&(e=0);for(let t=e;t<=r;t++){const e=i(t),n=o(e),a=s(t);(n||a)&&(T(a),(0,c.updateMessageBlock)(t,e),C(t))}}}},7810:(e,t,n)=>{n.d(t,{h:()=>l});var a=n(1613),r=n(1988),i=n(4365),s=n(363),o=n.n(s);const c={GLOBAL:"settings_updated",CHARACTER:"character_variables_changed",MESSAGE:"message_variables_changed",CHAT:""};class l{domUpdater;model;currentType=null;_boundListeners={global:{bound:!1,handler:this._handleVariableUpdate.bind(this,"global")},character:{bound:!1,handler:this._handleVariableUpdate.bind(this,"character")},chat:{bound:!1,handler:this._handleVariableUpdate.bind(this,"chat")},message:{bound:!1,handler:this._handleVariableUpdate.bind(this,"message")}};_chatPollingInterval=null;constructor(e,t){this.domUpdater=e,this.model=t}async cleanup(){this._unbindAllEventListeners(),this._stopChatPolling(),this.currentType=null}async initCurrentType(){this.currentType="global",this._activateCurrentListeners()}async setCurrentType(e){this.currentType!==e&&(this._deactivateCurrentListeners(),this.currentType=e,this._activateCurrentListeners())}reactivateListeners(){this._deactivateCurrentListeners(),this._activateCurrentListeners()}deactivateListeners(){this._deactivateCurrentListeners()}_activateCurrentListeners(){this.currentType&&("chat"===this.currentType?this._startChatPolling():this._bindVariableListener(this.currentType))}_deactivateCurrentListeners(){this._unbindAllEventListeners(),this._stopChatPolling()}_bindVariableListener(e){if("chat"===e)return;const t=c[e.toUpperCase()],n=this._boundListeners[e];if(!n.bound&&t)try{i.eventSource.on(t,n.handler),this._boundListeners[e].bound=!0,o().info(`[VariableManager]：开始同步监听${e}变量`)}catch(t){o().error(`[VariableManager]：绑定${e}变量事件监听器时出错:`,t),this._boundListeners[e].bound=!1}}_unbindAllEventListeners(){for(const e of Object.keys(this._boundListeners)){if("chat"===e)continue;const t=c[e.toUpperCase()],n=this._boundListeners[e];if(n.bound&&t)try{i.eventSource.removeListener(t,n.handler),this._boundListeners[e].bound=!1}catch(t){o().error(`[VariableManager]：解绑${e}变量事件监听器时出错:`,t),this._boundListeners[e].bound=!1}}}_startChatPolling(){if(this._stopChatPolling(),"chat"===this.currentType)try{this._chatPollingInterval=window.setInterval(async()=>{await this._handleVariableUpdate("chat")},2e3)}catch(e){o().error("[VariableManager]：启动聊天变量轮询时出错:",e),this._chatPollingInterval=null}}_stopChatPolling(){if(null!==this._chatPollingInterval)try{window.clearInterval(this._chatPollingInterval),this._chatPollingInterval=null}catch(e){o().error("[VariableManager]：停止聊天变量轮询时出错:",e)}}async _handleVariableUpdate(e,t){if(this.currentType&&this.currentType===e)try{let n,a;("character"===e||"message"===e)&&t&&t.variables?(n=t.variables,a=t.message_id):n=(0,r.UN)({type:e});const i=this.model.getCurrentMapVariables(),{added:s,removed:o,updated:c}=this._compareTavernVariables(i,n,a);this._processDiff(s,o,c,n,a)}catch(t){o().error(`[VariableManager]：处理${e}变量更新时出错:`,t)}}_compareTavernVariables(e,t,n){const a=new Map;e.forEach(e=>{void 0!==n?e.message_id===n&&a.set(e.name,e.value):void 0===e.message_id&&a.set(e.name,e.value)});const r=new Set(a.keys()),i=new Set(Object.keys(t));return{added:[...i].filter(e=>!r.has(e)),removed:[...r].filter(e=>!i.has(e)),updated:[...i].filter(e=>r.has(e)&&!_.isEqual(a.get(e),t[e]))}}_processDiff(e,t,n,r,i){const s=[],o=[],c=[];e.forEach(e=>{const t=this.model.createVariableItem(e,r[e],i);this.domUpdater.addVariableCard(t),s.push(t)}),t.forEach(e=>{const t=this.model.findVariableByName(e,i);t&&(this.domUpdater.removeVariableCard(t.id),o.push(t.id))}),n.forEach(e=>{const t=this.model.findVariableByName(e,i);t&&(t.value=r[e],t.dataType=a.w.inferDataType(t.value),this.domUpdater.updateVariableCard(t),c.push(t))}),this.model.syncCurrentVariables(s,o,c)}}},8020:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.d(t,{F:()=>y});var r=n(5403),i=n(3766),s=n(6491),o=n(9506),c=n(6530),l=n(2445),d=n(4365),p=n(9489),h=n(4249),u=n(3892),f=n(363),m=n.n(f),g=n(190),_=e([g]);g=(_.then?(await _)():_)[0];var v=n.n(g);class y{static instance;scriptManager;buttonManager;templatePath;baseTemplate=null;defaultScriptTemplate=null;folderTemplate=null;batchModeGlobal=!1;batchModeCharacter=!1;constructor(){this.scriptManager=s.D.getInstance(),this.buttonManager=new r.Ks,this.templatePath=`${c.VE}/src/component/script_repository/public`}static getInstance(){return y.instance||(y.instance=new y),y.instance}static destroyInstance(){y.instance&&(y.instance.cleanup(),y.instance=void 0)}cleanup(){this.buttonManager.clearButtons(),this.baseTemplate=null,this.defaultScriptTemplate=null,this.folderTemplate=null}async initialize(){await this.initializeTemplates(),this.setupScriptRepositoryEvents(),this.registerEventListeners(),await this.renderScriptLists(),(0,r.ol)(),i.hj.emit(i._9.UI_LOADED)}async initializeTemplates(){this.baseTemplate=$(await(0,p.renderExtensionTemplateAsync)(this.templatePath,"script_item_template",{scriptName:"",id:"",moveTo:"",faIcon:""})),this.defaultScriptTemplate=$(await(0,p.renderExtensionTemplateAsync)(this.templatePath,"script_default_repository",{scriptName:"",id:""})),this.folderTemplate=$(await(0,p.renderExtensionTemplateAsync)(this.templatePath,"folder_template",{folderId:"",folderName:"",folderIcon:"fa-folder",folderColor:document.documentElement.style.getPropertyValue("--SmartThemeBodyColor")}))}setupScriptRepositoryEvents(){$("#global-script-enable-toggle").prop("checked",this.scriptManager.isGlobalScriptEnabled).on("click",e=>{i.hj.emit(i._9.TYPE_TOGGLE,{type:o.eF.GLOBAL,enable:e.target.checked,userInput:!0})}),$("#character-script-enable-toggle").prop("checked",this.scriptManager.isCharacterScriptEnabled).on("click",e=>{i.hj.emit(i._9.TYPE_TOGGLE,{type:o.eF.CHARACTER,enable:e.target.checked,userInput:!0})}),$("#create-script").on("click",async()=>{await this.showCreateScriptDialog()}),$("#import-script-file").on("change",async function(){let e="global";const t=$(await(0,p.renderExtensionTemplateAsync)(`${c.VE}/src/component/script_repository/public`,"script_target_selector",{title:"导入到:",prefix:"script-import",globalLabel:"全局脚本",characterLabel:"局部脚本"}));t.find("#script-import-target-global").on("input",()=>e="global"),t.find("#script-import-target-character").on("input",()=>e="character");if(await(0,h.callGenericPopup)(t,h.POPUP_TYPE.CONFIRM,"",{okButton:"确认",cancelButton:"取消"})){const t=this instanceof HTMLInputElement&&this;if(t&&t.files){for(const n of t.files)i.hj.emit(i._9.SCRIPT_IMPORT,{file:n,type:"global"===e?o.eF.GLOBAL:o.eF.CHARACTER});t.value=""}}}),$("#import-script").on("click",function(){$("#import-script-file").trigger("click")}),$("#create-folder").on("click",()=>{this.showCreateFolderDialog()}),$("#default-script").on("click",()=>{i.hj.emit(i._9.UI_REFRESH,{action:"load_default_scripts"})}),$("#extensions_settings").css("min-width","0"),this.setupSearchEvents(),this.setupBatchOperationEvents()}registerEventListeners(){i.hj.on(i._9.UI_REFRESH,async e=>{const{action:t}=e;switch(t){case"script_toggle":await this.refreshScriptState(e.script,e.enable),this.updateParentFolderToggle(e.script.id,e.type);break;case"type_toggle":await this.refreshTypeState(e.type,e.enable);break;case"script_import":case"script_create":await this.addScriptToContainer(e.script,e.type);break;case"script_update":this.updateScriptUI(e.script,e.type);break;case"script_delete":this.removeScriptElement(e.scriptId);break;case"script_move":this.handleScriptMoved(e.script,e.fromType,e.targetType);break;case"folder_move":await this.renderScriptLists();break;case"folder_scripts_toggle":this.updateFolderAndScriptsUI(e.folderId,e.type,e.enable);break;case"load_default_scripts":await this.loadDefaultScriptsRepository();break;case"refresh_global_scripts":await this.refreshScriptList(o.eF.GLOBAL);break;case"refresh_charact_scripts":await this.refreshScriptList(o.eF.CHARACTER);break;default:m().warn(`[ScriptManager] 未处理的UI刷新事件: ${t}`)}}),i.hj.on(i._9.BUTTON_ADD,e=>{const{script:t}=e;this.addButton(t)}),i.hj.on(i._9.BUTTON_REMOVE,e=>{const{scriptId:t}=e;this.buttonManager.removeButtonsByScriptId(t)}),i.hj.on(i._9.SCRIPT_EDIT,async e=>{const{type:t,scriptId:n}=e;await this.openScriptEditor(t,n)}),i.hj.on(i._9.FOLDER_CREATE,async e=>{const{name:t,type:n,icon:a,color:r}=e;await this.handleFolderCreate(t,n,a,r)}),i.hj.on(i._9.FOLDER_EDIT,async e=>{const{folderId:t,newName:n,type:a,newIcon:r,newColor:i}=e;await this.handleFolderEdit(t,n,a,r,i)}),i.hj.on(i._9.FOLDER_DELETE,async e=>{const{folderId:t,type:n}=e;await this.handleFolderDelete(t,n)}),i.hj.on(i._9.FOLDER_SCRIPTS_TOGGLE,async e=>{const{folderId:t,type:n,enable:a}=e;await this.handleFolderScriptsToggle(t,n,a)})}addButton(e){e.buttons&&e.buttons.length>0&&this.buttonManager.addButtonsForScript(e)}async renderScriptLists(){this.clearScriptList(o.eF.GLOBAL),this.clearScriptList(o.eF.CHARACTER),await this.renderRepository(o.eF.GLOBAL),await this.renderRepository(o.eF.CHARACTER)}async renderRepository(e){const t=e===o.eF.GLOBAL?this.scriptManager.getGlobalRepositoryItems():this.scriptManager.getCharacterRepositoryItems();if(t.length>0)for(const n of t)"folder"===n.type?await this.renderFolder(n,e):await this.addScriptToContainer(n.value,e);else this.showEmptyScriptListTip(e);this.setupDraggable(e)}clearScriptList(e){$((0,o.v7)(e)).empty()}async refreshScriptList(e){this.clearScriptList(e),await this.renderRepository(e);const t=e===o.eF.GLOBAL?this.scriptManager.isGlobalScriptEnabled:this.scriptManager.isCharacterScriptEnabled,n=(0,o.L5)(e);$(n).prop("checked",t)}showEmptyScriptListTip(e){const t=$((0,o.v7)(e));0===t.find("small").length&&t.append("<small>暂无脚本</small>")}async refreshScriptState(e,t){const n=$(`#${e.id}`);n.length>0&&(t?(n.find(".script-toggle").addClass("enabled"),n.find(".script-toggle i.fa-toggle-off").hide(),n.find(".script-toggle i.fa-toggle-on").show()):(n.find(".script-toggle").removeClass("enabled"),n.find(".script-toggle i.fa-toggle-off").show(),n.find(".script-toggle i.fa-toggle-on").hide()))}async refreshTypeState(e,t){$((0,o.L5)(e)).prop("checked",t)}updateFolderAndScriptsUI(e,t,n){const a=$(`#${e}`);if(a.length>0){this.updateFolderToggleState(a,e,t);a.find(".folder-content").find(".script-item").each((e,t)=>{const a=$(t).find(".script-toggle");n?(a.addClass("enabled"),a.find("i.fa-toggle-off").hide(),a.find("i.fa-toggle-on").show()):(a.removeClass("enabled"),a.find("i.fa-toggle-off").show(),a.find("i.fa-toggle-on").hide())})}}updateScriptUI(e,t){const n=$(`#${CSS.escape(e.id)}`);if(0===n.length)return void this.addScriptToContainer(e,t);n.find(".script-item-name").text(e.name);const a=n.find(".script-toggle");e.enabled?(a.addClass("enabled"),a.find("i.fa-toggle-off").hide(),a.find("i.fa-toggle-on").show()):(a.removeClass("enabled"),a.find("i.fa-toggle-off").show(),a.find("i.fa-toggle-on").hide());const r=n.find(".script-storage-location");r.removeClass("move-to-character move-to-global"),r.addClass("global"===t?"move-to-character":"move-to-global");const i=r.find("i");i.removeClass("fa-arrow-down fa-arrow-up"),i.addClass("global"===t?"fa-arrow-down":"fa-arrow-up"),this.rebindScriptEvents(n,e,t)}rebindScriptEvents(e,t,n){e.find(".script-toggle").off("click.scriptToggle"),e.find(".script-info").off("click.scriptInfo"),e.find(".edit-script").off("click.scriptEdit"),e.find(".script-storage-location").off("click.scriptMove"),e.find(".export-script").off("click.scriptExport"),e.find(".delete-script").off("click.scriptDelete");e.find(".script-toggle").on("click.scriptToggle",function(){const e=!$(this).hasClass("enabled");e?($(this).addClass("enabled"),$(this).find("i.fa-toggle-off").hide(),$(this).find("i.fa-toggle-on").show()):($(this).removeClass("enabled"),$(this).find("i.fa-toggle-off").show(),$(this).find("i.fa-toggle-on").hide()),t.enabled=e,i.hj.emit(i._9.SCRIPT_TOGGLE,{script:t,type:n,enable:e,userInput:!0})}),e.find(".script-info").on("click.scriptInfo",()=>{const e=t.info||"",n=(0,l.G)(e);(0,h.callGenericPopup)(n,h.POPUP_TYPE.DISPLAY,void 0,{wide:!0})}),e.find(".edit-script").on("click.scriptEdit",e=>{e.preventDefault(),e.stopPropagation(),i.hj.emit(i._9.SCRIPT_EDIT,{type:n,scriptId:t.id})}),e.find(".script-storage-location").on("click.scriptMove",()=>{i.hj.emit(i._9.SCRIPT_MOVE,{script:t,fromType:n})}),e.find(".export-script").on("click.scriptExport",async()=>{const e=`${t.name.replace(/[\s.<>:"/\\|?*\x00-\x1F\x7F]/g,"_").toLowerCase()}.json`,n=await this.checkScriptDataAndGetExportData(t);if(null===n)return;const a=JSON.stringify(n,null,2);(0,u.download)(a,e,"application/json")}),e.find(".delete-script").on("click.scriptDelete",async()=>{await(0,h.callGenericPopup)("确定要删除这个脚本吗？",h.POPUP_TYPE.CONFIRM)&&(i.hj.emit(i._9.SCRIPT_DELETE,{scriptId:t.id,type:n}),e.remove())})}removeScriptElement(e){$(`#${e}`).remove()}handleScriptMoved(e,t,n){$(`#${e.id}`).remove();0===(t===o.eF.GLOBAL?$("#global-script-list"):$("#character-script-list")).children().length&&this.showEmptyScriptListTip(t),i.hj.emit(i._9.UI_REFRESH,{action:"script_import",script:e,type:n})}async addScriptToContainer(e,t){const n=await this.createScriptElement(e,t),a=$((0,o.v7)(t)),r=a.find("small");r.length>0&&r.remove(),a.append(n)}setupDraggable(e){$((0,o.v7)(e)).sortable({delay:(0,u.getSortableDelay)(),items:".script-item, .script-folder",handle:".drag-handle",cursor:"move",tolerance:"pointer",placeholder:"sortable-placeholder",connectWith:".folder-content",stop:async t=>{await this.handleDragStop(e)}}),this.setupFolderDropZones(e)}setupFolderDraggable(e){$((0,o.v7)(e)).find(".folder-content").sortable({delay:(0,u.getSortableDelay)(),items:".script-item",handle:".drag-handle",cursor:"move",tolerance:"pointer",placeholder:"sortable-placeholder",connectWith:`${(0,o.v7)(e)}, .folder-content`,stop:async t=>{await this.handleDragStop(e)}})}setupFolderDropZones(e){$((0,o.v7)(e)).find(".script-folder").each((t,n)=>{this.setupSingleFolderDropZone($(n),e)})}setupSingleFolderDropZone(e,t){const n=e.find(".folder-content");e.off(".dragdrop"),e.on("dragover dragenter dragleave drop",function(e){return e.preventDefault(),e.stopPropagation(),!1}),e.droppable({accept:'.script-item[data-script-repository="true"]',tolerance:"pointer",greedy:!0,over:(t,a)=>{if(!a.draggable.hasClass("script-item")||!a.draggable.attr("data-script-repository"))return;a.draggable.closest(".script-folder").attr("id")===e.attr("id")||(e.addClass("folder-drag-target"),n.is(":visible")||(n.slideDown(200),e.find(".folder-toggle").addClass("expanded")))},out:(t,n)=>{if(!n.draggable.hasClass("script-item")||!n.draggable.attr("data-script-repository"))return;n.draggable.closest(".script-folder").attr("id")===e.attr("id")||e.removeClass("folder-drag-target")},drop:async(n,a)=>{if(n.originalEvent&&(n.originalEvent.preventDefault(),n.originalEvent.stopPropagation()),n.preventDefault(),n.stopImmediatePropagation(),!a.draggable.hasClass("script-item")||!a.draggable.attr("data-script-repository"))return;const r=a.draggable.attr("id"),i=e.attr("id");if(a.draggable.closest(".script-folder").attr("id")===i)return e.removeClass("folder-drag-target"),!1;if(r&&i)try{if(!await this.handleScriptDropToFolder(r,i,t,a.draggable))return e.removeClass("folder-drag-target"),setTimeout(async()=>{await this.renderScriptLists()},100),!1}catch(t){return m().error("[ScriptManager] 拖拽脚本到文件夹失败:",t),toastr.error(`移动脚本失败: ${t instanceof Error?t.message:String(t)}`),e.removeClass("folder-drag-target"),setTimeout(async()=>{await this.renderScriptLists()},100),!1}return e.removeClass("folder-drag-target"),!1}})}async handleScriptDropToFolder(e,t,n,a){try{const r=(n===o.eF.GLOBAL?this.scriptManager.getGlobalRepositoryItems():this.scriptManager.getCharacterRepositoryItems()).find(e=>"folder"===e.type&&e.id===t);if(r){const t=r.value;if(t.some(t=>t.id===e))return toastr.info("脚本已经在该文件夹中"),!1}await this.scriptManager.moveScriptToFolder(e,t,n);const i=$(`#${t}`),s=i.find(".folder-content");return s.is(":visible")||(s.show(),i.find(".folder-toggle").addClass("expanded")),a.detach().prependTo(s),!0}catch(e){throw m().error("[ScriptManager] 移动脚本到文件夹失败:",e),e}}async handleDragStop(e){try{const t=await this.buildRepositoryFromDOM(e);i.hj.emit(i._9.ORDER_CHANGED,{data:t,type:e})}catch(e){m().error("[ScriptManager] handleDragStop: 处理拖拽结束失败:",e)}}async buildRepositoryFromDOM(e){const t=$((0,o.v7)(e)),n=[];for(const e of t.children().get()){const t=$(e);if(t.hasClass("script-folder")){const e=t.attr("id"),a=t.find(".folder-name").text(),r=t.find(".folder-icon i").attr("class")?.split(" ").find(e=>e.startsWith("fa-"))||"fa-folder",i=t.find(".folder-icon").css("color")||document.documentElement.style.getPropertyValue("--SmartThemeBodyColor"),s=[],o=t.find(".folder-content");for(const e of o.children(".script-item").get()){const t=$(e).attr("id"),n=this.scriptManager.getScriptById(t);n&&s.push(n)}n.push({type:"folder",id:e,name:a,icon:r,color:i,value:s})}else if(t.hasClass("script-item")){const e=t.attr("id"),a=this.scriptManager.getScriptById(e);a&&n.push({type:"script",value:a})}}return n}async cloneDefaultScriptTemplate(e){this.defaultScriptTemplate||await this.initializeTemplates();const t=this.defaultScriptTemplate.clone(),n=`default_lib_${e.id}`;return t.attr("id",n),t.find(".script-item-name").text(e.name),t.find(".script-info").on("click",()=>{const t=(0,l.G)(e.info);(0,h.callGenericPopup)(t,h.POPUP_TYPE.DISPLAY,void 0,{wide:!0})}),t.find(".add-script").on("click",async()=>{let t=o.eF.GLOBAL;const n=$(await(0,p.renderExtensionTemplateAsync)(this.templatePath,"script_target_selector",{title:"添加到:",prefix:"script-add",globalLabel:"全局脚本库",characterLabel:"角色脚本库"}));n.find("#script-add-target-global").on("input",()=>t=o.eF.GLOBAL),n.find("#script-add-target-character").on("input",()=>t=o.eF.CHARACTER);if(!await(0,h.callGenericPopup)(n,h.POPUP_TYPE.CONFIRM,"",{okButton:"确认",cancelButton:"取消"}))return;const a=new o.Nh({...e,enabled:!1});let r="new";const s=this.scriptManager.getScriptById(e.id);if(s){switch(await(0,h.callGenericPopup)(`要导入的脚本 '${e.name}' 与脚本库中的 '${s.name}' id 相同，是否要导入？`,h.POPUP_TYPE.TEXT,"",{okButton:"覆盖原脚本",cancelButton:"取消",customButtons:["新建脚本"]})){case 0:r="cancel";break;case 1:r="override";break;case 2:r="new"}}switch(r){case"new":s&&(a.id=(0,u.uuidv4)());break;case"override":if(!s)return;$(`#${s.id}`).remove(),s.enabled&&(await this.scriptManager.stopScript(s,t),this.buttonManager.removeButtonsByScriptId(s.id));break;case"cancel":return}i.hj.emit(i._9.SCRIPT_CREATE,{script:a,type:t}),toastr.success(`脚本"${a.name}"已添加到${t===o.eF.GLOBAL?"全局":"角色"}脚本库`)}),t}createDefaultScriptContainer(){return $('<div class="default-script-repository-container"></div>')}async loadDefaultScriptsRepository(){const e=(await n.e(487).then(n.bind(n,2487))).createDefaultScripts,t=this.createDefaultScriptContainer(),a=await e();for(const e of a){if(!e)continue;const n=await this.cloneDefaultScriptTemplate(e);t.append(n)}await(0,h.callGenericPopup)(t,h.POPUP_TYPE.DISPLAY,"",{wide:!0})}async openScriptEditor(e,t){const n=$(await(0,p.renderExtensionTemplateAsync)(this.templatePath,"script_editor"));let a;this.updateVariableListVisibility(n),t&&(a=this.scriptManager.getScriptById(t),a&&(n.find("#script-name-input").val(a.name),n.find("#script-content-textarea").val(a.content),n.find("#script-info-textarea").val(a.info),a.data&&Object.keys(a.data).length>0&&this.loadVariablesToEditor(n,a.data),a.buttons&&a.buttons.length>0&&a.buttons.forEach((e,t)=>{const a=$(`\n              <div class="button-item" id="button-${t}">\n                <span class="drag-handle menu-handle">☰</span>\n                <input type="checkbox" id="checkbox-button-${t}" class="button-visible" ${e.visible?"checked":""}>\n                <input class="text_pole button-name" type="text" id="text-button-${t}" value="${e.name}" placeholder="按钮名称">\n                <div class="delete-button menu_button interactable" data-index="${t}">\n                  <i class="fa-solid fa-trash"></i>\n                </div>\n              </div>\n            `);n.find(".button-list").append(a)}))),n.find("#add-variable-trigger").on("click",()=>{this.addVariableToEditor(n)}),n.find("#add-button-trigger").on("click",()=>{const e=n.find(".button-list .button-item").length,t=`button-${e}`,a=$(`<div class="button-item" id="${t}">\n        <span class="drag-handle menu-handle">☰</span>\n        <input type="checkbox" id="checkbox-${t}" class="button-visible" checked>\n        <input class="text_pole button-name" type="text" id="text-${t}" placeholder="按钮名称">\n        <div class="delete-button menu_button interactable" data-index="${e}">\n          <i class="fa-solid fa-trash"></i>\n        </div>\n      </div>`);n.find(".button-list").append(a)}),n.find("#script-button-content .button-list").sortable({handle:".drag-handle",items:".button-item"}),n.on("click",".delete-button",e=>{$(e.currentTarget).closest(".button-item").remove()}),n.on("click",".delete-variable",e=>{$(e.currentTarget).closest(".variable-item").remove(),this.updateVariableListVisibility(n)});if(!await(0,h.callGenericPopup)(n,h.POPUP_TYPE.CONFIRM,"",{wide:!0,okButton:"保存",cancelButton:"取消"}))return;const r=String(n.find("#script-name-input").val()),s=String(n.find("#script-content-textarea").val()),c=String(n.find("#script-info-textarea").val()),l=this.collectVariablesFromEditor(n),d=[];if(n.find(".button-list .button-item").each(function(){const e=$(this).find(".button-name").val(),t=$(this).find(".button-visible").prop("checked");e&&""!==e.trim()&&d.push({name:e,visible:t})}),a){const t=a.enabled;if(i.hj.emit(i._9.BUTTON_REMOVE,{scriptId:a.id}),a.name=r,a.content=s,a.info=c,a.buttons=d,a.data=l,i.hj.emit(i._9.SCRIPT_UPDATE,{script:a,type:e}),t)try{await this.scriptManager.stopScript(a,e),await this.scriptManager.runScript(a,e)}catch(e){m().error(`[ScriptManager] 重启脚本失败: ${a.name}`,e),toastr.error(`重启脚本失败: ${a.name}`)}}else a=new o.Nh({id:(0,u.uuidv4)(),name:r,content:s,info:c,enabled:!1,buttons:d,data:l}),i.hj.emit(i._9.SCRIPT_CREATE,{script:a,type:e})}loadVariablesToEditor(e,t){const n=e.find("#variable-list");for(const[e,a]of Object.entries(t)){const t=this.createVariableItem(e,a);n.append(t)}this.updateVariableListVisibility(e)}addVariableToEditor(e){const t=e.find("#variable-list"),n=this.createVariableItem("","");t.append(n),this.updateVariableListVisibility(e)}updateVariableListVisibility(e){const t=e.find("#variable-list");0===t.find(".variable-item").length?t.hide():t.show()}createVariableItem(e,t){const n="string"==typeof t?t:v().stringify(t);return $(`\n      <div class="variable-item flex-container flexFlowColumn width100p">\n        <div class="flex flexFlowColumn">\n        <div class="flex-container alignitemscenter spaceBetween wide100p">\n          <div>名称:</div>\n          <div class="menu_button interactable delete-variable" title="删除变量">\n            <i class="fa-solid fa-trash"></i>\n          </div>\n          </div>\n          <input type="text" class="text_pole variable-key" value="${this.escapeHtml(e)}" placeholder="变量名">\n        </div>\n        <div class="flex flexFlowColumn" style="align-items: flex-start;">\n          <div>值:</div>\n          <textarea class="text_pole variable-value" style="min-height: 12px;" rows="1" placeholder="请以纯文本或YAML格式输入变量值">${this.escapeHtml(n)}</textarea>\n        </div>\n      <hr>\n      </div>\n    `)}collectVariablesFromEditor(e){const t={};return e.find("#variable-list .variable-item").each(function(){const e=$(this).find(".variable-key").val(),n=$(this).find(".variable-value").val();if(e&&""!==e.trim()){let a;try{a=v().parse(n)}catch{a=n}t[e.trim()]=a}}),t}escapeHtml(e){const t=document.createElement("div");return t.textContent=e,t.innerHTML}async checkEmbeddedScripts(e){const t=(0,c.CN)("script.characters_with_scripts")||[],n=d.characters[e]?.avatar;if(t.includes(n))return;const a=`AlertScript_${n}`;if(!localStorage.getItem(a)){localStorage.setItem(a,"true");const e=await(0,p.renderExtensionTemplateAsync)(`${c.VE}/src/component/script_repository/public`,"script_allow_popup");await(0,h.callGenericPopup)(e,h.POPUP_TYPE.CONFIRM,"",{okButton:"确认",cancelButton:"取消"})?(n&&!t.includes(n)&&(t.push(n),(0,c.P1)("script.characters_with_scripts",t)),$("#character-script-enable-toggle").prop("checked",!0),i.hj.emit(i._9.TYPE_TOGGLE,{type:o.eF.CHARACTER,enable:!0,userInput:!1})):($("#character-script-enable-toggle").prop("checked",!1),i.hj.emit(i._9.TYPE_TOGGLE,{type:o.eF.CHARACTER,enable:!1,userInput:!1}))}}async renderFolder(e,t){const n=$((0,o.v7)(t));this.folderTemplate||await this.initializeTemplates();const a=e.id,r=e.name,i=e.icon||"fa-folder",s=e.color||document.documentElement.style.getPropertyValue("--SmartThemeBodyColor"),c="global"===t?"fa-arrow-down":"fa-arrow-up",l="global"===t?"move-to-character":"move-to-global",d="global"===t?"移动到角色脚本库":"移动到全局脚本库",h=$(await(0,p.renderExtensionTemplateAsync)(this.templatePath,"folder_template",{folderId:a,folderName:r,folderIcon:i,folderColor:s,moveIcon:c,moveClass:l,moveTitle:d}));this.bindFolderEvents(h,a,s,t);const u=e.value,f=h.find(".folder-content");for(const e of u){const n=await this.createScriptElement(e,t);f.append(n)}n.append(h),this.setupFolderDraggable(t),this.setupSingleFolderDropZone(h,t)}bindFolderEvents(e,t,n,a){e.find(".folder-header").on("click",t=>{if($(t.target).closest(".folder-control").length>0)return;if($(t.target).closest(".folder-checkbox").length>0||$(t.target).hasClass("folder-checkbox"))return;const n=e.find(".folder-content"),a=e.find(".folder-toggle");n.is(":visible")?(n.slideUp(),a.removeClass("expanded")):(n.slideDown(),a.addClass("expanded"))});const r=e.find(".folder-script-toggle");r.on("click",e=>{e.stopPropagation();const n=!r.hasClass("enabled");n?(r.addClass("enabled"),r.find("i.fa-toggle-off").hide(),r.find("i.fa-toggle-on").show()):(r.removeClass("enabled"),r.find("i.fa-toggle-off").show(),r.find("i.fa-toggle-on").hide()),i.hj.emit(i._9.FOLDER_SCRIPTS_TOGGLE,{folderId:t,type:a,enable:n})}),e.find(".folder-edit").on("click",e=>{e.stopPropagation(),this.showEditFolderDialog(t,a)}),e.find(".folder-export").on("click",e=>{e.stopPropagation(),this.exportFolder(t,a)}),e.find(".folder-move").on("click",e=>{e.stopPropagation(),i.hj.emit(i._9.FOLDER_MOVE,{folderId:t,fromType:a})}),e.find(".folder-delete").on("click",e=>{e.stopPropagation(),this.showDeleteFolderDialog(t,a)}),e.find(".folder-icon").css("color",n),this.updateFolderToggleState(e,t,a)}updateFolderToggleState(e,t,n){const a=this.scriptManager.getFolderScriptsState(t,n),r=e.find(".folder-script-toggle");r.removeClass("enabled"),"all"===a?(r.addClass("enabled"),r.find("i.fa-toggle-off").hide(),r.find("i.fa-toggle-on").show()):(r.find("i.fa-toggle-off").show(),r.find("i.fa-toggle-on").hide())}updateParentFolderToggle(e,t){const n=t===o.eF.GLOBAL?this.scriptManager.getGlobalRepositoryItems():this.scriptManager.getCharacterRepositoryItems();for(const a of n)if("folder"===a.type){if(a.value.some(t=>t.id===e)){const e=$(`#${a.id}`);e.length>0&&this.updateFolderToggleState(e,a.id,t);break}}}async createScriptElement(e,t){this.baseTemplate||await this.initializeTemplates();const n=this.baseTemplate.clone();n.attr("id",e.id),n.attr("data-script-repository","true"),n.find(".script-item-name").text(e.name),n.find(".script-storage-location").addClass("global"===t?"move-to-character":"move-to-global"),n.find(".script-storage-location i").addClass("global"===t?"fa-arrow-down":"fa-arrow-up");const a=n.find(".script-toggle");return e.enabled?(a.addClass("enabled"),a.find("i.fa-toggle-off").hide(),a.find("i.fa-toggle-on").show()):(a.removeClass("enabled"),a.find("i.fa-toggle-off").show(),a.find("i.fa-toggle-on").hide()),this.bindScriptEvents(n,e,t),n}async checkScriptDataAndGetExportData(e){const{enabled:t,...n}=e;if(e.data&&Object.keys(e.data).length>0)try{switch(await(0,h.callGenericPopup)(`脚本 "${e.name}" 包含数据，导出时如何处理？如有API-KEY等敏感数据，注意清除`,h.POPUP_TYPE.TEXT,"",{okButton:"直接导出（包含数据）",cancelButton:"取消",customButtons:["清除数据后导出"]})){case 0:default:return null;case 1:return n;case 2:return{...n,data:{}}}}catch(e){return m().error("[ScriptManager] 导出脚本数据选择对话框出错:",e),n}return n}bindScriptEvents(e,t,n){e.find(".script-toggle").on("click.scriptToggle",function(){const e=!$(this).hasClass("enabled");e?($(this).addClass("enabled"),$(this).find("i.fa-toggle-off").hide(),$(this).find("i.fa-toggle-on").show()):($(this).removeClass("enabled"),$(this).find("i.fa-toggle-off").show(),$(this).find("i.fa-toggle-on").hide()),t.enabled=e,i.hj.emit(i._9.SCRIPT_TOGGLE,{script:t,type:n,enable:e,userInput:!0})}),e.find(".script-info").on("click.scriptInfo",()=>{const e=t.info||"",n=(0,l.G)(e);(0,h.callGenericPopup)(n,h.POPUP_TYPE.DISPLAY,void 0,{wide:!0})}),e.find(".edit-script").on("click.scriptEdit",e=>{e.preventDefault(),e.stopPropagation(),i.hj.emit(i._9.SCRIPT_EDIT,{type:n,scriptId:t.id})}),e.find(".script-storage-location").on("click.scriptMove",()=>{i.hj.emit(i._9.SCRIPT_MOVE,{script:t,fromType:n})}),e.find(".export-script").on("click.scriptExport",async()=>{const e=`${t.name.replace(/[\s.<>:"/\\|?*\x00-\x1F\x7F]/g,"_").toLowerCase()}.json`,n=await this.checkScriptDataAndGetExportData(t);if(null===n)return;const a=JSON.stringify(n,null,2);(0,u.download)(a,e,"application/json")}),e.find(".delete-script").on("click.scriptDelete",async()=>{await(0,h.callGenericPopup)("确定要删除这个脚本吗？",h.POPUP_TYPE.CONFIRM)&&(i.hj.emit(i._9.SCRIPT_DELETE,{scriptId:t.id,type:n}),e.remove())})}async showCreateScriptDialog(){let e=o.eF.GLOBAL;const t=$(await(0,p.renderExtensionTemplateAsync)(this.templatePath,"script_target_selector",{title:"新建脚本到:",prefix:"script-create",globalLabel:"全局脚本库",characterLabel:"角色脚本库"}));t.find("#script-create-target-global").on("input",()=>e=o.eF.GLOBAL),t.find("#script-create-target-character").on("input",()=>e=o.eF.CHARACTER);await(0,h.callGenericPopup)(t,h.POPUP_TYPE.CONFIRM,"",{okButton:"确认",cancelButton:"取消"})&&i.hj.emit(i._9.SCRIPT_EDIT,{type:e})}async showCreateFolderDialog(){const e=document.documentElement.style.getPropertyValue("--SmartThemeBodyColor"),t=$(await(0,p.renderExtensionTemplateAsync)(this.templatePath,"folder_create",{defaultColor:e},!1));let n;t.find("#folder-icon-preview").on("click",async()=>{try{const e=await(0,u.showFontAwesomePicker)();e&&""!==e.trim()&&(t.find("#folder-icon-preview").removeClass().addClass(`fa ${e}`),t.find("#folder-icon-value").val(e))}catch(e){m().error("[ScriptManager] 图标选择失败:",e)}}),t.find("#folder-color-picker").on("change",e=>{n=e.detail?.rgba});if(!await(0,h.callGenericPopup)(t,h.POPUP_TYPE.CONFIRM,"",{okButton:"创建",cancelButton:"取消"}))return;const a=t.find("#folder-name-input").val(),r=t.find("#folder-icon-value").val(),s=t.find("#folder-target-global").prop("checked")?o.eF.GLOBAL:o.eF.CHARACTER;if(a&&""!==a.trim())try{const e=s===o.eF.GLOBAL?this.scriptManager.getGlobalRepositoryItems():this.scriptManager.getCharacterRepositoryItems();if(e.some(e=>"folder"===e.type&&e.name===a.trim()))return void toastr.error(`${s===o.eF.GLOBAL?"全局":"角色"}脚本库中已存在名为 "${a.trim()}" 的文件夹`);i.hj.emit(i._9.FOLDER_CREATE,{name:a.trim(),type:s,icon:r,color:n})}catch(e){m().error("[ScriptManager] 创建文件夹失败:",e),toastr.error(`创建文件夹失败: ${e instanceof Error?e.message:String(e)}`)}else toastr.error("请输入文件夹名称")}async showEditFolderDialog(e,t){const n=$(`#${e} .folder-name`).text(),a=(t===o.eF.GLOBAL?this.scriptManager.getGlobalRepositoryItems():this.scriptManager.getCharacterRepositoryItems()).find(t=>"folder"===t.type&&t.id===e),r=a?.icon||"fa-folder",s=a?.color||document.documentElement.style.getPropertyValue("--SmartThemeBodyColor");let c;const l=$(`\n      <div class="folder-edit-dialog">\n      <h3>编辑文件夹</h3>\n      <div class="flex-container flexFlowColumn wide100p padding10 justifyLeft">\n        <div>\n          <h4>文件夹名称:</h4>\n          <input type="text" id="folder-name-input" class="text_pole" value="${n}" />\n        </div>\n        <div>\n          <h4>文件夹图标:</h4>\n          <div class="flex" style="gap: 20px; margin: 5px 0; width: 100%;">\n            <div class="flex alignItemsCenter">\n            <label>选择颜色</label>\n            <toolcool-color-picker id="folder-color-picker" color="${s}"></toolcool-color-picker>\n            </div>\n            <div class="flex-container alignItemsCenter">\n             <label>选择图标</label>\n              <i id="folder-icon-preview" class="fa ${r} marginRight10" style="cursor: pointer; font-size: 12px; padding: 4px; border: 1px solid #ccc; border-radius: 4px;" title="点击选择图标"></i>\n              <input type="hidden" id="folder-icon-value" value="${r}" />\n            </div>\n          </div>\n        </div>\n      </div>\n    `);l.find("#folder-icon-preview").on("click",async()=>{try{const e=await(0,u.showFontAwesomePicker)();e&&""!==e.trim()&&(l.find("#folder-icon-preview").removeClass().addClass(`fa ${e}`),l.find("#folder-icon-value").val(e))}catch(e){m().error("[ScriptManager] 图标选择失败:",e)}}),l.find("#folder-color-picker").on("change",e=>{c=e.detail?.rgba});if(!await(0,h.callGenericPopup)(l,h.POPUP_TYPE.CONFIRM,"",{okButton:"保存",cancelButton:"取消"}))return;const d=l.find("#folder-name-input").val(),p=l.find("#folder-icon-value").val();i.hj.emit(i._9.FOLDER_EDIT,{folderId:e,newName:d?.trim()||n,type:t,newIcon:p,newColor:c})}async exportFolder(e,t){try{const a=(t===o.eF.GLOBAL?this.scriptManager.getGlobalRepositoryItems():this.scriptManager.getCharacterRepositoryItems()).find(t=>"folder"===t.type&&t.id===e);if(!a)return void toastr.error("未找到指定的文件夹");window.JSZip||await Promise.resolve().then(n.bind(n,2730));const r=new JSZip,i=(a.name||"folder").replace(/[<>:"/\\|?*]/g,"_");if(Array.isArray(a.value)){let e=!1;for(const t of a.value){const n=await this.checkScriptDataAndGetExportData(t);if(null===n){e=!0;break}const a=`${t.name.replace(/[<>:"/\\|?*]/g,"_")}.json`;r.file(`${i}/${a}`,JSON.stringify(n,null,2))}if(e)return}const s=await r.generateAsync({type:"blob"}),c=(new Date).toISOString().slice(0,19).replace(/:/g,"-"),l=`folder_${i}_${t===o.eF.GLOBAL?"global":"character"}_${c}.zip`,d=URL.createObjectURL(s),p=document.createElement("a");p.href=d,p.download=l,document.body.appendChild(p),p.click(),document.body.removeChild(p),URL.revokeObjectURL(d),toastr.success(`文件夹 "${a.name}" 导出成功`)}catch(e){m().error("[ScriptManager] 导出文件夹失败:",e),toastr.error(`导出文件夹失败: ${e instanceof Error?e.message:String(e)}`)}}async showDeleteFolderDialog(e,t){const n=(t===o.eF.GLOBAL?this.scriptManager.getGlobalRepositoryItems():this.scriptManager.getCharacterRepositoryItems()).find(t=>"folder"===t.type&&t.id===e),a=n?.name||"未知文件夹";await(0,h.callGenericPopup)(`确定要删除文件夹 "${a}" 及其中的所有脚本吗？此操作不可撤销。`,h.POPUP_TYPE.CONFIRM,"",{okButton:"删除",cancelButton:"取消"})&&i.hj.emit(i._9.FOLDER_DELETE,{folderId:e,type:t})}setupSearchEvents(){const e=$("#script-search-input");e.on("input",e=>{const t=e.target.value.trim();t?this.performSearch(t):this.clearSearch()}),e.on("keypress",t=>{if(13===t.which){const t=e.val();t&&t.trim()&&this.performSearch(t.trim())}})}performSearch(e){if(!e)return void this.clearSearch();const t=this.scriptManager.getGlobalScripts(),n=this.scriptManager.getCharacterScripts(),a=this.searchScripts(t,e),r=this.searchScripts(n,e);this.displaySearchResults(a,r)}searchScripts(e,t){const n=t.toLowerCase();return e.filter(e=>e.name.toLowerCase().includes(n))}async displaySearchResults(e,t){this.clearScriptList(o.eF.GLOBAL),this.clearScriptList(o.eF.CHARACTER);const n=$("#global-script-list");if(e.length>0)for(const t of e)await this.addScriptToContainer(t,o.eF.GLOBAL);else n.append("<small>无匹配的全局脚本</small>");const a=$("#character-script-list");if(t.length>0)for(const e of t)await this.addScriptToContainer(e,o.eF.CHARACTER);else a.append("<small>无匹配的角色脚本</small>")}clearSearch(){this.renderScriptLists()}setupBatchOperationEvents(){$("#global-batch-manager").on("click",()=>{this.toggleBatchMode(o.eF.GLOBAL)}),$("#character-batch-manager").on("click",()=>{this.toggleBatchMode(o.eF.CHARACTER)}),$("#global-batch-delete").on("click",()=>{this.performBatchDelete(o.eF.GLOBAL)}),$("#global-batch-export").on("click",()=>{this.performBatchExport(o.eF.GLOBAL)}),$("#global-batch-move").on("click",()=>{this.performBatchMove(o.eF.GLOBAL)}),$("#global-batch-cancel").on("click",()=>{this.exitBatchMode(o.eF.GLOBAL)}),$("#character-batch-delete").on("click",()=>{this.performBatchDelete(o.eF.CHARACTER)}),$("#character-batch-export").on("click",()=>{this.performBatchExport(o.eF.CHARACTER)}),$("#character-batch-move").on("click",()=>{this.performBatchMove(o.eF.CHARACTER)}),$("#character-batch-cancel").on("click",()=>{this.exitBatchMode(o.eF.CHARACTER)})}toggleBatchMode(e){(e===o.eF.GLOBAL?this.batchModeGlobal:this.batchModeCharacter)?this.exitBatchMode(e):this.enterBatchMode(e)}enterBatchMode(e){const t=(0,o.v7)(e),n=(0,o.C$)(e);e===o.eF.GLOBAL?this.batchModeGlobal=!0:this.batchModeCharacter=!0,$(n).show();const a=$(t);a.find(".script-item").each((e,t)=>{const n=$(t);n.addClass("batch-mode"),n.find(".script-checkbox").show(),n.find(".drag-handle").hide(),n.find(".script-item-control").hide(),n.find(".script-checkbox").off("change.batch").on("change.batch",()=>{this.handleCheckboxChange(n)})}),a.find(".script-folder").each((e,t)=>{const n=$(t);n.addClass("batch-mode"),n.find(".folder-checkbox").show(),n.find(".drag-handle").hide(),n.find(".folder-control .folder-script-toggle").hide(),n.find(".folder-control .folder-edit").hide(),n.find(".folder-control .folder-export").hide(),n.find(".folder-control .folder-move").hide(),n.find(".folder-control .folder-delete").hide(),n.find(".folder-checkbox").off("change.batch").on("change.batch",()=>{this.handleCheckboxChange(n)})})}exitBatchMode(e){const t=(0,o.v7)(e),n=(0,o.C$)(e);e===o.eF.GLOBAL?this.batchModeGlobal=!1:this.batchModeCharacter=!1,$(n).hide();$(t).find(".script-item, .script-folder").each((e,t)=>{const n=$(t);n.removeClass("batch-mode selected"),n.find(".script-checkbox, .folder-checkbox").hide().prop("checked",!1),n.find(".script-item-control").show(),n.find(".drag-handle").show(),n.find(".script-controls").show(),n.find(".folder-control .folder-script-toggle").show(),n.find(".folder-control .folder-edit").show(),n.find(".folder-control .folder-export").show(),n.find(".folder-control .folder-move").show(),n.find(".folder-control .folder-delete").show(),n.find(".script-checkbox, .folder-checkbox").off("change.batch")})}handleCheckboxChange(e){e.find(".script-checkbox, .folder-checkbox").prop("checked")?e.addClass("selected"):e.removeClass("selected")}getSelectedItems(e){const t=(0,o.v7)(e),n=[],a=[];return $(t).find(".script-item.selected").each((e,t)=>{const a=$(t).attr("id");a&&n.push(a)}),$(t).find(".script-folder.selected").each((e,t)=>{const n=$(t).attr("id");n&&a.push(n)}),{scriptIds:n,folderIds:a}}async performBatchDelete(e){const{scriptIds:t,folderIds:n}=this.getSelectedItems(e);if(0===t.length&&0===n.length)return void toastr.warning("请至少选择一个项目进行删除");const a=t.length+n.length;if(await(0,h.callGenericPopup)(`确定要删除选中的 ${a} 个项目吗？此操作不可撤销。`,h.POPUP_TYPE.CONFIRM,"",{okButton:"删除",cancelButton:"取消"}))try{for(const n of t)await this.scriptManager.deleteScript(n,e);for(const t of n)await this.handleFolderDelete(t,e,!0);toastr.success(`成功删除 ${a} 个项目`),this.exitBatchMode(e),await this.renderScriptLists()}catch(e){m().error("[ScriptManager] 批量删除失败:",e),toastr.error(`批量删除失败: ${e instanceof Error?e.message:String(e)}`)}}async performBatchExport(e){const{scriptIds:t,folderIds:a}=this.getSelectedItems(e);if(0!==t.length||0!==a.length)try{window.JSZip||await Promise.resolve().then(n.bind(n,2730));const r=new JSZip;let i=0,s=!1;for(const e of t){const t=this.scriptManager.getScriptById(e);if(t){const e=await this.checkScriptDataAndGetExportData(t);if(null===e){s=!0;break}const n=`${t.name.replace(/[<>:"/\\|?*]/g,"_")}.json`;r.file(n,JSON.stringify(e,null,2)),i++}}if(s)return;const c=e===o.eF.GLOBAL?this.scriptManager.getGlobalRepositoryItems():this.scriptManager.getCharacterRepositoryItems();for(const e of a){const t=c.find(t=>"folder"===t.type&&t.id===e);if(t){const e=(t.name||"folder").replace(/[<>:"/\\|?*]/g,"_");if(Array.isArray(t.value)){for(const n of t.value){const t=await this.checkScriptDataAndGetExportData(n);if(null===t){s=!0;break}const a=`${n.name.replace(/[<>:"/\\|?*]/g,"_")}.json`;r.file(`${e}/${a}`,JSON.stringify(t,null,2))}if(s)break}i++}}if(s)return;if(0===i)return void toastr.error("没有找到有效的导出数据");const l=await r.generateAsync({type:"blob"}),d=(new Date).toISOString().slice(0,19).replace(/:/g,"-"),p=`batch_export_${e===o.eF.GLOBAL?"global":"character"}_${d}.zip`,h=URL.createObjectURL(l),u=document.createElement("a");u.href=h,u.download=p,document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(h),toastr.success(`成功导出 ${i} 个项目`)}catch(e){m().error("[ScriptManager] 批量导出失败:",e),toastr.error(`批量导出失败: ${e instanceof Error?e.message:String(e)}`)}else toastr.warning("请至少选择一个项目进行导出")}async performBatchMove(e){const{scriptIds:t,folderIds:n}=this.getSelectedItems(e);if(n.length>0)toastr.error("不能移动文件夹，请只选择脚本进行移动操作");else if(0!==t.length)try{const n=(e===o.eF.GLOBAL?this.scriptManager.getGlobalRepositoryItems():this.scriptManager.getCharacterRepositoryItems()).filter(e=>"folder"===e.type);if(0===n.length)return void toastr.error("没有可用的文件夹，请先创建一个文件夹");const a=n.map(e=>`<option value="${e.id}">${e.name}</option>`).join(""),r=$(`\n        <div>\n          <p>选择要移动到的文件夹：</p>\n          <select id="target-folder-select" class="text_pole" style="width: 100%;">\n            <option value="">根目录</option>\n            ${a}\n          </select>\n        </div>\n      `);if(!await(0,h.callGenericPopup)(r,h.POPUP_TYPE.CONFIRM,"",{okButton:"移动",cancelButton:"取消"}))return;const i=r.find("#target-folder-select").val()||null;for(const n of t)await this.scriptManager.moveScriptToFolder(n,i,e);const s=i?n.find(e=>e.id===i)?.name||"未知文件夹":"根目录";toastr.success(`成功将 ${t.length} 个脚本移动到"${s}"`),this.exitBatchMode(e),await this.renderScriptLists()}catch(e){m().error("[ScriptManager] 批量移动失败:",e),toastr.error(`批量移动失败: ${e instanceof Error?e.message:String(e)}`)}else toastr.warning("请至少选择一个脚本进行移动")}async handleFolderCreate(e,t,n,a){try{await this.scriptManager.createFolder(e,t,n,a),await this.renderScriptLists(),toastr.success(`文件夹 "${e}" 创建成功`)}catch(e){m().error("[ScriptManager] 创建文件夹失败:",e),toastr.error(`创建文件夹失败: ${e instanceof Error?e.message:String(e)}`)}}async handleFolderEdit(e,t,n,a,r){try{await this.scriptManager.editFolder(e,t,n,a,r),t&&$(`#${e} .folder-name`).text(t),a&&$(`#${e} .folder-icon`).removeClass().addClass(`fa ${a} folder-icon marginLeft5`),r&&($(`#${e} .folder-icon`).css("color",r),$(`#${e} .folder-header`).css("border-left-color",r)),toastr.success("文件夹更新成功")}catch(e){m().error("[ScriptManager] 更新文件夹失败:",e),toastr.error(`更新文件夹失败: ${e instanceof Error?e.message:String(e)}`)}}async handleFolderDelete(e,t,n=!1){try{await this.scriptManager.deleteFolder(e,t),n||(await this.renderScriptLists(),toastr.success("文件夹删除成功"))}catch(e){throw m().error("[ScriptManager] 删除文件夹失败:",e),n||toastr.error(`删除文件夹失败: ${e instanceof Error?e.message:String(e)}`),e}}async handleFolderScriptsToggle(e,t,n){try{await this.scriptManager.toggleFolderScripts(e,t,n),this.updateFolderAndScriptsUI(e,t,n)}catch(e){m().error("[ScriptManager] 批量切换文件夹脚本状态失败:",e),toastr.error(`批量切换脚本状态失败: ${e instanceof Error?e.message:"未知错误"}`)}}}a()}catch(e){a(e)}})},8242:(e,t,n)=>{n.d(t,{K:()=>i});var a=n(8853);function r(){const e=Object.keys(a.SlashCommandParser.commands).filter(e=>a.SlashCommandParser.commands[e].name===e).sort((e,t)=>e.toLowerCase().localeCompare(t.toLowerCase())).map(e=>a.SlashCommandParser.commands[e]),t=e=>({is_required:e.isRequired,default_value:e.defaultValue??void 0,accepts_multiple:e.acceptsMultiple,enum_list:e.enumList.length>0?e.enumList.map(e=>e.value):void 0,type_list:e.typeList.length>0?e.typeList:void 0}),n=e=>({name:e.name,...t(e)});return e.map(e=>{return{name:e.name,named_args:e.namedArgumentList.map(n)??[],unnamed_args:e.unnamedArgumentList.map(t)??[],return_type:e.returns??"void",help_string:(a=e.helpString,$("<span>").html(a).text().split("\n").map(e=>e.trim()).filter(e=>""!==e).join(" ")??"NO DETAILS")};var a}).map(e=>`/${e.name}${e.named_args.length>0?" ":""}${e.named_args.map(e=>`[${e.accepts_multiple?"...":""}${e.name}=${e.enum_list?e.enum_list.join("|"):e.type_list?e.type_list.join("|"):""}]${e.is_required?"":"?"}${e.default_value?`=${e.default_value}`:""}`).join(" ")}${e.unnamed_args.length>0?" ":""}${e.unnamed_args.map(e=>`(${e.accepts_multiple?"...":""}${e.enum_list?e.enum_list.join("|"):e.type_list?e.type_list.join("|"):""})${e.is_required?"":"?"}${e.default_value?`=${e.default_value}`:""}`).join(" ")} // ${e.help_string}`).join("\n")}async function i(){$("#download_slash_commands").on("click",function(){const e=URL.createObjectURL(new Blob([r()],{type:"text/plain"}));$(this).attr("href",e),$(this).attr("download","slash_command.txt"),setTimeout(()=>URL.revokeObjectURL(e),0)})}},8337:(e,t,n)=>{n.d(t,{N:()=>a});class a{$container;$header;$content;options;isAnimating=!1;static initAll(e,t={}){const n=[];return $(e).each(function(){n.push(new a($(this),t))}),n}constructor(e,t={}){this.$container=e;this.options={headerSelector:".collapsible-header",contentSelector:".collapsible-content",expandedClass:"expanded",animationDuration:{expand:280,collapse:250},easingFunction:"swing",initiallyExpanded:!1,callbacks:{},...t},this.$header=this.$container.find(this.options.headerSelector),this.$content=this.$container.find(this.options.contentSelector),this.initEvents(),this.options.initiallyExpanded?(this.$container.addClass(this.options.expandedClass),this.$content.show()):(this.$container.removeClass(this.options.expandedClass),this.$content.hide())}initEvents(){this.$header.on("click",e=>{this.shouldIgnoreClick(e.target)||this.toggle()})}shouldIgnoreClick(e){const t=$(e);return!!(t.hasClass("toggle-switch")||t.hasClass("toggle-input")||t.hasClass("toggle-label")||t.hasClass("toggle-handle")||t.closest(".toggle-switch").length>0)||(!!(t.hasClass("menu_button")||t.closest(".menu_button").length>0||t.hasClass("TavernHelper-button")||t.closest(".TavernHelper-button").length>0)||!!(t.is("input, select, textarea, button, a")||t.closest("input, select, textarea, button, a").length>0))}toggle(){this.isAnimating||(this.$container.hasClass(this.options.expandedClass)?this.collapse():this.expand())}expand(){this.isAnimating||this.$container.hasClass(this.options.expandedClass)||(this.isAnimating=!0,this.options.callbacks?.beforeExpand&&this.options.callbacks.beforeExpand(),this.$container.addClass(this.options.expandedClass),this.$content.addClass("animating"),this.$content.slideDown({duration:this.options.animationDuration.expand,easing:this.options.easingFunction,start:function(){$(this).css({opacity:0,transform:"translateY(-10px) scaleY(0.95)"})},progress:function(e,t){$(this).css({opacity:t,transform:`translateY(${-10*(1-t)}px) scaleY(${.95+.05*t})`})},complete:()=>{this.$content.css({opacity:"",transform:""}).removeClass("animating"),this.isAnimating=!1,this.options.callbacks?.afterExpand&&this.options.callbacks.afterExpand()}}))}collapse(){!this.isAnimating&&this.$container.hasClass(this.options.expandedClass)&&(this.isAnimating=!0,this.options.callbacks?.beforeCollapse&&this.options.callbacks.beforeCollapse(),this.$container.removeClass(this.options.expandedClass),this.$content.addClass("animating"),this.$content.slideUp({duration:this.options.animationDuration.collapse,easing:this.options.easingFunction,start:function(){$(this).css({opacity:1,transform:"translateY(0) scaleY(1)"})},progress:function(e,t){const n=1-t;$(this).css({opacity:n,transform:`translateY(${-5*t}px) scaleY(${1-.05*t})`})},complete:()=>{this.$content.css({opacity:"",transform:""}).removeClass("animating"),this.isAnimating=!1,this.options.callbacks?.afterCollapse&&this.options.callbacks.afterCollapse()}}))}isExpanded(){return this.$container.hasClass(this.options.expandedClass)}destroy(){this.$header.off("click")}}},8628:(e,t,n)=>{e.exports=(e=>{var t={};return n.d(t,e),t})({getRegexedString:()=>h.getRegexedString,regex_placement:()=>h.regex_placement})},8652:(e,t,n)=>{n.d(t,{O:()=>f,m:()=>u});var a=n(6523),r=n(9897),i=n(9854),s=n(4365),o=n(363),c=n.n(o);const l=new Map;function d(){0!==l.size&&(c().info("[(deprecated)Script] 清理全局脚本..."),l.forEach((e,t)=>{e.remove()}),l.clear(),c().info("[(deprecated)Script] 全局脚本清理完成!"))}async function p(){try{d();const e=function(){const e=e=>e.scriptName.replace(/^【.*】/,"").startsWith("脚本-"),t=e=>!e.disabled,n=e=>e.scriptName.replace(/^【.*】/,"").replace("脚本-",""),r=[],i=(0,a.TE)().filter(e).filter(t);r.push(...i);const s=(0,a.XT)().filter(e).filter(t).filter(e=>!!(0,a.il)()||e.runOnEdit);return r.push(...s),r.map(e=>({name:n(e),code:e.replaceString}))}();c().info(`[(deprecated)Script] 加载全局脚本: ${JSON.stringify(e.map(e=>e.name))}`);const t=[];e.forEach(e=>{const{iframe:n,load_promise:a}=function(e){const t=document.createElement("iframe");t.style.display="none",t.id=`script-iframe-${e.name}`;const n=`\n    <html>\n    <head>\n      ${i.A}\n      <script src="${r.S.get("iframe_client")}"><\/script>\n    </head>\n    <body>\n      ${e.code}\n    </body>\n    </html>\n  `;t.srcdoc=n;const a=new Promise(e=>{t.onload=()=>{c().info(`[(deprecated)Script](${t.id}) 加载完毕`),e()}});return document.body.appendChild(t),{iframe:t,load_promise:a}}(e);l.set(e.name,n),t.push(a)}),await Promise.allSettled(t)}catch(e){throw c().error("[(deprecated)Script] 全局脚本加载失败:",e),e}}const h=[s.event_types.CHAT_CHANGED];function u(){p(),h.forEach(e=>{s.eventSource.makeFirst(e,p)})}function f(){h.forEach(e=>{s.eventSource.removeListener(e,p)}),d()}},8853:(e,t,n)=>{e.exports=(e=>{var t={};return n.d(t,e),t})({SlashCommandParser:()=>u.SlashCommandParser})},8961:(e,t,n)=>{n.d(t,{FF:()=>c,RV:()=>l});var a=n(5022),r=n(7723),i=n(515),s=n(6530),o=n(4365);const c={render_enabled:!0,tampermonkey_compatibility:!1,render_depth:0,render_optimize:!1};async function l(){const e=(0,s.CN)("render.render_optimize")??c.render_optimize;e&&await h(!0,!1),$("#render-optimize-toggle").prop("checked",e).on("click",e=>h(e.target.checked,!0));const t=(0,s.CN)("render.render_depth")??c.render_depth;$("#render-depth").val(t).on("blur",function(e){!async function(e){const t=parseInt(e,10);if(t<0)return toastr.warning("处理深度不能为负数"),void $("#render-depth").val((0,s.CN)("render.render_depth"));(0,s.P1)("render.render_depth",t),await(0,r.ZX)()}(e.target.value)});const n=(0,s.CN)("render.tampermonkey_compatibility")??c.tampermonkey_compatibility;n&&(0,r.Dl)(!0,!1),$("#tampermonkey-compatibility-toggle").prop("checked",n).on("click",e=>(0,r.Dl)(e.target.checked,!0));const a=(0,s.CN)("render.render_enabled")??c.render_enabled;await d(a,!1),$("#render-enable-toggle").prop("checked",a).on("click",e=>d(e.target.checked,!0));const i=(0,s.CN)("render.render_hide_style");i&&await p(!0,!1),$("#render-hide-style-toggle").prop("checked",i).on("click",e=>p(e.target.checked,!0)),$(window).on("resize",function(){$('iframe[data-needs-vh="true"]').length&&(0,r.r5)()}),function(){const e=$(`\n  <div id="tavern-helper-render-container" class="list-group-item flex-container flexGap5 interactable">\n      <div class="fa-solid fa-puzzle-piece extensionsMenuExtensionButton" /></div>\n      <span id="tavern-helper-render-text">${(0,s.CN)("render.render_enabled")?"关闭前端渲染":"开启前端渲染"}</span>\n  </div>`);e.css("display","flex"),$("#extensionsMenu").append(e),$("#tavern-helper-render-container").on("click",async function(){const e=(0,s.CN)("render.render_enabled")??c.render_enabled;await d(!e,!0)})}(),function(){if($("#iframe-loading-styles").length)return;const e=$("<style>",{id:"iframe-loading-styles",text:"\n        .iframe-loading-overlay{\n          position:absolute;\n          top:0;\n          left:0;\n          right:0;\n          bottom:0;\n          background:rgba(0,0,0,.7);\n          display:flex;\n          justify-content:center;\n          align-items:center;\n          z-index:1000;\n          transition:opacity .3s ease\n        }\n        .iframe-loading-content{\n          color:#fff;\n          display:flex;\n          align-items:center;\n          gap:10px;\n          font-size:16px\n        }\n        .iframe-loading-content i{\n          font-size:20px\n        }\n        .loading-text {\n          transition: opacity 0.3s ease;\n        }"});$("head").append(e)}(),(0,r.eV)()}async function d(e,t=!0){!function(e){$("#render-enable-toggle").prop("checked",e),$("#tavern-helper-render-text").text(e?"关闭前端渲染":"开启前端渲染")}(e),t&&(0,s.P1)("render.render_enabled",e),e?($("#render-settings-content .extension-content-item").slice(3).css("opacity",1),await(0,r.LV)()):($("#render-settings-content .extension-content-item").slice(3).css("opacity",.5),await(0,r.Yk)(),t&&void 0!==o.this_chid&&await(0,o.reloadCurrentChat)())}async function p(e,t=!0){t&&(0,s.P1)("render.render_hide_style",e),e?((0,a.Un)(),t&&await(0,r.ZX)()):((0,a.jh)(),t&&await(0,r.ZX)())}async function h(e,t=!0){t&&(0,s.P1)("render.render_optimize",e),e?((0,i.m)(),t&&await(0,r.ZX)()):((0,i.j)(),t&&await(0,r.ZX)())}},9489:(e,t,n)=>{e.exports=(e=>{var t={};return n.d(t,e),t})({extensionTypes:()=>f.extensionTypes,extension_settings:()=>f.extension_settings,getContext:()=>f.getContext,renderExtensionTemplateAsync:()=>f.renderExtensionTemplateAsync,saveMetadataDebounced:()=>f.saveMetadataDebounced,writeExtensionField:()=>f.writeExtensionField})},9506:(e,t,n)=>{n.d(t,{C$:()=>h,Hl:()=>o,Kh:()=>c,L5:()=>u,Nh:()=>i,RU:()=>d,eF:()=>a,oI:()=>l,v7:()=>p,y8:()=>s});var a,r=n(3892);class i{id;name;content;info;buttons;data;enabled;constructor(e){this.id=e?.id&&""!==e.id.trim()?e.id:(0,r.uuidv4)(),this.name=e?.name||"",this.content=e?.content||"",this.info=e?.info||"",this.enabled=e?.enabled||!1,this.buttons=e?.buttons||[],this.data=e?.data||{}}}!function(e){e.GLOBAL="global",e.CHARACTER="character"}(a||(a={}));const s={global_script_enabled:!0,scriptsRepository:[],characters_with_scripts:[]};function o(e){return e&&"object"==typeof e&&"type"in e&&"value"in e}function c(e){return e&&"object"==typeof e&&"id"in e&&"name"in e&&"content"in e}function l(e){const t=[];for(const n of e)if(c(n))t.push(n);else if(o(n))if("script"===n.type)t.push(n.value);else if("folder"===n.type){const e=n.value;t.push(...e)}return t}function d(e){return e===a.GLOBAL?a.CHARACTER:a.GLOBAL}function p(e){return e===a.GLOBAL?"#global-script-list":"#character-script-list"}function h(e){return e===a.GLOBAL?"#global-batch-controls":"#character-batch-controls"}function u(e){return e===a.GLOBAL?"#global-script-enable-toggle":"#character-script-enable-toggle"}},9736:(e,t,n)=>{n.a(e,async(e,a)=>{try{n.d(t,{G:()=>u});var r=n(3303),i=n(1613),s=n(6521),o=n(4249),c=n(1553),l=n(3892),d=n(363),p=n.n(d),h=e([r]);r=(h.then?(await h)():h)[0];class u{static MIN_DIALOG_WIDTH=300;static MIN_DIALOG_HEIGHT=250;container;cardFactory;dialog=null;controller=null;constructor(e){this.container=e,this.cardFactory=new r.L(this.showVariableTypeDialog.bind(this))}setController(e){this.controller=e}initUI(){this.container.find("#global-tab").addClass("active"),this.container.find("#global-content").addClass("active"),this.container.find("#floor-filter-container").hide(),this.initSortable(),this.initFloorFilter()}initSortable(){this.container.find(".list-items-container").sortable({delay:(0,l.getSortableDelay)(),handle:".drag-handle",stop:function(e,t){const n=$(t.item).closest(".list-items-container"),a=[];n.find(".variable-content-input").each(function(){a.push($(this).val())});const r=_.uniqBy(a,e=>e.trim().toLowerCase());_.isEqual(a,r)||(n.empty(),r.forEach(e=>{const t=`\n              <div class="list-item">\n                <span class="drag-handle">☰</span>\n                <textarea class="variable-content-input">${e}</textarea>\n                <button class="list-item-delete"><i class="fa-solid fa-times"></i></button>\n              </div>\n            `;n.append(t)})),n.trigger("sortupdate")}})}initFloorFilter(){this.container.find("#floor-filter-btn").on("click",()=>{const e=this.container.find("#floor-min").val(),t=this.container.find("#floor-max").val(),n=e.trim()?parseInt(e,10):null,a=t.trim()?parseInt(t,10):null;e.trim()&&isNaN(n)||t.trim()&&isNaN(a)?this.showFloorFilterError("请输入有效的楼层数值"):null!==n&&n<0?this.showFloorFilterError("最小楼层不能小于0"):null!==a&&a<0?this.showFloorFilterError("最大楼层不能小于0"):null!==n&&null!==a&&a<n?this.showFloorFilterError("最大楼层不能小于最小楼层"):null!==n||null!==a?this.hideFloorFilterError():this.showFloorFilterError("请至少设置最小楼层或最大楼层")}),this.container.find("#floor-min, #floor-max").on("input",()=>{const e=this.container.find("#floor-min").val(),t=this.container.find("#floor-max").val(),n=e.trim()?parseInt(e,10):null,a=t.trim()?parseInt(t,10):null;null!==n&&n<0?this.showFloorFilterError("最小楼层不能小于0"):null!==a&&a<0?this.showFloorFilterError("最大楼层不能小于0"):null!==n&&null!==a&&!isNaN(n)&&!isNaN(a)&&a<n?this.showFloorFilterError("最大楼层不能小于最小楼层"):this.hideFloorFilterError()})}showFloorFilterError(e){this.container.find("#floor-filter-error").text(e).show()}hideFloorFilterError(){this.container.find("#floor-filter-error").hide()}updateFloorRangeInputs(e,t){this.container.find("#floor-min").val(null!==e?e.toString():""),this.container.find("#floor-max").val(null!==t?t.toString():""),this.hideFloorFilterError()}getActiveVariableType(){return this.container.find(".tab-item.active").attr("id")?.replace("-tab","")||"chat"}setActiveTab(e){this.container.find(".tab-item").removeClass("active"),this.container.find(".tab-content").removeClass("active"),this.container.find(`#${e}-tab`).addClass("active"),this.container.find(`#${e}-content`).addClass("active");const t=this.container.find("#floor-filter-container");if("message"===e){t.show();const[e,n]=this.getFloorRange();if(null===e&&null===n){const e=(0,s.nw)(),t=Math.max(0,e-4),n=e;this.controller.model.updateFloorRange(t,n),this.updateFloorRangeInputs(t,n)}}else t.hide()}getFloorRange(){const e=this.container.find("#floor-min").val(),t=this.container.find("#floor-max").val();let n=e&&e.trim()?parseInt(e,10):null,a=t&&t.trim()?parseInt(t,10):null;return null!==n&&(n=Math.max(0,n)),null!==a&&(a=Math.max(0,a)),[null===n||isNaN(n)?null:n,null===a||isNaN(a)?null:a]}refreshVariableCards(e,t){this.container.find(".variable-type-label").text(`${e}变量`);const n=this.container.find(`#${e}-content`).find(".variable-list"),a=n.scrollTop()||0;if(n.empty(),0!==t.length)if("message"===e)this.renderMessageVariablesByFloor(n,t),n.scrollTop(a);else{for(const e of t){const t=this.cardFactory.createCard(e);n.append(t)}n.scrollTop(a)}else n.html('<div class="empty-state"><p>暂无变量</p></div>')}applyClientSideFilters(e,t){const n=this.getActiveVariableType(),a=this.container.find(`#${n}-content .variable-card`);let r=0;a.each((n,a)=>{const i=$(a),s=i.attr("data-type"),o=i.find(".variable-title").val()||"",c=e[s],l=!t||o.toLowerCase().includes(t.toLowerCase());c&&l?(i.show(),r++):i.hide()}),this.updateFilterEmptyState(n,r)}updateFilterEmptyState(e,t){const n=this.container.find(`#${e}-content .variable-list`),a=n.find(".filter-empty-state");if(0===t){n.find(".variable-card").length>0&&0===a.length&&n.append('<div class="filter-empty-state"><p>没有符合筛选条件的变量</p></div>')}else a.remove()}renderMessageVariablesByFloor(e,t){const n=new Map;for(const e of t)void 0!==e.message_id&&(n.has(e.message_id)||n.set(e.message_id,[]),n.get(e.message_id).push(e));const a=Array.from(n.keys()).sort((e,t)=>t-e);for(let t=0;t<a.length;t++){const r=a[t],i=n.get(r),s=0===t,o=this.createFloorPanel(r,s),c=o.find(".floor-panel-body");for(const e of i){const t=this.cardFactory.createCard(e);t.attr("data-floor-id",r.toString()),c.append(t)}e.append(o)}}createFloorPanel(e,t){const n=$(`\n      <div class="floor-panel" data-floor="${e}">\n        <div class="floor-panel-header flex spaceBetween alignItemsCenter">\n          <div class="floor-panel-title">${`# ${e} 楼`}</div>\n          <div class="floor-panel-icon ${t?"expanded":""}">\n            <i class="fa-solid fa-chevron-down"></i>\n          </div>\n        </div>\n        <div class="floor-panel-body ${t?"expanded":""}"></div>\n      </div>\n    `);return n.find(".floor-panel-header").on("click",function(){const e=$(this),t=e.find(".floor-panel-icon"),n=e.closest(".floor-panel").find(".floor-panel-body");t.toggleClass("expanded"),n.toggleClass("expanded")}),n}addNewVariableCard(e,t,n){const a=this.container.find(`#${e}-content`).find(".variable-list");a.find(".empty-state").remove();const r={name:"new_variable",value:i.w.getDefaultValueForType(t),dataType:t,id:(0,l.uuidv4)()},s=this.cardFactory.createCard(r);s.attr("data-type",t),"message"===e&&void 0!==n?this.addCardToFloorPanel(a,s,n):(a.prepend(s),this.addAnimation(s,"variable-added",()=>{}))}addCardToFloorPanel(e,t,n){let a=e.find(`.floor-panel[data-floor="${n}"]`);if(0===a.length){const t=this.createFloorPanel(n,!0);this.insertFloorPanelInOrder(e,t,n),a=t}const r=a.find(".floor-panel-body");r.hasClass("expanded")||(a.find(".floor-panel-icon").addClass("expanded"),r.addClass("expanded")),t.attr("data-floor-id",n.toString()),r.prepend(t),this.addAnimation(t,"variable-added",()=>{})}insertFloorPanelInOrder(e,t,n){let a=!1;e.find(".floor-panel").each(function(){const e=parseInt($(this).attr("data-floor")||"0");if(n>e)return $(this).before(t),a=!0,!1}),a||e.prepend(t)}getVariableFromCard(e){return this.cardFactory.getVariableFromCard(e)}getVariableCardName(e){const t=this.getVariableFromCard(e);return t?.name||""}async showAddVariableDialog(e){"message"!==this.getActiveVariableType()?await this.showVariableTypeDialog(t=>{e(t)}):await this.showFloorInputDialog(async t=>{null!==t&&await this.showVariableTypeDialog(n=>{e(n,t)})})}async showFloorInputDialog(e){const t=$('\n      <div>\n        <h3>输入楼层号码</h3>\n        <div class="floor-input-dialog">\n          <input type="number" id="floor-input" min="0" placeholder="请输入楼层号码" />\n          <div id="floor-input-error" class="floor-filter-error" style="display: none">请输入有效的楼层号码</div>\n        </div>\n      </div>\n    '),n=t.find("#floor-input"),a=t.find("#floor-input-error"),r=(0,s.nw)();r>=0&&n.val(r),n.on("input",function(){const e=parseInt($(this).val(),10);$(this).val()&&(isNaN(e)||e<0)?a.text("楼层号码不能小于0").show():a.hide()});if(!await(0,o.callGenericPopup)(t,o.POPUP_TYPE.CONFIRM,"",{okButton:"确认",cancelButton:"取消"}))return void e(null);const i=n.val(),c=parseInt(i,10);return!i.trim()||isNaN(c)?(a.text("请输入有效的楼层号码").show(),void setTimeout(()=>this.showFloorInputDialog(e),100)):c<0?(a.text("楼层号码不能小于0").show(),void setTimeout(()=>this.showFloorInputDialog(e),100)):void e(c)}async showVariableTypeDialog(e){const t=$('\n      <div>\n        <h3>选择变量类型</h3>\n        <div class="variable-type-options">\n          <div data-type="string"><i class="fa-regular fa-font"></i> 字符串</div>\n          <div data-type="number"><i class="fa-regular fa-hashtag"></i> 数字</div>\n          <div data-type="boolean"><i class="fa-regular fa-toggle-on"></i> 布尔值</div>\n          <div data-type="array"><i class="fa-regular fa-list"></i> 数组</div>\n          <div data-type="object"><i class="fa-regular fa-code"></i> 对象</div>\n        </div>\n      </div>\n    ');t.find(".variable-type-options div").on("click",function(){const t=$(this).attr("data-type");e(t),$(".popup").find(".popup-button-close").trigger("click")}),await(0,o.callGenericPopup)(t,o.POPUP_TYPE.DISPLAY)}async showConfirmDialog(e,t){t(!!await(0,o.callGenericPopup)(e,o.POPUP_TYPE.CONFIRM,"",{okButton:"确认",cancelButton:"取消"}))}render(){this.unrender(),this.dialog=$('\n      <div class="variable-manager-dialog">\n        <div class="dialog-header">\n          <div class="dialog-title">变量管理器</div>\n          <div class="dialog-controls">\n            <button class="dialog-toggle-btn" title="折叠/展开内容"><i class="fa-solid fa-chevron-up"></i></button>\n            <button class="dialog-close-btn"><i class="fa-solid fa-times"></i></button>\n          </div>\n        </div>\n        <div class="dialog-content"></div>\n        <div class="dialog-resize-handle"></div>\n      </div>\n    '),this.dialog.find(".dialog-content").append(this.container),this.dialog.find(".dialog-close-btn").on("click",()=>{this.unrender()}),this.dialog.find(".dialog-toggle-btn").on("click",()=>{const e=this.dialog.find(".dialog-content"),t=this.dialog.find(".dialog-toggle-btn i");e.slideToggle(300,()=>{e.is(":visible")?(t.removeClass("fa-chevron-down").addClass("fa-chevron-up"),this.dialog.find(".dialog-resize-handle").show()):(t.removeClass("fa-chevron-up").addClass("fa-chevron-down"),this.dialog.find(".dialog-resize-handle").hide())}),this.dialog.toggleClass("content-collapsed")}),$("body").append(this.dialog),this.initDraggableDialog(),this.centerDialog(),this.container.show()}unrender(){this.dialog&&(this.container.detach(),this.dialog.remove(),this.dialog=null,this.controller&&this.controller.cleanup())}initDraggableDialog(){if(!this.dialog)return;const e=(0,c.isMobile)();this.dialog.draggable({handle:".dialog-header",containment:"window",start:()=>{this.dialog?.addClass("dragging")},stop:()=>{this.dialog?.removeClass("dragging")}}),this.dialog.resizable({handles:e?"se":"n,e,s,w,ne,se,sw,nw",minHeight:u.MIN_DIALOG_HEIGHT,minWidth:u.MIN_DIALOG_WIDTH,start:()=>{this.dialog?.addClass("resizing")},stop:()=>{this.dialog?.removeClass("resizing")}}),this.dialog.find(".dialog-resize-handle").toggle(e)}centerDialog(){if(!this.dialog)return;const e=$(window).width()||0,t=$(window).height()||0,n=this.dialog.outerWidth()||u.MIN_DIALOG_WIDTH,a=this.dialog.outerHeight()||u.MIN_DIALOG_HEIGHT,r=Math.max(0,(e-n)/2),i=Math.max(0,(t-a)/2);this.dialog.css({left:`${r}px`,top:`${i}px`,position:"fixed"})}addVariableCard(e){try{const t=this.cardFactory.createCard(e);t.attr("data-type",e.dataType);const n=this.getActiveVariableType(),a=this.container.find(`#${n}-content .variable-list`);"message"===n&&void 0!==e.message_id?this.addCardToFloorPanel(a,t,e.message_id):(a.find(".empty-state").remove(),a.append(t),this.addAnimation(t,"variable-added",()=>{}))}catch(t){p().error(`[VariableManager] 添加卡片"${e.name}"失败:`,t)}}removeVariableCard(e){try{if(!e)return void p().warn("[VariableManager] 变量ID为空，无法移除卡片");const t=this.container.find(`.variable-card[data-variable-id="${e}"]`);if(0===t.length)return void p().warn(`[VariableManager] 未找到ID为"${e}"的卡片`);this.addAnimation(t,"variable-deleted",()=>{t.remove(),this.checkAndShowEmptyState()})}catch(e){p().error("[VariableManager] 移除卡片失败:",e)}}checkAndShowEmptyState(){const e=this.getActiveVariableType(),t=this.container.find(`#${e}-content .variable-list`);0===t.find(".variable-card").length&&t.html('<div class="empty-state"><p>暂无变量</p></div>')}updateVariableCard(e){try{const t=this.container.find(`.variable-card[data-variable-id="${e.id}"]`);if(0===t.length)return void p().warn(`[VariableManager] 未找到ID为"${e.id}"的卡片`);const n=this.cardFactory.createCard(e);n.attr("data-type",e.dataType),void 0!==e.message_id&&n.attr("data-floor-id",e.message_id.toString()),t.replaceWith(n),this.addAnimation(n,"variable-changed",()=>{})}catch(e){p().error("[VariableManager] 更新卡片失败:",e)}}async showKeyNameInputDialog(e){const t=$('\n      <div>\n        <h3>输入键名</h3>\n        <div class="key-input-dialog">\n          <input type="text" id="key-input" placeholder="请输入键名" />\n          <div id="key-input-error" class="input-error" style="display: none">请输入有效的键名</div>\n        </div>\n      </div>\n    '),n=t.find("#key-input"),a=t.find("#key-input-error");if(!await(0,o.callGenericPopup)(t,o.POPUP_TYPE.CONFIRM,"",{okButton:"确认",cancelButton:"取消"}))return void e(null);const r=n.val();if(!r||""===r.trim())return a.show(),void setTimeout(()=>this.showKeyNameInputDialog(e),10);e(r.trim())}addAnimation(e,t,n){const a=`animation.${t}`;e.addClass(t),e[0].offsetHeight,e.off(`animationend.${a}`),e.on(`animationend.${a}`,function(){$(this).off(`animationend.${a}`),$(this).removeClass(t),n()})}}a()}catch(e){a(e)}})},9836:(e,t,n)=>{n.d(t,{Ah:()=>l,Ji:()=>d,Km:()=>p,NU:()=>h});var a=n(5330),r=n(4365),i=n(9489),s=n(363),o=n.n(s);let c=null;function l(){(0,a.cB)("[Variables][setVariables]",async e=>{const t=e.data.variables,n=e.data.message_id;if(isNaN(n))return;const s=(0,i.getContext)().chat.length-1;if(n!==s)return void o().info(`因为 ${n} 楼不是最新楼层 ${s} 楼, 取消设置聊天变量. 原本要设置的变量:\n${JSON.stringify(t,void 0,2)} `);c=n,r.chat_metadata.variables&&"object"==typeof r.chat_metadata.variables||(r.chat_metadata.variables={}),r.chat_metadata.variables.tempVariables&&"object"==typeof r.chat_metadata.variables.tempVariables||(r.chat_metadata.variables.tempVariables={}),_.has(t,"tempVariables")&&delete t.tempVariables;const l=r.chat_metadata.variables.tempVariables,d=r.chat_metadata.variables;Object.keys(t).forEach(e=>{const n=t[e];n!==d[e]&&(l[e]=n)}),r.chat_metadata.variables.tempVariables=l,(0,i.saveMetadataDebounced)(),o().info(`${(0,a.Jn)(e)}设置聊天变量, 要设置的变量:\n${JSON.stringify(t,void 0,2)} `)})}function d(){r.chat_metadata.variables&&r.chat_metadata.variables.tempVariables&&Object.keys(r.chat_metadata.variables.tempVariables).length>0&&(o().info("[Var]Clearing tempVariables."),r.chat_metadata.variables.tempVariables={},(0,i.saveMetadataDebounced)())}function p(e){if(r.chat_metadata.variables&&r.chat_metadata.variables.tempVariables&&0!==Object.keys(r.chat_metadata.variables.tempVariables).length)if(e!==c)if(null!==c&&e>c){o().info("[Var]Event mesId is newer than setVariables mesId, updating ST variables.");!function(e){r.chat_metadata.variables||(r.chat_metadata.variables={});const t=r.chat_metadata.variables;for(const n in e)_.has(e,n)&&(t[n]=e[n]);r.chat_metadata.variables=t,(0,i.saveMetadataDebounced)()}({...r.chat_metadata.variables.tempVariables}),r.chat_metadata.variables.tempVariables={},o().info("[Var]TempVariables cleared.")}else o().info("[Var]Event mesId is older than setVariables mesId, ignoring.");else o().info("[Var]MesId matches the latest setVariables, skipping ST variable update.")}const h=[r.event_types.CHARACTER_MESSAGE_RENDERED,r.event_types.USER_MESSAGE_RENDERED]},9854:(e,t,n)=>{n.d(t,{A:()=>a});const a='<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer"/> <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.14.1/themes/base/jquery-ui.min.css" integrity="sha512-TFee0335YRJoyiqz8hA8KV3P0tXa5CpRBSoM0Wnkn7JoJx1kaq1yXL/rb8YFpWXkMOjRcv5txv+C6UluttluCQ==" crossorigin="anonymous" referrerpolicy="no-referrer"/> <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"><\/script> <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.14.1/jquery-ui.min.js" integrity="sha512-MSOo1aY+3pXCOCdGAYoBZ6YGI0aragoQsg1mKKBHXCYPIWxamwOE7Drh+N5CPgGI5SA9IEKJiPjdfqWFWmZtRA==" crossorigin="anonymous" referrerpolicy="no-referrer"><\/script> <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js" integrity="sha512-WFN04846sdKMIP5LKNphMaWzU7YpMyCU245etK3g/2ARYbPK9Ub18eG+ljU96qKRCWh+quCY7yefSmlkQw1ANQ==" crossorigin="anonymous" referrerpolicy="no-referrer"><\/script> <script src="https://fastly.jsdelivr.net/gh/N0VI028/JS-Slash-Runner/lib/yaml.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"><\/script> '},9871:(e,t,n)=>{n.d(t,{c:()=>We});var a=n(5731),r=n(6530),i=n(4365),s=n(363),o=n.n(s);class c{charData;constructor(e){this.charData=e}static find({name:e,allowAvatar:t=!0}={}){if(void 0===e){const n=i.characters[i.this_chid];n&&(e=n.avatar,t=!0)}const n=i.characters;if(t&&e){const t=n.find(t=>t.avatar===e);if(t)return t}const a=e?n.filter(n=>!e||n.name===e||t&&n.avatar===e):n;if(a.length>1&&o().warn(`找到多个符合条件的角色，返回导入时间最早的角色: ${e}`),0===a.length)throw new Error(`提供的名称或头像ID为: ${e}，未找到符合条件的角色`);return a[0]}static findCharacterIndex(e){const t=[(e,t)=>e===t,(e,t)=>e.startsWith(t),(e,t)=>e.includes(t)],n=i.characters.findIndex(t=>t.avatar===e);if(-1!==n)return n;for(const n of t){const t=i.characters.findIndex(t=>n(t.name.toLowerCase(),e.toLowerCase()));if(-1!==t)return t}return-1}static async getChatsFromFiles(e,t){const n={},a=Object.values(e).sort((e,t)=>e.file_name.localeCompare(t.file_name)).reverse().map(async({file_name:e})=>{const a=t?"":e.split(" - ")[0];let r=null,s="";!t&&a&&(r=c.find({name:a}),r&&(s=r.avatar));const o=t?"/api/chats/group/get":"/api/chats/get",l=t?JSON.stringify({id:e}):JSON.stringify({ch_name:a,file_name:e.replace(".jsonl",""),avatar_url:s}),d=await fetch(o,{method:"POST",headers:(0,i.getRequestHeaders)(),body:l,cache:"no-cache"});if(!d.ok)return;const p=await d.json();t||p.shift(),n[e]=p});return await Promise.all(a),n}getCardData(){return this.charData}getAvatarId(){return this.charData.avatar||""}getRegexScripts(){return this.charData.data?.extensions?.regex_scripts||[]}getCharacterBook(){return this.charData.data?.character_book||null}getWorldName(){return this.charData.data?.extensions?.world||""}}function l(e,t=!0){try{const n=c.find({name:e,allowAvatar:t});if(!n)return null;const a=new c(n);return o().info(`获取角色卡数据成功, 角色: ${e||"未知"}`),a.getCardData()}catch(t){return o().error(`获取角色卡数据失败, 角色: ${e||"未知"}`,t),null}}function d(e,t=!0){try{const n=c.find({name:e,allowAvatar:t});if(!n)return null;const a=new c(n).getAvatarId(),s=(0,i.getThumbnailUrl)("avatar",a),l=s.substring(s.lastIndexOf("=")+1);return o().info(`获取角色头像路径成功, 角色: ${e||"未知"}`),r.R+l}catch(t){return o().error(`获取角色头像路径失败, 角色: ${e||"未知"}`,t),null}}async function p(e,t=!0){try{const n=c.find({name:e,allowAvatar:t});if(!n)return null;const a=new c(n),r=c.findCharacterIndex(a.getAvatarId());if(-1===r)return null;const s=await(0,i.getPastCharacterChats)(r);return o().info(`获取角色聊天历史摘要成功, 角色: ${e||"未知"}`),s}catch(t){return o().error(`获取角色聊天历史摘要失败, 角色: ${e||"未知"}`,t),null}}async function h(e,t=!1){try{const n=await c.getChatsFromFiles(e,t);return o().info("获取聊天文件详情成功"),n}catch(e){return o().error("获取聊天文件详情失败",e),null}}var u=n(7631),f=n(476);function E(e,{message_id:t="last"}={}){if("number"!=typeof t&&!["last","last_user","last_char"].includes(t))throw Error(`提供的 message_id 无效, 请提供 'last', 'last_user', 'last_char' 或楼层消息号, 你提供的是: ${t}`);const n=(0,f.getLastMessageId)();if(null===n)throw Error(`未找到任何消息楼层, 你提供的是: ${t}`);switch(t){case"last":t=n;break;case"last_user":{const e=(0,f.getLastMessageId)({filter:e=>e.is_user&&!e.is_system});if(null===e)throw Error(`未找到任何 user 消息楼层, 你提供的是: ${t}`);t=e;break}case"last_char":{const e=(0,f.getLastMessageId)({filter:e=>!e.is_user&&!e.is_system});if(null===e)throw Error(`未找到任何 char 消息楼层, 你提供的是: ${t}`);t=e;break}}if(t<0||t>n)throw Error(`提供的 message_id 不在 [0, ${n}] 内, 你提供的是: ${t} `);const a=i.chat[t],r=(0,i.messageFormatting)(e,a.name,a.is_system,a.is_user,t);return o().info(`将字符串处理为酒馆用于显示的 html 格式, 字符串: '${e}', 选项: '${JSON.stringify({message_id:t})}', 结果: '${r}'`),r}function S(e){return $(`div.mes[mesid = "${e}"]`,window.parent.document).find("div.mes_text")}const C=(e=>{var t={};return n.d(t,e),t})({NOTE_MODULE_NAME:()=>m.NOTE_MODULE_NAME,metadata_keys:()=>m.metadata_keys,shouldWIAddPrompt:()=>m.shouldWIAddPrompt});var T=n(9489),A=n(8628);const x=(e=>{var t={};return n.d(t,e),t})({ChatCompletion:()=>g.ChatCompletion,Message:()=>g.Message,MessageCollection:()=>g.MessageCollection,isImageInliningSupported:()=>g.isImageInliningSupported,oai_settings:()=>g.oai_settings,prepareOpenAIMessages:()=>g.prepareOpenAIMessages,sendOpenAIRequest:()=>g.sendOpenAIRequest,setOpenAIMessageExamples:()=>g.setOpenAIMessageExamples,setOpenAIMessages:()=>g.setOpenAIMessages,setupChatCompletionPromptManager:()=>g.setupChatCompletionPromptManager});var I=n(434);const R=(e=>{var t={};return n.d(t,e),t})({METADATA_KEY:()=>v.METADATA_KEY,createNewWorldInfo:()=>v.createNewWorldInfo,deleteWorldInfo:()=>v.deleteWorldInfo,getWorldInfoPrompt:()=>v.getWorldInfoPrompt,getWorldInfoSettings:()=>v.getWorldInfoSettings,loadWorldInfo:()=>v.loadWorldInfo,saveWorldInfo:()=>v.saveWorldInfo,selected_world_info:()=>v.selected_world_info,setWorldInfoButtonClass:()=>v.setWorldInfoButtonClass,wi_anchor_position:()=>v.wi_anchor_position,world_info:()=>v.world_info,world_info_include_names:()=>v.world_info_include_names,world_names:()=>v.world_names}),k=["world_info_before","persona_description","char_description","char_personality","scenario","world_info_after","dialogue_examples","chat_history","user_input"],O={system:0,user:1,assistant:2},L=["world_info_before","persona_description","char_description","char_personality","scenario","world_info_after","dialogue_examples","chat_history","user_input"],M={NONE:-1,DEFAULT:0,COMPLETION:1,CONTENT:2};var P=n(3892);async function N(e){let t;if(!("string"==typeof e&&(0,P.isDataURL)(e)))try{if("string"==typeof e){const n=await fetch(e,{method:"GET",cache:"force-cache"});if(!n.ok)throw new Error("Failed to fetch image");const a=await n.blob();t=await(0,P.getBase64Async)(a)}else t=await(0,P.getBase64Async)(e)}catch(e){o().error("[Generate:图片数组处理] 图片处理失败:",e)}return t}function D(e){if(0===e.length||"<START>"===e)return[];e.startsWith("<START>")||(e="<START>\n"+e.trim());return e.split(/<START>/gi).slice(1).map(e=>`<START>\n${e.trim()}\n`)}function F(e){switch(e){case i.extension_prompt_roles.SYSTEM:return"system";case i.extension_prompt_roles.USER:return"user";case i.extension_prompt_roles.ASSISTANT:return"assistant";default:return"system"}}function B(e,t){if(!t.overrides)return!1;if("with_depth_entries"===e)return!1===t.overrides.with_depth_entries;if("chat_history"===e){const e=t.overrides.chat_history;return void 0!==e&&0===e.length}const n=t.overrides[e];return void 0!==n&&""===n}function G(){(0,i.activateSendButtons)(),(0,i.showSwipeButtons)(),(0,i.setGenerationProgress)(0),(0,I.flushEphemeralStoppingStrings)()}async function V(e){const t=(0,T.getContext)().extensionPrompts;Object.keys(t).filter(t=>e.some(e=>t.startsWith(e))).forEach(e=>delete t[e]),await(0,i.saveChatConditional)()}async function j(e,t){const n=t=>{if(!e.overrides)return;const n=e.overrides[t];return"boolean"!=typeof n?n:void 0};B("chat_history",e)||function(){const e=(0,i.baseChatReplace)(i.characters[i.this_chid]?.data?.extensions?.depth_prompt?.prompt?.trim(),i.name1,i.name2)||"",t=i.characters[i.this_chid]?.data?.extensions?.depth_prompt?.depth??"4",n=(0,i.getExtensionPromptRoleByName)(i.characters[i.this_chid]?.data?.extensions?.depth_prompt?.role??"system");(0,i.setExtensionPrompt)("DEPTH_PROMPT",e,i.extension_prompt_types.IN_CHAT,t,T.extension_settings.note.allowWIScan,n)}(),B("chat_history",e)||B("author_note",e)||function(e){const t=e?.overrides?.author_note,n=t??$("#extension_floating_prompt").val();(0,i.setExtensionPrompt)(C.NOTE_MODULE_NAME,n,i.chat_metadata[C.metadata_keys.position],i.chat_metadata[C.metadata_keys.depth],T.extension_settings.note.allowWIScan,i.chat_metadata[C.metadata_keys.role])}(e),B("chat_history",e)||B("persona_description",e)||function(){const e=I.power_user.persona_description,t="PERSONA_DESCRIPTION";if((0,i.setExtensionPrompt)(t,"",i.extension_prompt_types.IN_PROMPT,0),!e||I.power_user.persona_description_position===I.persona_description_positions.NONE)return;if([I.persona_description_positions.BOTTOM_AN,I.persona_description_positions.TOP_AN].includes(I.power_user.persona_description_position)&&C.shouldWIAddPrompt){const t=(0,T.getContext)().extensionPrompts[C.NOTE_MODULE_NAME].value,n=I.power_user.persona_description_position===I.persona_description_positions.TOP_AN?`${e}\n${t}`:`${t}\n${e}`;(0,i.setExtensionPrompt)(C.NOTE_MODULE_NAME,n,i.chat_metadata[C.metadata_keys.position],i.chat_metadata[C.metadata_keys.depth],T.extension_settings.note.allowWIScan,i.chat_metadata[C.metadata_keys.role])}I.power_user.persona_description_position===I.persona_description_positions.AT_DEPTH&&(0,i.setExtensionPrompt)(t,e,i.extension_prompt_types.IN_CHAT,I.power_user.persona_description_depth,!0,I.power_user.persona_description_role)}();const a=(0,i.baseChatReplace)(i.characters[i.this_chid]?.data?.extensions?.depth_prompt?.prompt?.trim(),i.name1,i.name2),r=(0,i.baseChatReplace)(i.characters[i.this_chid]?.data?.creator_notes?.trim(),i.name1,i.name2),{description:s,personality:o,persona:c,scenario:l,mesExamples:d,system:p,jailbreak:h}=(0,i.getCharacterCardFields)(),u=B("char_description",e)?"":n("char_description")??s,f=B("char_personality",e)?"":n("char_personality")??o,m=B("persona_description",e)?"":n("persona_description")??c,g=B("scenario",e)?"":n("scenario")??l;let _=D(B("dialogue_examples",e)?"":n("dialogue_examples")??d),v=[];v=(0,x.setOpenAIMessageExamples)(_);const{promptBias:y}=(0,i.getBiasStrings)(t,"quiet");e.inject&&await async function(e){if(!e||!Array.isArray(e.inject))return;const t=e.inject,n={before_prompt:i.extension_prompt_types.BEFORE_PROMPT,in_chat:i.extension_prompt_types.IN_CHAT,after_prompt:i.extension_prompt_types.IN_PROMPT,none:i.extension_prompt_types.NONE};for(const e of t){const t={role:O[e.role]??i.extension_prompt_roles.SYSTEM,content:e.content||"",depth:Number(e.depth)||0,should_scan:Boolean(e.should_scan)||!0,position:n[e.position]??i.extension_prompt_types.IN_CHAT};(0,i.setExtensionPrompt)(`INJECTION-${e.depth}-${e.role}`,t.content,t.position,t.depth,t.should_scan,t.role)}}(e);let b=[];var w;e.overrides?.chat_history?b=[...e.overrides.chat_history].reverse():(b=(0,x.setOpenAIMessages)(await async function(e){const t=e.filter(e=>!e.is_system);return await Promise.all(t.map(async(e,n)=>{const a=e.mes,r=e.is_user?A.regex_placement.USER_INPUT:A.regex_placement.AI_OUTPUT,i=(0,A.getRegexedString)(a,r,{isPrompt:!0,depth:t.length-n-1});return{...e,mes:i,index:n}}))}(i.chat)),void 0!==e.max_chat_history&&(b=b.slice(0,e.max_chat_history))),w=t,(0,i.setExtensionPrompt)("TEMP_USER_MESSAGE",w,i.extension_prompt_types.IN_PROMPT,0,!0,1);const E=await async function(e,t,n){const a=e.filter(e=>"system"!==e.role).map(e=>{const t="user"===e.role?i.name1:i.name2;return R.world_info_include_names?`${t}: ${e.content}`:e.content}).reverse(),r=(0,i.getMaxContextSize)(),s={personaDescription:t.overrides?.persona_description??n.persona,characterDescription:t.overrides?.char_description??n.description,characterPersonality:t.overrides?.char_personality??n.personality,characterDepthPrompt:n.charDepthPrompt,scenario:t.overrides?.scenario??n.scenario,creatorNotes:n.creatorNotes},{worldInfoString:o,worldInfoBefore:c,worldInfoAfter:l,worldInfoExamples:d,worldInfoDepth:p}=await(0,R.getWorldInfoPrompt)(a,r,!1,s);await V(["customDepthWI"]),B("with_depth_entries",t)||function(e){Array.isArray(e)&&e.forEach(e=>{const t=e.entries.join("\n");(0,i.setExtensionPrompt)(`customDepthWI-${e.depth}-${e.role}`,t,i.extension_prompt_types.IN_CHAT,e.depth,!1,e.role)})}(p);const h=B("world_info_before",t)?null:void 0!==t.overrides?.world_info_before?t.overrides.world_info_before:c,u=B("world_info_after",t)?null:void 0!==t.overrides?.world_info_after?t.overrides.world_info_after:l;return{worldInfoString:o,worldInfoBefore:h,worldInfoAfter:u,worldInfoExamples:d,worldInfoDepth:B("with_depth_entries",t)?null:p}}(b,e,{description:s,personality:o,persona:c,scenario:l,charDepthPrompt:a,creatorNotes:r});return(0,i.setExtensionPrompt)("TEMP_USER_MESSAGE","",i.extension_prompt_types.IN_PROMPT,0,!0,1),_=B("dialogue_examples",e)?[]:await async function(e,t){for(const n of t){if(!n.content.length)continue;const t=D((0,i.baseChatReplace)(n.content,i.name1,i.name2));n.position===R.wi_anchor_position.before?e.unshift(...t):e.push(...t)}return e}(_,E.worldInfoExamples),{characterInfo:{description:u,personality:f,persona:m,scenario:g,system:p,jailbreak:h},chatContext:{oaiMessages:b,oaiMessageExamples:v,promptBias:y},worldInfo:E}}const U=!1;const H=(e=>{var t={};return n.d(t,e),t})({Prompt:()=>y.Prompt,PromptCollection:()=>y.PromptCollection});async function q(e,t,n,a,r){const s=t.order||L,c=s.findIndex(e=>"string"==typeof e&&"chat_history"===e.toLowerCase()),l=s.findIndex(e=>"string"==typeof e&&"user_input"===e.toLowerCase()),d=-1!==l,p=-1!==c,h=B("chat_history",t);let u;if(r&&d)u=await x.Message.createAsync("user",r,"user_input");else if(u=await x.Message.createAsync("user",a,"user_input"),t.image&&d&&!Array.isArray(t.image)){const e=await N(t.image);e&&await u.addImage(e)}if(h||!p){const e=d?l:s.length;return void n.add(new x.MessageCollection("user_input",u),e)}const f=new x.MessageCollection("chat_history"),m=x.oai_settings.new_chat_prompt,g=await x.Message.createAsync("system",(0,i.substituteParams)(m),"newMainChat");n.reserveBudget(g),f.add(g);const _=e.chatContext.oaiMessages[e.chatContext.oaiMessages.length-1],v=await x.Message.createAsync("user",x.oai_settings.send_if_empty,"emptyUserMessageReplacement");if(_&&"assistant"===_.role&&x.oai_settings.send_if_empty&&n.canAfford(v)&&f.add(v),!d){let n;n=r?{role:"user",content:r,identifier:"user_input"}:{role:"user",content:a,identifier:"user_input",image:t.image&&!Array.isArray(t.image)?await N(t.image):void 0},e.chatContext.oaiMessages.unshift(n)}const y=(await async function(e,t,n=[],a){const r=[...t];let s=0;const c=[],l=(0,T.getContext)().extensionPrompts[C.NOTE_MODULE_NAME];l&&l.value&&c.push({role:F(l.role),content:l.value,identifier:"authorsNote",injection_depth:l.depth,injected:!0});I.power_user.persona_description&&I.power_user.persona_description_position===I.persona_description_positions.AT_DEPTH&&c.push({role:"system",content:I.power_user.persona_description,identifier:"persona_description",injection_depth:I.power_user.persona_description_depth,injected:!0});if(!B("char_depth_prompt",a)){const t=e.worldInfo.worldInfoDepth;if(t)for(const e of t){const t=await(0,i.getExtensionPromptByName)(`customDepthWI-${e.depth}-${e.role}`);c.push({role:F(e.role),content:t,injection_depth:e.depth,injected:!0}),o().info("injectionPrompts",c)}}if(Array.isArray(n))for(const e of n)c.push({identifier:`INJECTION-${e.role}-${e.depth}`,role:e.role,content:e.content,injection_depth:e.depth||0,injected:!0});for(let e=0;e<=i.MAX_INJECTION_DEPTH;e++){const t=c.filter(t=>t.injection_depth===e&&t.content),n=["system","user","assistant"],a=[],i="\n";for(const e of n){const n=t.filter(t=>t.role===e).map(e=>e.content.trim()).join(i);n&&a.push({role:e,content:n,injected:!0})}if(a.length){const t=e+s;r.splice(t,0,...a),s+=a.length}}return r}(e,e.chatContext.oaiMessages,t.inject,t)).reverse(),b=(0,x.isImageInliningSupported)(),w=[...y];for(const e of w){const t=new H.Prompt(e);t.identifier="chat_history-"+(y.length-w.indexOf(e)),t.content=(0,i.substituteParams)(t.content);const a=await x.Message.fromPromptAsync(t),r=(0,x.setupChatCompletionPromptManager)(x.oai_settings);if(r&&r.serviceSettings.names_behavior===M.COMPLETION&&t.name){const e=r.isValidName(t.name)?t.name:r.sanitizeName(t.name);await a.setName(e)}if(b&&e.image&&await a.addImage(e.image),!n.canAfford(a))break;f.add(a)}n.freeBudget(g),d?(n.add(f,c),n.add(new x.MessageCollection("user_input",u),l)):n.add(f,c)}async function Y(e,t,n){const a=new x.ChatCompletion;a.setTokenBudget(x.oai_settings.openai_max_context,x.oai_settings.openai_max_tokens),a.reserveBudget(3);const r=t.order||L,i=r.reduce((e,t,n)=>("string"==typeof t?e[t.toLowerCase()]=n:"object"==typeof t&&(e[`custom_prompt_${n}`]=n),e),{}),{systemPrompts:s,dialogue_examples:o}=await async function(e,t){const n=new H.PromptCollection,a=new x.MessageCollection("dialogue_examples"),r=t.order||k,i={world_info_before:e.worldInfo.worldInfoBefore,persona_description:I.power_user.persona_description&&I.power_user.persona_description_position===I.persona_description_positions.IN_PROMPT?e.characterInfo.persona:null,char_description:e.characterInfo.description,char_personality:e.characterInfo.personality,scenario:e.characterInfo.scenario,world_info_after:e.worldInfo.worldInfoAfter};for(const[e,t]of r.entries())if("string"==typeof t){const e=i[t];e&&n.add(new H.Prompt({identifier:t,role:"system",content:e,system_prompt:!0}))}else if("object"==typeof t&&t.role&&t.content){const a=`custom_prompt_${e}`;n.add(new H.Prompt({identifier:a,role:t.role,content:t.content,system_prompt:"system"===t.role}))}if(e.chatContext.oaiMessageExamples.length>0)for(const t of[...e.chatContext.oaiMessageExamples]){const n=e.chatContext.oaiMessageExamples.indexOf(t),r=[];for(let e=0;e<t.length;e++){const a=t[e],i="system",s=a.content||"",o=`dialogue_examples ${n}-${e}`,c=await x.Message.createAsync(i,s,o);await c.setName(a.name),r.push(c)}for(const e of r)a.add(e)}return{systemPrompts:n,dialogue_examples:a}}(e,t),c=async(e,t)=>{if("object"==typeof e){const n=new x.MessageCollection(`custom_prompt_${t}`),r=await x.Message.createAsync(e.role,e.content,`custom_prompt_${t}`);n.add(r),a.add(n,t)}else if(s.has(e)){const t=s.get(e),n=new x.MessageCollection(e),r=await x.Message.fromPromptAsync(t);n.add(r),a.add(n,i[e])}};for(const[e,n]of r.entries())"string"==typeof n?B(n,t)||await c(n,e):"object"==typeof n&&n.role&&n.content&&await c(n,e);const l=r.findIndex(e=>"string"==typeof e&&"dialogue_examples"===e.toLowerCase());-1===l||B("dialogue_examples",t)||a.add(o,l);const d=await x.Message.createAsync("user",n,"user_input");a.reserveBudget(d),await q(e,t,a,n,t.processedImageArray),a.freeBudget(d),x.oai_settings.squash_system_messages&&await a.squashSystemMessages();return{prompt:a.getChat()}}function W(e=""){const t=function(e){return""===e&&(e=x.oai_settings.send_if_empty.trim()),(0,A.getRegexedString)(e,A.regex_placement.USER_INPUT,{isPrompt:!0,depth:0})}((0,i.substituteParams)(e))||"";return t}async function z(e="",t=!0,n=void 0){let a,r,s=W(e);return Array.isArray(n)&&n.length>0&&(t?(a=function(e,t){const n="__IMG_ARRAY_MARKER_",a=e+n;let r,s;const c=new Promise((e,t)=>{r=e,s=t});let l=null,d=!0;const p=async e=>{o().debug("[Generate:图片数组处理] imageArrayHandler 被调用");try{l=setTimeout(()=>{o().warn("[Generate:图片数组处理] 图片处理超时"),s(new Error("图片处理超时"))},3e4);for(let a=e.chat.length-1;a>=0;a--){const i=e.chat[a],c="string"==typeof i.content?i.content:"";if("user"===i.role&&c.includes(n))try{const e=x.oai_settings.inline_image_quality||"low",a=(await Promise.all(t.map(async t=>{try{const n=await N(t);return n?{type:"image_url",image_url:{url:n,detail:e}}:(o().warn("[Generate:图片数组处理] 图片处理失败，跳过该图片"),null)}catch(e){return o().error("[Generate:图片数组处理] 单个图片处理失败:",e),null}}))).filter(e=>null!==e),s={type:"text",text:c.replace(n,"")};return i.content=[s,...a],l&&(clearTimeout(l),l=null),o().info("[Generate:图片数组处理] 成功将",a.length,"张图片插入到用户消息中"),void r()}catch(e){return l&&(clearTimeout(l),l=null),o().error("[Generate:图片数组处理] 处理图片时出错:",e),void s(e)}}o().warn("[Generate:图片数组处理] 未找到包含图片标记的用户消息"),r()}catch(e){o().error("[Generate:图片数组处理] imageArrayHandler 异常:",e),s(e)}};return i.eventSource.once("chat_completion_prompt_ready",p),{userInputWithMarker:a,imageProcessingPromise:c,resolveImageProcessing:r,rejectImageProcessing:s,cleanup:()=>{if(l&&(clearTimeout(l),l=null),d)try{i.eventSource.removeListener("chat_completion_prompt_ready",p),d=!1,o().debug("[Generate:图片数组处理] 已清理事件监听器")}catch(e){o().warn("[Generate:图片数组处理] 清理事件监听器时出错:",e)}}}}(s,n),s=a.userInputWithMarker):r=await async function(e,t){const n=x.oai_settings.inline_image_quality||"low",a=(await Promise.all(t.map(async e=>{try{const t=await N(e);return t?{type:"image_url",image_url:{url:t,detail:n}}:(o().warn("[Generate:图片数组处理] 图片处理失败，跳过该图片"),null)}catch(e){return o().error("[Generate:图片数组处理] 单个图片处理失败:",e),null}}))).filter(e=>null!==e),r={type:"text",text:e};return o().info("[Generate:图片数组处理] 成功处理",a.length,"张图片"),[r,...a]}(s,n)),{processedUserInput:s,imageProcessingSetup:a,processedImageArray:r}}var J=n(5983);const K="quiet";class X{generator;stoppingStrings;result;isStopped;isFinished;abortController;messageBuffer;constructor(){this.result="",this.messageBuffer="",this.isStopped=!1,this.isFinished=!1,this.generator=this.nullStreamingGeneration,this.abortController=new AbortController}onProgressStreaming(e,t){const n=e.slice(this.messageBuffer.length);this.messageBuffer=e;let a=(0,i.cleanUpMessage)(n,!1,!1,!t,this.stoppingStrings);const r=["*",'"',"```"];for(const e of r)if(!t&&(0,i.isOdd)((0,i.countOccurrences)(a,e))){const t=e.length>1?"\n":"";a=a.trimEnd()+t+e}if(i.eventSource.emit("js_stream_token_received_fully",e),i.eventSource.emit("js_stream_token_received_incrementally",a),t){const t=(0,i.cleanUpMessage)(e,!1,!1,!1,this.stoppingStrings);i.eventSource.emit("js_generation_ended",t)}}onErrorStreaming(){this.abortController&&this.abortController.abort(),this.isStopped=!0,G(),(0,i.saveChatConditional)()}async*nullStreamingGeneration(){throw Error("Generation function for streaming is not hooked up")}async generate(){try{const e=new P.Stopwatch(1e3/I.power_user.streaming_fps),t=[];for await(const{text:n}of this.generator()){if(t.push(Date.now()),this.isStopped)return void(this.messageBuffer="");this.result=n,await e.tick(()=>this.onProgressStreaming(n,!1))}this.isStopped?this.messageBuffer="":this.onProgressStreaming(this.result,!0);const n=(t[t.length-1]-t[0])/1e3;o().warn(`Stream stats: ${t.length} tokens, ${n.toFixed(2)} seconds, rate: ${Number(t.length/n).toFixed(2)} TPS`)}catch(e){if(!this.isFinished)throw this.onErrorStreaming(),Error(`Generate method error: ${e}`);return this.messageBuffer="",this.result}return this.isFinished=!0,this.result}}async function Z(e,t=!1,n=void 0,a){let r="";try{if((0,i.deactivateSendButtons)(),n)try{await n.imageProcessingPromise,o().debug("[Generate:图片数组处理] 图片处理已完成，继续生成流程")}catch(e){throw o().error("[Generate:图片数组处理] 图片处理失败:",e),new Error(`图片处理失败: ${e?.message||"未知错误"}`)}if(t){const t=x.oai_settings.stream_openai;t||(x.oai_settings.stream_openai=!0,(0,i.saveSettingsDebounced)());const n=new X;n.generator=await(0,x.sendOpenAIRequest)("normal",e.prompt,a.signal),r=await n.generate(),t!==x.oai_settings.stream_openai&&(x.oai_settings.stream_openai=t,(0,i.saveSettingsDebounced)())}else{i.eventSource.emit("js_generation_started");const t=await(0,x.sendOpenAIRequest)(K,e.prompt,a.signal);r=await async function(e){if(!e)throw Error("未得到响应");if(e.error)throw e?.response&&toastr.error(e.response,J.t`API Error`,{preventDuplicates:!0}),Error(e?.response);const t=function(e){return"string"==typeof e?e:e?.choices?.[0]?.message?.content??e?.choices?.[0]?.text??e?.text??e?.message?.content?.[0]?.text??e?.message?.tool_plan??""}(e);return i.eventSource.emit("js_generation_ended",t),t}(t)}}catch(e){throw n&&n.rejectImageProcessing(e),o().error(e),e}finally{G(),await V(["INJECTION"])}return r}let Q,ee=new AbortController;function te(e){return{world_info_before:e.world_info_before,persona_description:e.persona_description,char_description:e.char_description,char_personality:e.char_personality,scenario:e.scenario,world_info_after:e.world_info_after,dialogue_examples:e.dialogue_examples,with_depth_entries:e.chat_history?.with_depth_entries,author_note:e.chat_history?.author_note,chat_history:e.chat_history?.prompts}}function ne(e){return{role:e.role,content:e.content,position:{before_prompt:"before_prompt",in_chat:"in_chat",after_prompt:"after_prompt",none:"none"}[e.position],depth:e.depth,should_scan:e.should_scan}}async function ae({user_input:e="",use_preset:t=!0,image:n,overrides:a,max_chat_history:r,inject:s=[],order:c,stream:l=!1}={}){ee=new AbortController;const d=await z(e,t,n),{processedUserInput:p,imageProcessingSetup:h,processedImageArray:u}=d;Q=h;const f=await j({overrides:a,max_chat_history:r,inject:s,order:c},p),m=t?await async function(e,t,n){let a=null;try{const r=n?.overrides?.scenario;r&&i.characters&&i.characters[i.this_chid]&&(a=i.characters[i.this_chid].scenario||null,i.characters[i.this_chid].scenario=r);const s={role:"user",content:t,image:n.image};n.image&&(Array.isArray(n.image)?delete s.image:s.image=await N(n.image)),e.chatContext.oaiMessages.unshift(s);const o={name2:i.name2,charDescription:e.characterInfo.description,charPersonality:e.characterInfo.personality,Scenario:e.characterInfo.scenario,worldInfoBefore:e.worldInfo.worldInfoBefore,worldInfoAfter:e.worldInfo.worldInfoAfter,extensionPrompts:(0,T.getContext)().extensionPrompts,bias:e.chatContext.promptBias,type:"normal",quietPrompt:"",quietImage:null,cyclePrompt:"",systemPromptOverride:e.characterInfo.system,jailbreakPromptOverride:e.characterInfo.jailbreak,personaDescription:e.characterInfo.persona,messages:e.chatContext.oaiMessages,messageExamples:e.chatContext.oaiMessageExamples},[c]=await(0,x.prepareOpenAIMessages)(o,U);return{prompt:c}}finally{null!==a&&i.characters&&i.characters[i.this_chid]&&(i.characters[i.this_chid].scenario=a)}}(f,p,{image:n,overrides:a,max_chat_history:r,inject:s,order:c}):await Y(f,{image:n,overrides:a,max_chat_history:r,inject:s,order:c,processedImageArray:u},p);try{o().info("[Generate:发送提示词]",m);const e=await Z(m,l,h,ee);return Q=void 0,e}catch(e){throw h&&h.rejectImageProcessing(e),Q=void 0,e}}async function re(e){const t=function(e){return{user_input:e.user_input,use_preset:!0,image:e.image,stream:e.should_stream??!1,overrides:void 0!==e.overrides?te(e.overrides):void 0,inject:void 0!==e.injects?e.injects.map(ne):void 0,max_chat_history:"number"==typeof e.max_chat_history?e.max_chat_history:void 0}}(e);return await ae(t)}async function ie(e){const t=function(e){return{user_input:e.user_input,use_preset:!1,image:e.image,stream:e.should_stream??!1,max_chat_history:"number"==typeof e.max_chat_history?e.max_chat_history:void 0,overrides:e.overrides?te(e.overrides):void 0,inject:e.injects?e.injects.map(ne):void 0,order:e.ordered_prompts}}(e);return await ae(t)}$(document).on("click","#mes_stop",function(){(0,i.stopGeneration)()&&(ee&&ee.abort("Clicked stop button"),function(){if(Q){try{Q.cleanup(),Q.rejectImageProcessing(new Error("Generation stopped by user")),o().info("[Generate:停止] 已清理图片处理相关逻辑")}catch(e){o().warn("[Generate:停止] 清理图片处理时出错:",e)}Q=void 0}}(),G())});const se=(e=>{var t={};return n.d(t,e),t})({selected_group:()=>b.selected_group});function oe(){const e={selected_global_lorebooks:(t=(0,R.getWorldInfoSettings)()).world_info.globalSelect,scan_depth:t.world_info_depth,context_percentage:t.world_info_budget,budget_cap:t.world_info_budget_cap,min_activations:t.world_info_min_activations,max_depth:t.world_info_min_activations_depth_max,max_recursion_steps:t.world_info_max_recursion_steps,insertion_strategy:{0:"evenly",1:"character_first",2:"global_first"}[t.world_info_character_strategy],include_names:t.world_info_include_names,recursive:t.world_info_recursive,case_sensitive:t.world_info_case_sensitive,match_whole_words:t.world_info_match_whole_words,use_group_scoring:t.world_info_use_group_scoring,overflow_alert:t.world_info_overflow_alert};var t;return o().info(`获取世界书全局设置:\n${JSON.stringify(e)}`),structuredClone(e)}function ce(e){if(e.selected_global_lorebooks){const t=e.selected_global_lorebooks.filter(e=>!R.world_names.includes(e));if(t.length>0)throw Error(`尝试修改要全局启用的世界书, 但未找到以下世界书: ${JSON.stringify(t)}`)}!function(e){const t={selected_global_lorebooks:e=>{$("#world_info").find('option[value!=""]').remove(),R.world_names.forEach((t,n)=>$("#world_info").append(`<option value='${n}'${e.includes(t)?" selected":""}>${t}</option>`)),R.selected_world_info.length=0,R.selected_world_info.push(...e),(0,i.saveSettings)()},scan_depth:e=>{$("#world_info_depth").val(e).trigger("input")},context_percentage:e=>{$("#world_info_budget").val(e).trigger("input")},budget_cap:e=>{$("#world_info_budget_cap").val(e).trigger("input")},min_activations:e=>{$("#world_info_min_activations").val(e).trigger("input")},max_depth:e=>{$("#world_info_min_activations_depth_max").val(e).trigger("input")},max_recursion_steps:e=>{$("#world_info_max_recursion_steps").val(e).trigger("input")},insertion_strategy:e=>{const t={evenly:0,character_first:1,global_first:2}[e];$(`#world_info_character_strategy option[value='${t}']`).prop("selected",!0),$("#world_info_character_strategy").val(t).trigger("change")},include_names:e=>{$("#world_info_include_names").prop("checked",e).trigger("input")},recursive:e=>{$("#world_info_recursive").prop("checked",e).trigger("input")},case_sensitive:e=>{$("#world_info_case_sensitive").prop("checked",e).trigger("input")},match_whole_words:e=>{$("#world_info_match_whole_words").prop("checked",e).trigger("input")},use_group_scoring:e=>{$("#world_info_use_group_scoring").prop("checked",e).trigger("change")},overflow_alert:e=>{$("#world_info_overflow_alert").prop("checked",e).trigger("change")}};Object.entries(e).filter(([e,t])=>void 0!==t).forEach(([e,n])=>{t[e]?.(n)})}(e),o().info(`修改世界书全局设置:\n${JSON.stringify(e)}`)}function le(){return o().info(`获取世界书列表: ${JSON.stringify(R.world_names)}`),structuredClone(R.world_names)}async function de(e){const t=await(0,R.deleteWorldInfo)(e);return o().info(`移除世界书 '${e}' ${t?"成功":"失败"}`),t}async function pe(e){const t=await(0,R.createNewWorldInfo)(e,{interactive:!1});return o().info(`新建世界书 '${e}' ${t?"成功":"失败"}`),t}function he({name:e=i.characters[i.this_chid]?.avatar??null,type:t="all"}={}){if(se.selected_group&&!e)throw Error("不要在群组中调用这个功能");const n=(0,P.findChar)({name:e});if(!n)throw Error(`未找到名为 '${e}' 的角色卡`);const a={primary:null,additional:[]};n.data?.extensions?.world&&(a.primary=n.data?.extensions?.world);const r=(0,P.getCharaFilename)(i.characters.indexOf(n)),s=R.world_info.charLore?.find(e=>e.name===r);if(s&&Array.isArray(s.extraBooks)&&(a.additional=s.extraBooks),t)switch(t){case"primary":return{primary:a.primary,additional:[]};case"additional":return{primary:null,additional:a.additional};default:return a}return o().info(`获取角色卡绑定的世界书, 选项: ${JSON.stringify({name:e,type:t})}, 获取结果: ${JSON.stringify(a)}`),structuredClone(a)}function ue(){return he().primary}async function fe(e){if(se.selected_group)throw Error("不要在群组中调用这个功能");const t=(0,P.getCharaFilename)(i.this_chid);if(!t)throw Error("未打开任何角色卡");const n=[...e.primary&&!R.world_names.includes(e.primary)?[e.primary]:[],...e.additional?e.additional.filter(e=>!R.world_names.includes(e)):[]];if(n.length>0)throw Error(`尝试修改 '${t}' 绑定的世界书, 但未找到以下世界书: ${n}`);if(void 0!==e.primary){const n=String($("#character_world").val());if($("#character_world").val(e.primary?e.primary:""),$(".character_world_info_selector").find("option:selected").val(e.primary?R.world_names.indexOf(e.primary):""),n&&!e.primary){const e=JSON.parse(String($("#character_json_data").val()));e?.data?.character_book&&(e.data.character_book=void 0),$("#character_json_data").val(JSON.stringify(e))}if(!await async function(){$("#rm_info_avatar").html("");const e=new FormData($("#form_create").get(0)),t=e.get("avatar");if(t instanceof File){const n=await(0,P.ensureImageFormatSupported)(t);e.set("avatar",n)}const n=(0,i.getRequestHeaders)();delete n["Content-Type"],e.delete("alternate_greetings");const a=$(".open_alternate_greetings").data("chid");if(a&&Array.isArray(i.characters[a]?.data?.alternate_greetings))for(const t of i.characters[a].data.alternate_greetings)e.append("alternate_greetings",t);return!!(await fetch("/api/characters/edit",{method:"POST",headers:n,body:e,cache:"no-cache"})).ok&&(await(0,i.getOneCharacter)(e.get("avatar_url")),$("#add_avatar_button").replaceWith($("#add_avatar_button").val("").clone(!0)),$("#create_button").attr("value","Save"),!0)}())throw Error(`尝试为 '${t}' 绑定主要世界书, 但在访问酒馆后端时出错`);(0,R.setWorldInfoButtonClass)(void 0,!!e.primary)}if(void 0!==e.additional){const n=R.world_info.charLore??[],a=n.findIndex(e=>e.name===t);-1===a?n.push({name:t,extraBooks:e.additional}):0===e.additional.length?n.splice(a,1):n[a].extraBooks=e.additional,Object.assign(R.world_info,{charLore:n})}(0,i.saveCharacterDebounced)(),(0,i.saveSettingsDebounced)(),o().info(`修改角色卡绑定的世界书, 要修改的部分: ${JSON.stringify(e)}${void 0===e.primary?", 主要世界书保持不变":""}${void 0===e.additional?", 附加世界书保持不变":""}`)}async function me(){if(!(0,i.getCurrentChatId)())throw Error("未打开任何聊天, 不可获取聊天世界书");const e=_.get(i.chat_metadata,R.METADATA_KEY,"");return R.world_names.includes(e)?e:(_.unset(i.chat_metadata,R.METADATA_KEY),null)}async function ge(e){if(null===e)_.unset(i.chat_metadata,R.METADATA_KEY),$(".chat_lorebook_button").removeClass("world_set");else{if(!R.world_names.includes(e))throw new Error(`尝试为角色卡绑定聊天世界书, 但该世界书 '${e}' 不存在`);_.set(i.chat_metadata,R.METADATA_KEY,e),$(".chat_lorebook_button").addClass("world_set")}await(0,i.saveMetadata)()}async function _e(e){const t=await me();if(null!==t)return t;const n=(()=>{if(e){if(R.world_names.includes(e))throw new Error(`尝试创建聊天世界书, 但该名称 '${e}' 已存在`);return e}return`Chat Book ${(0,i.getCurrentChatId)()}`.replace(/[^a-z0-9]/gi,"_").replace(/_{2,}/g,"_").substring(0,64)})();return await(0,R.createNewWorldInfo)(n),await ge(n),n}function ve(e,t=!1){const n=Number($("#world_editor_select").val()),a=R.world_names.indexOf(e);-1===a||!t&&n!==a||$("#world_editor_select").val(a).trigger("change")}const ye=_.debounce(ve,1e3),be={key:[],keysecondary:[],comment:"",content:"",constant:!1,vectorized:!1,selective:!0,selectiveLogic:0,addMemo:!0,order:100,position:0,disable:!1,excludeRecursion:!1,preventRecursion:!1,matchPersonaDescription:!1,matchCharacterDescription:!1,matchCharacterPersonality:!1,matchCharacterDepthPrompt:!1,matchScenario:!1,matchCreatorNotes:!1,delayUntilRecursion:0,probability:100,useProbability:!0,depth:4,group:"",groupOverride:!1,groupWeight:100,scanDepth:null,caseSensitive:null,matchWholeWords:null,useGroupScoring:null,automationId:"",role:0,sticky:null,cooldown:null,delay:null};function we(e){return{uid:e.uid,display_index:e.displayIndex,comment:e.comment,enabled:!e.disable,type:e.constant?"constant":e.vectorized?"vectorized":"selective",position:{0:"before_character_definition",1:"after_character_definition",5:"before_example_messages",6:"after_example_messages",2:"before_author_note",3:"after_author_note"}[e.position]??(0===e.role?"at_depth_as_system":1===e.role?"at_depth_as_user":"at_depth_as_assistant"),depth:4===e.position?e.depth:null,order:e.order,probability:e.probability,key:e.key,keys:e.key,logic:{0:"and_any",1:"not_all",2:"not_any",3:"and_all"}[e.selectiveLogic],filter:e.keysecondary,filters:e.keysecondary,scan_depth:e.scanDepth??"same_as_global",case_sensitive:e.caseSensitive??"same_as_global",match_whole_words:e.matchWholeWords??"same_as_global",use_group_scoring:e.useGroupScoring??"same_as_global",automation_id:e.automationId||null,exclude_recursion:e.excludeRecursion,prevent_recursion:e.preventRecursion,delay_until_recursion:e.delayUntilRecursion,content:e.content,group:e.group,group_prioritized:e.groupOverride,group_weight:e.groupWeight,sticky:e.sticky||null,cooldown:e.cooldown||null,delay:e.delay||null}}async function Ee(e,{filter:t="none"}={}){if(!R.world_names.includes(e))throw Error(`未能找到世界书 '${e}'`);let n=Object.values((await(0,R.loadWorldInfo)(e)).entries).map(we);return"none"!==t&&(n=n.filter(e=>Object.entries(t).every(([t,n])=>{const a=e[t];return Array.isArray(a)?n.every(e=>a.includes(e)):"string"==typeof a?a.includes(n):a===n}))),o().info(`获取世界书 '${e}' 中的条目, 选项: ${JSON.stringify({filter:t})}`),structuredClone(n)}function Se(e){_.has(e,"key")&&!_.has(e,"keys")&&_.set(e,"keys",e.key),_.has(e,"filter")&&!_.has(e,"filters")&&_.set(e,"filters",e.filter);const t={uid:e=>({uid:e}),display_index:e=>({displayIndex:e}),comment:e=>({comment:e}),enabled:e=>({disable:!e}),type:e=>({constant:"constant"===e,vectorized:"vectorized"===e}),position:e=>({position:{before_character_definition:0,after_character_definition:1,before_example_messages:5,after_example_messages:6,before_author_note:2,after_author_note:3,at_depth_as_system:4,at_depth_as_user:4,at_depth_as_assistant:4}[e],role:{at_depth_as_system:0,at_depth_as_user:1,at_depth_as_assistant:2}[e]??null}),depth:e=>({depth:null===e?4:e}),order:e=>({order:e}),probability:e=>({probability:e}),keys:e=>({key:e}),logic:e=>({selectiveLogic:{and_any:0,not_all:1,not_any:2,and_all:3}[e]}),filters:e=>({keysecondary:e}),scan_depth:e=>({scanDepth:"same_as_global"===e?null:e}),case_sensitive:e=>({caseSensitive:"same_as_global"===e?null:e}),match_whole_words:e=>({matchWholeWords:"same_as_global"===e?null:e}),use_group_scoring:e=>({useGroupScoring:"same_as_global"===e?null:e}),automation_id:e=>({automationId:null===e?"":e}),exclude_recursion:e=>({excludeRecursion:e}),prevent_recursion:e=>({preventRecursion:e}),delay_until_recursion:e=>({delayUntilRecursion:e}),content:e=>({content:e}),group:e=>({group:e}),group_prioritized:e=>({groupOverride:e}),group_weight:e=>({groupWeight:e}),sticky:e=>({sticky:null===e?0:e}),cooldown:e=>({cooldown:null===e?0:e}),delay:e=>({delay:null===e?0:e})};return _.merge({},be,...Object.entries(e).filter(([e,t])=>void 0!==t).map(([e,n])=>t[e]?.(n)))}const Ce=1e6;function $e(e){const t=new Set,n=e=>{void 0===e&&(e=_.random(0,Ce-1));let n=1;for(;;){if(!t.has(e))return t.add(e),e;e=(e+n*n)%Ce,++n}};let a=_.max(e.map(e=>e.display_index??-1))??-1;return e.map(e=>({...e,uid:n(e.uid),display_index:e.display_index??++a}))}async function Te(e,t){if(!R.world_names.includes(e))throw Error(`未能找到世界书 '${e}'`);const n={entries:_.merge({},...$e(t).map(Se).map(e=>({[e.uid]:e})))};await(0,R.saveWorldInfo)(e,n),ye(e),o().info(`更新世界书 '${e}' 中的条目`)}async function Ae(e,t){return o().info(`对世界书 '${e}' 中的条目进行更新`),await Te(e,await t(await Ee(e))),Ee(e)}async function xe(e,t){return await Ae(e,e=>{for(const n of t){const t=e.find(e=>e.uid===n.uid);t&&_.merge(t,n)}return e})}async function Ie(e,t){const n=[],a=await Ae(e,e=>{const a=new Set(e.map(e=>e.uid));return t.forEach(e=>e.uid=(()=>{for(let e=0;e<Ce;++e)if(!a.has(e))return a.add(e),n.push(e),e;throw Error("无法找到可用的世界书条目 uid")})()),[...e,...t]});return{entries:a,new_uids:n}}async function Re(e,t){let n=!1;const a=await Ae(e,e=>{const a=_.remove(e,e=>t.includes(e.uid));return n=a.length>0,e});return{entries:a,delete_occurred:n}}async function ke(e,t){return(await Ie(e,[t])).new_uids[0]}async function Oe(e,t){return(await Re(e,[t])).delete_occurred}var Le=n(7203),Me=n(6491);function Pe(e){if(!e)throw new Error("脚本ID不能为空");return Me.D.getInstance().getScriptButton(e)}function Ne(e,t){if(!e)throw new Error("脚本ID不能为空");const n=Me.D.getInstance().getScriptById(e);if(!n)throw new Error(`脚本不存在: ${e}`);const a=Le.i.getInstance().getScriptType(n);n.buttons=t,Me.D.getInstance().setScriptButton(n,a)}const De=(e=>{var t={};return n.d(t,e),t})({executeSlashCommandsWithOptions:()=>w.executeSlashCommandsWithOptions});async function Fe(e){const t=await(0,De.executeSlashCommandsWithOptions)(e);if(t.isError)throw Error(`运行 Slash 命令 '${e}' 时出错: ${t.errorMessage}`);return o().info(`运行 Slash 命令: ${e}`),t.pipe}var Be=n(6523),Ge=n(6521),Ve=n(1988),je=n(4925);async function Ue(){const e=await(0,je.Mj)(je.ch);if("string"!=typeof e)throw new Error("获取的版本号无效");return e}async function He(){return(0,je.Oj)()}var qe=n(162);const Ye={addOneMessage:i.addOneMessage,saveSettings:i.saveSettings,reloadEditor:ve,reloadEditorDebounced:ye};function We(){globalThis.TavernHelper={audioEnable:qe.oS,audioImport:qe.Gy,audioMode:qe.SP,audioPlay:qe.nW,audioSelect:qe.xI,builtin:Ye,Character:c,getCharData:l,getCharAvatarPath:d,getChatHistoryBrief:p,getChatHistoryDetail:h,getChatMessages:u.Zm,setChatMessages:u.jf,setChatMessage:u.Gg,createChatMessages:u.NN,deleteChatMessages:u.AH,rotateChatMessages:u.Ab,formatAsDisplayedMessage:E,retrieveDisplayedMessage:S,builtin_prompt_default_order:k,generate:re,generateRaw:ie,getLorebookEntries:Ee,replaceLorebookEntries:Te,updateLorebookEntriesWith:Ae,setLorebookEntries:xe,createLorebookEntries:Ie,createLorebookEntry:ke,deleteLorebookEntries:Re,deleteLorebookEntry:Oe,getLorebookSettings:oe,setLorebookSettings:ce,getCharLorebooks:he,setCurrentCharLorebooks:fe,getLorebooks:le,deleteLorebook:de,createLorebook:pe,getCurrentCharPrimaryLorebook:ue,getChatLorebook:me,setChatLorebook:ge,getOrCreateChatLorebook:_e,registerMacroLike:a.Fz,triggerSlash:Fe,triggerSlashWithResult:Fe,formatAsTavernRegexedString:Be.L_,isCharacterTavernRegexesEnabled:Be.a5,getTavernRegexes:Be.Oy,replaceTavernRegexes:Be.aw,updateTavernRegexesWith:Be.$X,substitudeMacros:Ge.AV,getLastMessageId:Ge.nw,errorCatched:Ge.lO,getVariables:Ve.UN,replaceVariables:Ve.qk,updateVariablesWith:Ve.lC,insertOrAssignVariables:Ve.rT,deleteVariable:Ve.S1,insertVariables:Ve.v1,getScriptButtons:Pe,replaceScriptButtons:Ne,getTavernHelperVersion:Ue,updateTavernHelper:He,getFrontendVersion:Ue,updateFrontendVersion:He}}},9880:(e,t,n)=>{n.d(t,{h:()=>d});var a=n(4365),r=n(1320),i=n(2361),s=n(8853),o=n(363),c=n.n(o);async function l(e){const t=e.event,n=e.data??[];return a.eventSource.emit(t,...n),c().info(`[Event][/event-emit] 发送 '${t}' 事件, 携带数据: ${JSON.stringify(n)}`),t}function d(){s.SlashCommandParser.addCommandObject(r.SlashCommand.fromProps({name:"event-emit",callback:l,returns:"发送的事件名称",namedArgumentList:[i.SlashCommandNamedArgument.fromProps({name:"event",description:"事件名称",typeList:[i.ARGUMENT_TYPE.STRING],isRequired:!0}),i.SlashCommandNamedArgument.fromProps({name:"data",description:"要传输的数据",typeList:[i.ARGUMENT_TYPE.STRING],isRequired:!1,acceptsMultiple:!0})],unnamedArgumentList:[],helpString:'\n    <div>\n        发送 `event` 事件, 同时可以发送一些数据.\n        所有正在监听该消息频道的 listener 函数都会自动运行, 并能用函数参数接收发送来的数据.\n        由于酒馆 STScript 输入方式的局限性, 所有数据将会以字符串 string 类型接收; 如果需要 number 等类型, 请自行转换.\n    </div>\n    <div>\n        <strong>Example:</strong>\n        <ul>\n            <li>\n                <pre><code class="language-stscript">/event-emit event="读档"</code></pre>\n            </li>\n            <li>\n                <pre><code class="language-stscript">/event-emit event="存档" data={{getvar::数据}} data=8 data=你好 {{user}}</code></pre>\n            </li>\n            <li>\n                <pre><code class="language-stscript">/event-emit event="随便什么名称" data="这是一个 数据" data={{user}}</code></pre>\n            </li>\n        </ul>\n    </div>\n  '}))}},9897:(e,t,n)=>{n.d(t,{S:()=>a});const a=new class{map=new Map;get(e){return this.map.get(e)}set(e,t){this.map.set(e,function(e){return URL.createObjectURL(new Blob([e],{type:"application/javascript"}))}(t))}delete(e){const t=function(e,t){const n=e.get(t);if(n)return e.delete(t),n}(this.map,e);return t?(URL.revokeObjectURL(t),t):t}}}},L={};function M(e){var t=L[e];if(void 0!==t)return t.exports;var n=L[e]={exports:{}};return O[e].call(n.exports,n,n.exports,M),n.exports}M.m=O,E="function"==typeof Symbol,S=E?Symbol("webpack queues"):"__webpack_queues__",C=E?Symbol("webpack exports"):"__webpack_exports__",T=E?Symbol("webpack error"):"__webpack_error__",A=e=>{e&&e.d<1&&(e.d=1,e.forEach(e=>e.r--),e.forEach(e=>e.r--?e.r++:e()))},M.a=(e,t,n)=>{var a;n&&((a=[]).d=-1);var r,i,s,o=new Set,c=e.exports,l=new Promise((e,t)=>{s=t,i=e});l[C]=c,l[S]=e=>(a&&e(a),o.forEach(e),l.catch(e=>{})),e.exports=l,t(e=>{var t;r=(e=>e.map(e=>{if(null!==e&&"object"==typeof e){if(e[S])return e;if(e.then){var t=[];t.d=0,e.then(e=>{n[C]=e,A(t)},e=>{n[T]=e,A(t)});var n={};return n[S]=e=>e(t),n}}var a={};return a[S]=e=>{},a[C]=e,a}))(e);var n=()=>r.map(e=>{if(e[T])throw e[T];return e[C]}),i=new Promise(e=>{(t=()=>e(n)).r=0;var i=e=>e!==a&&!o.has(e)&&(o.add(e),e&&!e.d&&(t.r++,e.push(t)));r.map(e=>e[S](i))});return t.r?i:n()},e=>(e?s(l[T]=e):i(c),A(a))),a&&a.d<0&&(a.d=0)},M.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return M.d(t,{a:t}),t},M.d=(e,t)=>{for(var n in t)M.o(t,n)&&!M.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},M.f={},M.e=e=>Promise.all(Object.keys(M.f).reduce((t,n)=>(M.f[n](e,t),t),[])),M.u=e=>e+".1adeab2c1447a937a8e8.chunk.js",M.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),x={},I="JS-Slash-Runner:",M.l=(e,t,n,a)=>{if(x[e])x[e].push(t);else{var r,i;if(void 0!==n)for(var s=document.getElementsByTagName("script"),o=0;o<s.length;o++){var c=s[o];if(c.getAttribute("src")==e||c.getAttribute("data-webpack")==I+n){r=c;break}}r||(i=!0,(r=document.createElement("script")).type="module",r.charset="utf-8",r.timeout=120,M.nc&&r.setAttribute("nonce",M.nc),r.setAttribute("data-webpack",I+n),r.src=e),x[e]=[t];var l=(t,n)=>{r.onerror=r.onload=null,clearTimeout(d);var a=x[e];if(delete x[e],r.parentNode&&r.parentNode.removeChild(r),a&&a.forEach(e=>e(n)),t)return t(n)},d=setTimeout(l.bind(null,void 0,{type:"timeout",target:r}),12e4);r.onerror=l.bind(null,r.onerror),r.onload=l.bind(null,r.onload),i&&document.head.appendChild(r)}},M.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},R={792:0},k=e=>{var t,n,a=e.__webpack_ids__,r=e.__webpack_modules__,i=e.__webpack_runtime__,s=0;for(t in r)M.o(r,t)&&(M.m[t]=r[t]);for(i&&i(M);s<a.length;s++)n=a[s],M.o(R,n)&&R[n]&&R[n][0](),R[a[s]]=0},M.f.j=(e,t)=>{var n=M.o(R,e)?R[e]:void 0;if(0!==n)if(n)t.push(n[1]);else{var a=import("./"+M.u(e)).then(k,t=>{throw 0!==R[e]&&(R[e]=void 0),t});a=Promise.race([a,new Promise(t=>n=R[e]=[t])]),t.push(n[1]=a)}};var P=M(3008);P=await P;
//# sourceMappingURL=index.js.map