<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档处理功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .file-input {
            margin: 10px 0;
        }
        
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 文档处理功能测试</h1>
        <p>这个页面用于测试文档处理插件的各项功能</p>
        
        <div class="test-section" id="pluginStatus">
            <h3>🔌 插件状态检查</h3>
            <button onclick="checkPluginStatus()">检查插件状态</button>
            <div id="pluginResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>📁 文件上传测试</h3>
            <input type="file" id="testFile" class="file-input" accept=".txt,.pdf,.doc,.docx,.json,.md">
            <br>
            <button onclick="testFileUpload()">测试文件处理</button>
            <div id="uploadResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>📋 支持格式查询</h3>
            <button onclick="getSupportedFormats()">获取支持的格式</button>
            <div id="formatsResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🧪 功能集成测试</h3>
            <button onclick="testIntegration()">测试集成功能</button>
            <div id="integrationResult" class="result"></div>
        </div>
    </div>

    <script>
        // 检查插件状态
        function checkPluginStatus() {
            const result = document.getElementById('pluginResult');
            const section = document.getElementById('pluginStatus');
            
            let status = '插件状态检查:\n\n';
            
            // 检查各个函数是否存在
            const functions = [
                '__processFileByPlugin',
                '__processDocumentByPlugin', 
                '__uploadImageByPlugin',
                '__getSupportedFileTypes'
            ];
            
            let allAvailable = true;
            
            functions.forEach(func => {
                if (typeof top.window[func] === 'function') {
                    status += `✅ ${func} - 可用\n`;
                } else {
                    status += `❌ ${func} - 不可用\n`;
                    allAvailable = false;
                }
            });
            
            if (allAvailable) {
                status += '\n🎉 所有插件功能都可用！';
                section.className = 'test-section success';
            } else {
                status += '\n⚠️ 部分插件功能不可用，请检查插件是否正确加载';
                section.className = 'test-section warning';
            }
            
            result.textContent = status;
        }
        
        // 测试文件上传
        async function testFileUpload() {
            const fileInput = document.getElementById('testFile');
            const result = document.getElementById('uploadResult');
            
            if (!fileInput.files.length) {
                result.textContent = '请先选择一个文件';
                return;
            }
            
            const file = fileInput.files[0];
            result.textContent = `正在处理文件: ${file.name}...`;
            
            try {
                // 检查插件是否可用
                if (typeof top.window.__processFileByPlugin !== 'function') {
                    throw new Error('文档处理插件未加载');
                }
                
                // 处理文件
                const processResult = await top.window.__processFileByPlugin(file, {
                    enableAIReading: false
                });
                
                let output = `文件处理结果:\n\n`;
                output += `文件名: ${file.name}\n`;
                output += `类型: ${file.type}\n`;
                output += `大小: ${(file.size / 1024).toFixed(2)} KB\n`;
                output += `处理状态: ${processResult.success ? '成功' : '失败'}\n\n`;
                
                if (processResult.success) {
                    if (processResult.content) {
                        const preview = processResult.content.length > 200 
                            ? processResult.content.substring(0, 200) + '...' 
                            : processResult.content;
                        output += `内容预览:\n${preview}`;
                    } else if (processResult.url) {
                        output += `文件URL: ${processResult.url}`;
                    }
                }
                
                result.textContent = output;
                
            } catch (error) {
                result.textContent = `文件处理失败: ${error.message}`;
            }
        }
        
        // 获取支持的格式
        function getSupportedFormats() {
            const result = document.getElementById('formatsResult');
            
            try {
                if (typeof top.window.__getSupportedFileTypes !== 'function') {
                    throw new Error('获取格式信息功能不可用');
                }
                
                const formats = top.window.__getSupportedFileTypes();
                
                let output = '支持的文件格式:\n\n';
                
                if (formats.images && formats.images.length > 0) {
                    output += '图片格式:\n';
                    formats.images.forEach(format => {
                        output += `  • ${format}\n`;
                    });
                    output += '\n';
                }
                
                if (formats.documents && formats.documents.length > 0) {
                    output += '文档格式:\n';
                    formats.documents.forEach(format => {
                        output += `  • ${format}\n`;
                    });
                    output += '\n';
                }
                
                output += `总计支持 ${formats.all.length} 种格式`;
                
                result.textContent = output;
                
            } catch (error) {
                result.textContent = `获取格式信息失败: ${error.message}`;
            }
        }
        
        // 测试集成功能
        function testIntegration() {
            const result = document.getElementById('integrationResult');
            
            let output = '集成功能测试:\n\n';
            
            // 测试全局函数
            const globalFunctions = [
                'toggleDocumentContent',
                'copyDocumentContent', 
                'requestDocumentAnalysis'
            ];
            
            globalFunctions.forEach(func => {
                if (typeof window[func] === 'function') {
                    output += `✅ ${func} - 已注册\n`;
                } else {
                    output += `❌ ${func} - 未注册\n`;
                }
            });
            
            // 检查同层手机界面是否存在
            const phoneInterface = parent.document || top.document;
            if (phoneInterface) {
                output += '\n📱 同层手机界面检测:\n';
                
                const elements = [
                    'fileSendModal',
                    'fileBtn',
                    'chatMessages'
                ];
                
                elements.forEach(id => {
                    const element = phoneInterface.getElementById(id);
                    if (element) {
                        output += `✅ ${id} - 存在\n`;
                    } else {
                        output += `❌ ${id} - 不存在\n`;
                    }
                });
            }
            
            output += '\n🔗 集成状态: 功能已准备就绪';
            
            result.textContent = output;
        }
        
        // 页面加载时自动检查插件状态
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkPluginStatus, 500);
        });
    </script>
</body>
</html>
