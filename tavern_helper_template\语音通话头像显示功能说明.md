# 语音通话头像显示功能说明

## 功能概述

为SillyTavern聊天界面的语音通话功能添加了角色头像和昵称显示功能，使语音通话界面更加直观和美观。

## 修改内容

### 1. 消息格式支持

现在支持以下语音通话消息格式：

#### 用户消息
```
[我方消息|语音通话|{{消息内容}}|{{消息时间}}]
```

#### 角色消息（带头像）
```
[{{角色昵称}}|{{对方头像}}|语音通话|{{消息内容}}|{{消息时间}}]
```

#### 角色消息（不带头像，兼容旧格式）
```
[{{角色昵称}}|语音通话|{{消息内容}}|{{消息时间}}]
```

#### 语音通话结束消息（带头像）
```
[{{角色昵称}}|{{对方头像}}|语音通话已挂断|{{通话时长}}|{{通话记录JSON}}|{{消息时间}}]
```

### 2. 界面改进

#### 语音通话界面显示效果：
- ✅ **角色头像**：32x32像素，圆角设计
- ✅ **角色昵称**：小字体显示，半透明效果
- ✅ **消息气泡**：保持原有样式
- ✅ **布局对齐**：用户消息右对齐，角色消息左对齐

#### CSS样式新增：
- `.incall-message-wrapper`：消息包装器，支持头像和内容布局
- `.incall-avatar`：语音通话中的头像样式
- `.incall-message-content`：消息内容容器
- `.incall-sender-name`：发送者昵称样式

### 3. 代码修改详情

#### 消息解析器（Message Parsers）
- 添加了支持4字段格式的语音通话消息解析器
- 添加了支持5字段格式的语音通话结束消息解析器
- 保持向后兼容，支持原有的3字段格式

#### 渲染函数（Rendering Functions）
- 修改了 `appendMessageToCallView()` 函数
- 新的布局结构支持头像和昵称显示
- 自动处理头像加载失败的情况

#### 序列化函数（Serialization Functions）
- 更新了语音通话消息的序列化格式
- 确保输出包含头像信息的完整格式

## 使用方法

### 在SillyTavern中使用

1. **发起语音通话**：点击聊天界面的"语音通话"按钮
2. **查看效果**：在语音通话界面中，角色消息将显示头像和昵称
3. **消息格式**：系统会自动使用正确的格式保存和解析消息

### 测试功能

可以使用提供的测试页面 `test_voice_call.html` 来验证消息格式解析是否正确。

## 兼容性

- ✅ **向后兼容**：支持原有的3字段消息格式
- ✅ **渐进增强**：新格式提供更好的显示效果
- ✅ **错误处理**：头像加载失败时自动隐藏
- ✅ **默认值**：缺少头像或昵称时使用默认值

## 技术细节

### 文件修改
- `同层私聊4.html`：主要聊天界面文件
- 新增CSS样式约50行
- 修改JavaScript函数约100行
- 新增消息解析器2个

### 关键函数
- `appendMessageToCallView()`：语音通话消息渲染
- `serializeQunliaoLog()`：消息序列化
- 消息解析器：支持新格式解析

## 效果预览

语音通话界面现在会显示：
```
[头像] 小美
       你好！有什么可以帮助你的吗？

                    你好，我想问个问题 [我]
```

## 注意事项

1. **头像URL**：确保头像URL可访问，否则会自动隐藏
2. **昵称长度**：建议昵称不超过10个字符以保持界面美观
3. **兼容性**：新旧格式可以混合使用，系统会自动识别
4. **性能**：头像加载采用懒加载，不会影响聊天性能

## 更新日志

- **2024-07-24**：初始版本，添加语音通话头像和昵称显示功能
