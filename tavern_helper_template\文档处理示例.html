<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>文档处理示例 - 集成到同层手机界面</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background: #f5f5f5;
      }

      .container {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .file-upload-area {
        border: 2px dashed #ccc;
        border-radius: 10px;
        padding: 40px;
        text-align: center;
        margin: 20px 0;
        transition: all 0.3s ease;
      }

      .file-upload-area:hover {
        border-color: #007bff;
        background: #f8f9fa;
      }

      .file-upload-area.dragover {
        border-color: #007bff;
        background: #e3f2fd;
      }

      .file-input {
        display: none;
      }

      .upload-btn {
        background: #007bff;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
      }

      .upload-btn:hover {
        background: #0056b3;
      }

      .file-info {
        margin: 10px 0;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 5px;
        border-left: 4px solid #007bff;
      }

      .content-display {
        margin: 20px 0;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 5px;
        max-height: 400px;
        overflow-y: auto;
        white-space: pre-wrap;
        font-family: monospace;
      }

      .ai-response {
        margin: 20px 0;
        padding: 15px;
        background: #e8f5e8;
        border-radius: 5px;
        border-left: 4px solid #28a745;
      }

      .error {
        color: #dc3545;
        background: #f8d7da;
        border-color: #dc3545;
      }

      .success {
        color: #155724;
        background: #d4edda;
        border-color: #28a745;
      }

      .loading {
        display: none;
        text-align: center;
        margin: 20px 0;
      }

      .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .supported-formats {
        margin: 20px 0;
        padding: 15px;
        background: #fff3cd;
        border-radius: 5px;
        border-left: 4px solid #ffc107;
      }

      .format-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;
      }

      .format-tag {
        background: #007bff;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>📄 文档处理示例</h1>
      <p>这个示例展示了如何在同层手机界面中集成文档处理功能</p>

      <div class="supported-formats">
        <h3>📋 支持的文件格式</h3>
        <div id="supportedFormats">正在加载...</div>
      </div>

      <div class="file-upload-area" id="uploadArea">
        <div>
          <h3>📁 拖拽文件到这里或点击上传</h3>
          <p>支持图片和文档文件</p>
          <button class="upload-btn" onclick="document.getElementById('fileInput').click()">选择文件</button>
          <input type="file" id="fileInput" class="file-input" multiple />
        </div>
      </div>

      <div class="loading" id="loading">
        <div class="spinner"></div>
        <p>正在处理文件...</p>
      </div>

      <div id="results"></div>

      <div style="margin-top: 40px; padding: 20px; background: #e9ecef; border-radius: 5px">
        <h3>🔧 集成到同层手机界面的代码示例</h3>
        <pre><code>// 在同层手机界面中使用文档处理功能
async function handleFileUpload(file) {
  try {
    // 检查插件是否可用
    if (typeof top.window.__processFileByPlugin === 'function') {
      // 使用插件处理文件（自动识别类型）
      const result = await top.window.__processFileByPlugin(file, {
        enableAIReading: true, // 启用AI阅读分析
        sendToChat: true, // 自动发送到聊天让AI分析
        aiPrompt: '请详细分析这个文档的内容，提供总结和关键见解', // 自定义AI提示
        saveToLocal: true // 保存文档到本地
      });

      if (result.success) {
        console.log('文档处理成功:', result);

        // 显示处理状态
        showStatus('✅ 文档已上传，AI正在分析并回复...');

        // 文档内容会自动发送到SillyTavern聊天
        // 连接的AI模型会根据文档内容进行分析和回复
        // 整个流程完全自动化
      }
    } else {
      console.warn('文档处理插件未加载');
    }
  } catch (error) {
    console.error('文件处理失败:', error);
    showStatus('❌ 文件处理失败: ' + error.message);
  }
}

// 显示状态信息
function showStatus(message) {
  const statusDiv = document.getElementById('uploadStatus');
  if (statusDiv) {
    statusDiv.textContent = message;
    statusDiv.className = message.includes('✅') ? 'status-success' :
                         message.includes('❌') ? 'status-error' : 'status-info';
  }
}</code></pre>
      </div>
    </div>

    <script>
      // 初始化
      document.addEventListener('DOMContentLoaded', function () {
        initializeFileUpload();
        loadSupportedFormats();
      });

      // 加载支持的文件格式
      function loadSupportedFormats() {
        try {
          if (typeof top.window.__getSupportedFileTypes === 'function') {
            const formats = top.window.__getSupportedFileTypes();
            displaySupportedFormats(formats);
          } else {
            document.getElementById('supportedFormats').innerHTML =
              '<span class="error">插件未加载，无法获取支持的格式</span>';
          }
        } catch (error) {
          document.getElementById('supportedFormats').innerHTML =
            '<span class="error">获取格式信息失败: ' + error.message + '</span>';
        }
      }

      // 显示支持的格式
      function displaySupportedFormats(formats) {
        const container = document.getElementById('supportedFormats');
        let html = '';

        if (formats.images && formats.images.length > 0) {
          html += '<div><strong>图片格式:</strong><div class="format-list">';
          formats.images.forEach(format => {
            html += `<span class="format-tag">${format}</span>`;
          });
          html += '</div></div>';
        }

        if (formats.documents && formats.documents.length > 0) {
          html += '<div style="margin-top: 10px;"><strong>文档格式:</strong><div class="format-list">';
          formats.documents.forEach(format => {
            html += `<span class="format-tag">${format}</span>`;
          });
          html += '</div></div>';
        }

        container.innerHTML = html || '暂无支持的格式';
      }

      // 初始化文件上传
      function initializeFileUpload() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');

        // 拖拽事件
        uploadArea.addEventListener('dragover', function (e) {
          e.preventDefault();
          uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function (e) {
          e.preventDefault();
          uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function (e) {
          e.preventDefault();
          uploadArea.classList.remove('dragover');
          const files = Array.from(e.dataTransfer.files);
          handleFiles(files);
        });

        // 文件选择事件
        fileInput.addEventListener('change', function (e) {
          const files = Array.from(e.target.files);
          handleFiles(files);
        });
      }

      // 处理文件
      async function handleFiles(files) {
        const resultsContainer = document.getElementById('results');
        const loading = document.getElementById('loading');

        loading.style.display = 'block';
        resultsContainer.innerHTML = '';

        for (const file of files) {
          try {
            await processFile(file);
          } catch (error) {
            displayError(`处理文件 ${file.name} 失败: ${error.message}`);
          }
        }

        loading.style.display = 'none';
      }

      // 处理单个文件
      async function processFile(file) {
        const resultsContainer = document.getElementById('results');

        // 显示文件信息
        const fileInfo = document.createElement('div');
        fileInfo.className = 'file-info';
        fileInfo.innerHTML = `
                <strong>📄 ${file.name}</strong><br>
                类型: ${file.type}<br>
                大小: ${(file.size / 1024).toFixed(2)} KB
            `;
        resultsContainer.appendChild(fileInfo);

        try {
          // 检查插件是否可用
          if (typeof top.window.__processFileByPlugin !== 'function') {
            throw new Error('文档处理插件未加载');
          }

          // 处理文件
          const result = await top.window.__processFileByPlugin(file, {
            enableAIReading: false, // 这里先不启用AI阅读，只展示内容
          });

          if (result.success) {
            // 显示处理结果
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = `
                        <div class="file-info success">
                            ✅ 处理成功！
                        </div>
                        <div class="content-display">
                            <strong>文件内容:</strong><br>
                            ${result.content || result.url || '处理完成'}
                        </div>
                    `;
            resultsContainer.appendChild(resultDiv);
          } else {
            throw new Error('处理失败');
          }
        } catch (error) {
          displayError(`处理文件失败: ${error.message}`);
        }
      }

      // 显示错误信息
      function displayError(message) {
        const resultsContainer = document.getElementById('results');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'file-info error';
        errorDiv.innerHTML = `❌ ${message}`;
        resultsContainer.appendChild(errorDiv);
      }
    </script>
  </body>
</html>
