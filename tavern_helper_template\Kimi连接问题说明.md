# Kimi连接问题说明

## 为什么不能连接Kimi？

### 1. API格式不兼容
Kimi（月之暗面）使用的API格式与OpenAI标准格式有所不同：

**OpenAI标准格式（我们支持的）：**
```javascript
// 请求格式
{
  "model": "gpt-4-vision-preview",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请描述这张图片"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,..."
          }
        }
      ]
    }
  ]
}
```

**Kimi的格式（不同）：**
```javascript
// Kimi可能使用不同的字段名或结构
{
  "model": "moonshot-v1-8k",
  "messages": [
    {
      "role": "user", 
      "content": "请描述这张图片",
      "files": [
        {
          "type": "image",
          "url": "data:image/jpeg;base64,..."
        }
      ]
    }
  ]
}
```

### 2. 认证方式差异
- **OpenAI标准**：`Authorization: Bearer sk-xxx`
- **Kimi**：可能使用不同的认证头或格式

### 3. 端点路径不同
- **OpenAI标准**：`/v1/chat/completions`
- **Kimi**：可能使用不同的端点路径

## 解决方案

### 方案1：使用兼容OpenAI格式的API服务
推荐使用以下支持OpenAI格式的识图API：

1. **OpenAI官方**
   - API地址：`https://api.openai.com/v1`
   - 模型：`gpt-4-vision-preview`, `gpt-4o`

2. **硅基流动**
   - API地址：`https://api.siliconflow.cn/v1`
   - 模型：`Pro/OpenAI/GPT-4o`, `Pro/Qwen/Qwen2-VL-72B-Instruct`

3. **DeepSeek**
   - API地址：`https://api.deepseek.com/v1`
   - 模型：`deepseek-vl-7b-chat`

4. **智谱AI**
   - API地址：`https://open.bigmodel.cn/api/paas/v4`
   - 模型：`glm-4v-plus`

### 方案2：为Kimi添加专门的适配器
如果您确实需要使用Kimi，我可以为您添加专门的Kimi适配器：

```javascript
// Kimi专用识图函数
async function requestVisionAnalysisWithKimi(imageMsg, indicator) {
  try {
    const response = await fetch('https://api.moonshot.cn/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${kimiApiKey}`
      },
      body: JSON.stringify({
        model: 'moonshot-v1-8k',
        messages: [
          {
            role: 'user',
            content: '请详细描述这张图片的内容',
            files: [
              {
                type: 'image',
                url: imageMsg.imageData
              }
            ]
          }
        ]
      })
    });
    
    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error('Kimi识图失败:', error);
    throw error;
  }
}
```

### 方案3：使用酒馆内置API（推荐）
现在我们已经添加了酒馆内置API选项，这是最简单的方案：

1. **优点**：
   - 无需额外配置API
   - 使用SillyTavern已配置的模型
   - 自动继承酒馆的所有设置
   - 支持破限模式

2. **使用方法**：
   - 在识图配置中选择"使用酒馆内置API"
   - 确保SillyTavern已正确配置了支持视觉的模型
   - 直接使用，无需额外设置

## 当前功能特性

### ✅ 已实现的功能
1. **双模式选择**：
   - 酒馆内置API（推荐）
   - 自定义API

2. **智能回退**：
   - 自定义API失败时自动回退到酒馆API
   - 酒馆API失败时使用原有分析方式

3. **用户体验优化**：
   - 根据选择的模式自动显示/隐藏配置项
   - 清晰的状态指示（酒馆识图中、自定义识图中等）
   - 配置自动保存和恢复

### 🔧 使用建议

1. **首选酒馆模式**：
   - 最简单，无需额外配置
   - 使用您在SillyTavern中已配置好的模型
   - 自动支持破限模式

2. **备选自定义模式**：
   - 当需要使用特定的识图模型时
   - 当酒馆配置的模型不支持视觉时
   - 当需要更精确的识图结果时

3. **Kimi替代方案**：
   - 使用硅基流动的Qwen2-VL模型（中文识图效果很好）
   - 使用智谱AI的GLM-4V（国产模型，中文支持好）
   - 使用OpenAI的GPT-4V（识图精度最高）

## 错误排查

### 常见问题：
1. **"识图API连接失败"**
   - 检查API地址是否正确
   - 检查API密钥是否有效
   - 检查网络连接

2. **"找不到支持视觉的模型"**
   - 尝试手动选择模型
   - 检查API提供商是否支持视觉模型

3. **"酒馆AI不可用"**
   - 检查SillyTavern是否正确配置了AI模型
   - 确保模型支持图片输入

如果您需要连接Kimi，请告诉我，我可以为您添加专门的Kimi适配器！
