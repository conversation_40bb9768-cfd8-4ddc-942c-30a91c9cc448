<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务终端 - 折叠式演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', <PERSON><PERSON>, sans-serif;
            background: #1a1a1a;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: #e0e0e0;
        }
        
        .demo-note {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #ffc107;
            font-size: 13px;
            text-align: center;
        }
        
        details {
            margin-bottom: 20px;
        }
        
        summary {
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            padding: 10px;
            background: rgba(45, 45, 45, 0.8);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #ffffff;
            transition: all 0.3s ease;
        }
        
        summary:hover {
            background: rgba(55, 55, 55, 0.9);
            border-color: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="demo-note">
        🎮 这是折叠式任务终端的演示页面<br>
        点击"任务终端"标题可以展开/折叠内容<br>
        点击任务项会出现交互按钮，大大节省了空间！
    </div>
    
    <details>
    <summary>任务终端</summary>
    <style>
      .task-terminal-container {
        background-image: url('https://files.catbox.moe/jdvsy1.png');
        background-size: cover;
        background-position: center;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
        font-family: 'Microsoft YaHei', 'Helvetica Neue', 'Hiragino Sans GB', sans-serif;
        position: relative;
        overflow: hidden;
        color: #ffffff;
        max-width: 400px;
        margin: 0 auto;
      }
      
      .task-terminal-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 1;
      }
      
      .task-terminal-content {
        position: relative;
        z-index: 2;
        display: grid;
        grid-template-columns: 1fr;
        gap: 12px;
      }
      
      .task-section {
        background-color: rgba(255, 255, 255, 0.1);
        padding: 12px 15px;
        border-left: 4px solid #4CAF50;
        border-radius: 0 6px 6px 0;
        backdrop-filter: blur(2px);
        margin-bottom: 8px;
      }
      
      .task-section.story {
        border-left-color: #ff9800;
      }
      
      .task-section-label {
        font-weight: bold;
        color: #e0e6f0;
        font-size: 0.9em;
        margin-bottom: 8px;
        display: block;
      }
      
      .task-section-content {
        font-size: 0.9em;
        color: #f0f8ff;
        line-height: 1.5;
        max-height: 200px;
        overflow-y: auto;
      }
      
      .task-comment {
        background: rgba(255, 255, 255, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.15);
        border-radius: 6px;
        margin-bottom: 6px;
        padding: 0;
        overflow: hidden;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
      }
      
      .task-comment:hover {
        border-color: #4CAF50;
        box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        transform: translateY(-1px);
        background: rgba(76, 175, 80, 0.1);
      }
      
      .task-comment.story:hover {
        border-color: #ff9800;
        box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
        background: rgba(255, 152, 0, 0.1);
      }
      
      .task-comment-content {
        padding: 8px 10px;
        color: #f0f8ff;
        font-size: 0.85em;
        line-height: 1.4;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
      
      .task-comment-actions {
        background: rgba(0, 0, 0, 0.3);
        padding: 4px 10px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        opacity: 0;
        transition: all 0.3s ease;
      }
      
      .task-comment:hover .task-comment-actions {
        opacity: 1;
      }
      
      .task-comment-info {
        color: #adb5bd;
        font-size: 0.75em;
        font-style: italic;
      }
      
      .task-accept-btn {
        background: linear-gradient(135deg, #4CAF50, #45a049);
        color: white;
        border: none;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 0.75em;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      }
      
      .task-accept-btn:hover {
        background: linear-gradient(135deg, #45a049, #4CAF50);
        transform: scale(1.05);
      }
      
      .task-accept-btn.story {
        background: linear-gradient(135deg, #ff9800, #f57c00);
      }
      
      .task-accept-btn.story:hover {
        background: linear-gradient(135deg, #f57c00, #ff9800);
      }
    </style>
    <div class="task-terminal-container">
      <div class="task-terminal-content">
        <div class="task-section">
          <span class="task-section-label">💚 私人委托</span>
          <div class="task-section-content" id="accepted-tasks-content">
            护送商队任务 - 进度：50%
            调查失踪案件 - 收集线索中
            收集史莱姆胶囊 - 进度：3/10
          </div>
        </div>
        
        <div class="task-section">
          <span class="task-section-label">📋 港区事务</span>
          <div class="task-section-content" id="available-tasks-content">
            <!-- 这里会被JavaScript动态填充 -->
          </div>
        </div>
        
        <div class="task-section story">
          <span class="task-section-label">📖 魔法指令台</span>
          <div class="task-section-content" id="story-tasks-content">
            <!-- 这里会被JavaScript动态填充 -->
          </div>
        </div>
      </div>
    </div>

    <script>
      function acceptTask(taskContent) {
        alert(`演示：接取任务 - ${taskContent}\n\n在SillyTavern中会调用:\ntriggerSlash('接取任务${taskContent}')`);
      }

      function viewStoryTask(taskContent) {
        alert(`演示：查看剧情任务 - ${taskContent}\n\n在SillyTavern中会调用:\ntriggerSlash('查看剧情任务${taskContent}')`);
      }

      function createTaskComment(taskContent, type) {
        const taskDiv = document.createElement('div');
        taskDiv.className = `task-comment ${type === 'story' ? 'story' : ''}`;

        const btnClass = type === 'story' ? 'story' : '';
        const btnText = type === 'story' ? '查看详情' : '确定接取';
        const infoText = type === 'story' ? '点击查看剧情' : '点击接取任务';

        taskDiv.innerHTML = `
          <div class="task-comment-content">${taskContent}</div>
          <div class="task-comment-actions">
            <div class="task-comment-info">${infoText}</div>
            <button class="task-accept-btn ${btnClass}" onclick="event.stopPropagation(); ${type === 'story' ? 'viewStoryTask' : 'acceptTask'}('${taskContent.replace(/'/g, '\\\'')}')" title="${btnText}">
              ${btnText}
            </button>
          </div>
        `;

        taskDiv.addEventListener('click', function(e) {
          if (e.target.classList.contains('task-accept-btn')) {
            return;
          }
          if (type === 'story') {
            viewStoryTask(taskContent);
          } else {
            acceptTask(taskContent);
          }
        });

        return taskDiv;
      }

      // 初始化演示数据
      document.addEventListener('DOMContentLoaded', function() {
        // 可接取任务
        const availableTasks = [
          '清理地下室老鼠群 - 难度★☆☆ - 奖励：50金币',
          '寻找走失的小猫咪 - 难度★★☆ - 奖励：80金币',
          '采集珍稀魔法草药 - 难度★★★ - 奖励：300金币',
          '讨伐森林中的野狼 - 难度★★☆ - 奖励：120金币'
        ];
        
        const availableContainer = document.getElementById('available-tasks-content');
        availableContainer.innerHTML = '';
        availableTasks.forEach(task => {
          const taskElement = createTaskComment(task, 'available');
          availableContainer.appendChild(taskElement);
        });

        // 剧情任务
        const storyTasks = [
          '第四章：命运的抉择 - 神秘占卜师的预言',
          '收集预言书残页 - 当前进度：2/3',
          '解锁古代遗迹入口 - 已完成'
        ];
        
        const storyContainer = document.getElementById('story-tasks-content');
        storyContainer.innerHTML = '';
        storyTasks.forEach(task => {
          const taskElement = createTaskComment(task, 'story');
          storyContainer.appendChild(taskElement);
        });
      });
    </script>
    </details>
</body>
</html>
