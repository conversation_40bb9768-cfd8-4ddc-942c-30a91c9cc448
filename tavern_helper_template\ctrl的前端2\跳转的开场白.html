```
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开场白跳转列表</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        
        
        .container {
            max-width: 800px;
            width: 100%;
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            margin-top: 30px;
        }
        
        header {
            background: #5c6bc0;
            color: white;
            padding: 25px 30px;
            text-align: center;
        }
        
        h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            letter-spacing: 1px;
        }
        
        .subtitle {
            font-size: 1rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .scene-list {
            padding: 25px 20px;
        }
        
        .scene-item {
            background: white;
            border-radius: 15px;
            padding: 20px 25px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid #e0e0e0;
            position: relative;
            overflow: hidden;
        }
        
        .scene-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(92, 107, 192, 0.2);
            border-color: #5c6bc0;
        }
        
        .scene-item:last-child {
            margin-bottom: 0;
        }
        
        .scene-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .scene-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #333;
        }
        
        .scene-index {
            background: #5c6bc0;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .scene-description {
            color: #555;
            line-height: 1.6;
            margin-bottom: 15px;
            font-size: 1rem;
        }
        
        .opening-tag {
            background: rgba(170, 193, 177, 0.85);
            color: white;
            padding: 6px 14px;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: bold;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .scene-item:hover .opening-tag {
            background: #5c6bc0;
            transform: scale(1.05);
        }
        
        .footer {
            text-align: center;
            padding: 25px;
            color: #666;
            font-size: 0.9rem;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }
        
        /* 响应式设计 */
        @media (max-width: 600px) {
            h1 {
                font-size: 2rem;
            }
            
            .subtitle {
                font-size: 1rem;
            }
            
            .scene-item {
                padding: 18px 20px;
            }
            
            .scene-title {
                font-size: 1.2rem;
            }
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .scene-item {
            animation: fadeIn 0.4s ease forwards;
            opacity: 0;
        }
        
        .scene-item:nth-child(1) { animation-delay: 0.1s; }
        .scene-item:nth-child(2) { animation-delay: 0.2s; }
        .scene-item:nth-child(3) { animation-delay: 0.3s; }
        .scene-item:nth-child(4) { animation-delay: 0.4s; }
        .scene-item:nth-child(5) { animation-delay: 0.5s; }
        .scene-item:nth-child(6) { animation-delay: 0.6s; }
        .scene-item:nth-child(7) { animation-delay: 0.7s; }
        .scene-item:nth-child(8) { animation-delay: 0.8s; }
        .scene-item:nth-child(9) { animation-delay: 0.9s; }
        .scene-item:nth-child(10) { animation-delay: 1.0s; }
        .scene-item:nth-child(11) { animation-delay: 1.1s; }
        .scene-item:nth-child(12) { animation-delay: 1.2s; }
        .scene-item:nth-child(13) { animation-delay: 1.3s; }
        
        /* 点击反馈效果 */
        .scene-item:active {
            transform: scale(0.98);
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <p class="subtitle">点击任意场景项跳转到对应的开场白</p>
        </header>
        
        <div class="scene-list">
            <!-- 场景项将由JavaScript动态生成 -->
        </div>
        
        <div class="footer">
            共12个场景 &copy; 开场白跳转器
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 场景数据
            const scenes = [
                {
                    id: 1,
                    title: "普通初遇",
                    description: "Essex在去向虫帝汇报的路上，遇到了你……嗯？S级雄虫？",
                },
                {
                    id: 2,
                    title: "成保镖了？",
                    description: "从今天开始，Essex就是你的保镖了！...",
                },
                {
                    id: 3,
                    title: "英年早婚",
                    description: "虫帝这个死老头把你俩绑定结婚了...",
                },
                {
                    id: 4,
                    title: "雌君吃醋",
                    description: "虫帝要给你挑选雌侍，Essex暗中吃醋...",
                },
                {
                    id: 5,
                    title: "你穿越了？！",
                    description: "坏消息，你穿越了；更坏的消息，你又发情了；不知道是不是好消息，你被捡到了；好消息，见到你的人带着面具看起来帅帅的...",
                },
                {
                    id: 6,
                    title: "你穿越了2.0",
                    description: "你穿越了，下场并不好被星盗抓走了。但好在Essex来救你了，但好像把你认成亚雌了？等等，地球上懂这些吗？",
                },
                {
                    id: 7,
                    title: "养的小亚雌发情了",
                    description: "Essex养的小亚雌发情了怎么办...",
                },
                {
                    id: 8,
                    title: "雄主太弱鸡了怎么办？",
                    description: "你的雌君把你扔给元帅和他雄主操练去了，就这样累死了一个可怜的地球入...",
                },
                {
                    id: 9,
                    title: "战损Essex",
                    description: "可怜的雌虫快死了，想到还没有回应你于是努力活下来赶回去见你...",
                },
                {
                    id: 10,
                    title: "网恋吗？",
                    description: "你网恋了；你已经一个月没有上线了，Essex很想你（你可以选择天降书房）...",
                },
                {
                    id: 11,
                    title: "小时候！",
                    description: "穿越到Essex小时候了！快去拯救他吧...",
                },
                {
                    id: 12,
                    title: "又一条网恋",
                    description: "三年网恋，你好久不上线了，Essex为了转移注意力跑去训练场加练了...",
                },
                {
                    id: 13,
                    title: "老辈子日常",
                    description: "吃腻了小辈谈恋爱，看看老辈子虫怎么相处吧！...",
                },
                {
                    id: 14,
                    title: "反叛军的头目…",
                    description: "你的雌君Essex三个月前成了代理元帅，要去平定反叛军啦！",
                },
               {
                    id: 15,
                    title: "孕夫小番外",
                    description: "老辈子虫也不能输…！",
                },
                {
                    id: 16,
                    title: "社畜Essex",
                    description: "副官们为了躲避催婚出逃啦！Essex把你忙忘啦！",
                },
                  {
                    id: 17,
                    title: "维克小故事",
                    description: "雌父你儿子是给啊！！！你儿子是给！！！",
                },
            ];

            const sceneList = document.querySelector('.scene-list');
            
            // 生成场景项
            scenes.forEach(scene => {
                const sceneItem = document.createElement('div');
                sceneItem.className = 'scene-item';
                sceneItem.dataset.id = scene.id;
                
                sceneItem.innerHTML = `
                    <div class="scene-header">
                        <div class="scene-title">${scene.title}</div>
                        <div class="scene-index">${scene.id}</div>
                    </div>
                    <div class="scene-description">${scene.description}</div>
                    <div class="opening-tag">开场白 ${scene.id}</div>
                `;
                
                // 添加点击事件
                sceneItem.addEventListener('click', function() {
                    switchToOpening(scene.id);
                    
                    // 添加点击反馈效果
                    this.style.backgroundColor = '#f0f4ff';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 300);
                });
                
                sceneList.appendChild(sceneItem);
            });
            
            // 切换到开场白的函数
            async function switchToOpening(openingId) {
                try {
                    // 在实际的SillyTavern环境中，使用以下API
                    if (typeof getChatMessages === 'function' && typeof setChatMessage === 'function') {
                        // 获取第一条消息（通常是角色的开场白）
                        const messages = await getChatMessages(0, { include_swipe: true });
                        
                        if (messages && messages.length > 0 && messages[0].swipes && messages[0].swipes.length > openingId) {
                            // 如果找到了开场白，就切换到对应的开场白
                            const content = messages[0].swipes[openingId];
                            
                            // 使用setChatMessage API设置消息
                            setChatMessage(content, 0, {
                                swipe_id: openingId,
                                refresh: 'display_and_render_current'
                            });
                            
                            console.log(`已切换到开场白 ${openingId}`);
                            
                            // 显示成功消息
                            alert(`成功切换到开场白: ${scenes[openingId-1].title}`);
                        } else {
                            console.error('找不到指定的开场白');
                            alert('错误：找不到指定的开场白');
                        }
                    } else {
                        // 在非SillyTavern环境中，模拟成功消息
                        console.log(`模拟切换到开场白 ${openingId}`);
                        alert(`模拟切换到开场白: ${scenes[openingId-1].title}`);
                    }
                } catch (error) {
                    console.error('切换开场白时出错:', error);
                    alert(`切换开场白时出错: ${error.message}`);
                }
            }
        });
    </script>
</body>
</html>
```