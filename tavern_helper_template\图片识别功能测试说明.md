# 同层私聊喵喵喵2.html 图片识别功能说明

## 功能概述

已为同层私聊喵喵喵2.html添加了完整的图片上传和识图功能，参考了茶馆正式2.0的实现。

## 新增功能

### 1. 识图API配置界面
- 位置：设置面板 → 🖼️ 识图API配置
- 配置项：
  - 识图API地址（支持OpenAI、硅基流动等兼容OpenAI格式的API）
  - 识图API密钥
  - 识图模型选择
- 功能按钮：
  - 测试连接：验证API配置是否正确
  - 刷新模型：获取最新的可用模型列表

### 2. 图片上传功能增强
- 原有的图片上传功能保持不变
- 新增识图分析支持：
  - 当用户上传真实图片时，会自动触发AI识图分析
  - 识图完成后会显示"✅ 已识图"标识
  - AI会根据图片内容生成相应回复

### 3. 识图API集成
- 优先使用配置的识图API进行图片分析
- 如果识图API不可用，会回退到原有的AI分析方式
- 支持多种视觉模型的自动识别和过滤

## 使用方法

### 配置识图API
1. 点击设置按钮进入设置面板
2. 找到"🖼️ 识图API配置"部分
3. 填入API地址，例如：
   - OpenAI: `https://api.openai.com/v1`
   - 硅基流动: `https://api.siliconflow.cn/v1`
4. 填入对应的API密钥
5. 点击"测试连接"验证配置
6. 从下拉菜单中选择支持视觉的模型
7. 配置会自动保存到本地存储

### 发送图片并识图
1. 点击图片按钮（📷）
2. 选择"上传本地图片"
3. 选择图片文件并上传
4. 可选择添加图片描述
5. 点击发送
6. 系统会自动进行识图分析
7. AI会根据图片内容生成回复

## 技术实现

### 识图API调用流程
1. 用户上传图片 → 标记`needsVisionAnalysis: true`
2. 渲染图片消息时显示"🤖 AI识图中..."指示器
3. 调用配置的识图API进行图片分析
4. 获取图片描述后更新指示器为"✅ 已识图"
5. 构建包含图片描述的消息发送给角色AI
6. 触发AI回复生成

### 数据存储
- 识图API配置保存在localStorage中
- 支持页面刷新后自动恢复配置
- 模型列表会缓存以提高性能

### 错误处理
- API连接失败时显示错误信息
- 识图失败时回退到原有分析方式
- 提供详细的状态反馈

## 兼容性

- 完全兼容原有功能
- 不影响现有的图片发送和显示
- 可选功能，不配置识图API也能正常使用
- 支持破限模式和普通模式

## 注意事项

1. 识图API需要支持OpenAI格式的vision接口
2. 图片需要是base64格式或可访问的URL
3. 建议选择专门的视觉模型以获得更好的识图效果
4. API调用会产生费用，请注意使用量控制

## 支持的识图模型

系统会自动识别以下类型的视觉模型：
- GPT-4系列（gpt-4-vision-preview等）
- Claude系列
- Gemini系列
- 通义千问VL系列（qwen-vl, qwen2-vl）
- InternVL系列
- LLaVA系列
- MiniCPM系列
- 其他包含vision、visual、multimodal关键词的模型

如果自动识别不准确，可以手动选择合适的模型。
