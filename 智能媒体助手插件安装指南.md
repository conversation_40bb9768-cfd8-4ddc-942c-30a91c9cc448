# 🚀 智能媒体助手插件安装指南

## 📋 问题解决

### 原问题
- txt、json等文档文件上传时显示"Invalid image format"错误
- 文件类型识别混乱，文档被当作图片处理

### 解决方案
创建了统一的SillyTavern插件，使用标准的manifest.json结构，彻底解决文件类型识别问题。

## 📦 插件结构

```
智能媒体助手插件/
├── manifest.json          # 插件配置文件
├── index.js               # 主要功能代码
├── style.css              # 样式文件
├── README.md              # 使用说明
└── 智能媒体助手插件测试.html  # 测试页面
```

## 🔧 安装方法

### 方法1: 直接复制（推荐）

1. **复制插件文件夹**
   ```
   将整个"智能媒体助手插件"文件夹复制到:
   SillyTavern/public/scripts/extensions/third-party/
   ```

2. **重启SillyTavern**
   - 完全关闭SillyTavern
   - 重新启动SillyTavern

3. **启用插件**
   - 打开SillyTavern
   - 进入"扩展"页面
   - 找到"智能媒体助手"
   - 点击启用

### 方法2: 手动创建

如果复制不成功，可以手动创建：

1. **创建插件目录**
   ```bash
   mkdir "SillyTavern/public/scripts/extensions/third-party/smart-media-assistant"
   ```

2. **复制文件**
   - 将manifest.json、index.js、style.css复制到新目录
   - 确保文件名和内容完全正确

3. **重启并启用**
   - 重启SillyTavern
   - 在扩展页面启用插件

## ✅ 验证安装

### 1. 检查插件状态
打开浏览器开发者工具（F12），在控制台查看：
```javascript
// 检查插件是否加载
console.log(typeof window.__processFileByPlugin); // 应该显示 "function"
console.log(typeof window.__uploadImageByPlugin); // 应该显示 "function"
console.log(typeof window.__processDocumentByPlugin); // 应该显示 "function"
```

### 2. 使用测试页面
打开 `智能媒体助手插件测试.html` 进行功能测试：
- 检查插件状态（应该显示"已加载"）
- 测试文档文件处理
- 测试图片文件处理
- 验证文件类型识别

### 3. 实际测试
1. 创建一个txt文件，内容随意
2. 在SillyTavern中上传这个txt文件
3. 应该正确识别为文档，不再显示"Invalid image format"错误

## ⚙️ 配置选项

插件提供丰富的配置选项，在SillyTavern的扩展设置中可以找到：

### 基础设置
- ✅ **启用图片处理**: 开启图片压缩和优化
- ✅ **启用文档处理**: 开启文档文件处理
- 📏 **文件大小限制**: 默认20MB

### 图片设置
- 🎨 **图片质量**: 压缩质量 (10-100)，默认85
- 📐 **图片最大尺寸**: 最大宽高，默认2048px

### 高级设置
- 🤖 **启用AI阅读**: 自动分析文档内容
- 📊 **显示处理信息**: 显示处理进度提示
- 🔍 **启用调试日志**: 输出详细日志信息

## 🎯 使用方法

### 在同层界面中使用

```javascript
// 处理任意文件（自动识别类型）
async function handleFileUpload(file) {
    try {
        const result = await window.__processFileByPlugin(file, {
            sendToChat: true,        // 发送到聊天
            enableAIReading: true    // 启用AI阅读
        });
        
        console.log('处理成功:', result);
        return result;
    } catch (error) {
        console.error('处理失败:', error);
        throw error;
    }
}

// 在文件选择事件中使用
document.getElementById('fileInput').addEventListener('change', async (e) => {
    const file = e.target.files[0];
    if (file) {
        await handleFileUpload(file);
    }
});
```

### 专门处理文档

```javascript
// 只处理文档文件
async function handleDocumentUpload(file) {
    if (!window.__isDocumentFile(file)) {
        throw new Error('不是文档文件');
    }
    
    const result = await window.__processDocumentByPlugin(file, {
        sendToChat: true,
        enableAIReading: true
    });
    
    return result;
}
```

### 专门处理图片

```javascript
// 只处理图片文件
async function handleImageUpload(file) {
    const result = await window.__uploadImageByPlugin(file);
    return result;
}
```

## 🔍 故障排除

### 常见问题

**Q1: 插件显示未加载**
```
解决方法:
1. 检查文件路径是否正确
2. 确保manifest.json格式正确
3. 重启SillyTavern
4. 查看浏览器控制台错误信息
```

**Q2: txt文件仍然报错**
```
解决方法:
1. 确认插件已正确启用
2. 检查插件配置中"启用文档处理"是否开启
3. 查看控制台日志，确认文件类型识别结果
```

**Q3: 图片处理失败**
```
解决方法:
1. 检查图片文件大小是否超限
2. 确认图片格式是否支持
3. 查看插件配置中"启用图片处理"是否开启
```

### 调试方法

1. **启用调试日志**
   - 在插件设置中开启"启用调试日志"
   - 查看浏览器控制台的详细信息

2. **使用测试页面**
   - 打开测试页面验证功能
   - 查看API接口状态
   - 测试不同类型的文件

3. **手动测试API**
   ```javascript
   // 在控制台中测试
   const testFile = new File(['test content'], 'test.txt', {type: 'text/plain'});
   window.__processFileByPlugin(testFile).then(console.log).catch(console.error);
   ```

## 📈 版本对比

| 功能 | 旧版本 | 新版本 |
|------|--------|--------|
| 插件结构 | 多个独立文件 | 统一manifest.json |
| 文件识别 | 容易混淆 | 智能识别，准确区分 |
| 错误处理 | 简单提示 | 详细错误信息 |
| 配置选项 | 有限 | 丰富的设置界面 |
| 界面设计 | 简单列表 | 优雅的收缩栏界面 |
| 状态保存 | 无 | 自动保存用户偏好 |
| 调试支持 | 基础 | 完整的日志系统 |
| 兼容性 | 部分兼容 | 完全兼容SillyTavern |

## 🎉 预期效果

安装成功后：
- ✅ txt文件正确识别为文档
- ✅ json文件正确识别为文档并格式化
- ✅ 图片文件正常处理
- ✅ 不再出现"Invalid image format"错误
- ✅ 统一的处理接口和配置管理
- ✅ 优雅的收缩栏设置界面
- ✅ 自动保存界面状态偏好
- ✅ 完整的错误处理和用户反馈

## 🎨 界面特性

### 收缩栏功能
- **优雅设计**: 渐变色彩和平滑动画效果
- **状态保存**: 自动记住用户的展开/收缩偏好
- **响应式**: 适配不同屏幕尺寸
- **交互反馈**: 鼠标悬停和点击动画效果

### 设置分组
- **基础设置**: 图片和文档处理开关
- **图片设置**: 质量和尺寸控制
- **文档设置**: AI阅读和文件大小限制
- **高级设置**: 调试日志和处理信息显示

## 📞 技术支持

如果遇到问题：
1. 查看本指南的故障排除部分
2. 使用测试页面进行诊断
3. 查看浏览器控制台的错误信息
4. 确保SillyTavern版本兼容

---

**作者**: kencuo  
**版本**: 2.0.0  
**更新日期**: 2024-08-02
