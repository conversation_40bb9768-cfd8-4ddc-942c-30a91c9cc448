#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除HTML文件中的所有注释以节省内存
支持删除：
1. HTML注释 <!-- -->
2. CSS注释 /* */
3. JavaScript单行注释 //
4. JavaScript多行注释 /* */
"""

import re
import sys
import os

def remove_comments_from_html(content):
    """删除HTML文件中的所有注释"""
    
    # 1. 删除HTML注释 <!-- -->
    content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
    
    # 2. 删除CSS和JavaScript多行注释 /* */
    # 需要小心处理字符串中的注释标记
    def remove_multiline_comments(text):
        result = []
        i = 0
        in_string = False
        string_char = None
        
        while i < len(text):
            if not in_string:
                # 检查是否进入字符串
                if text[i] in ['"', "'"]:
                    in_string = True
                    string_char = text[i]
                    result.append(text[i])
                # 检查多行注释开始
                elif i < len(text) - 1 and text[i:i+2] == '/*':
                    # 找到注释结束
                    end = text.find('*/', i + 2)
                    if end != -1:
                        i = end + 1  # 跳过 */
                    else:
                        break  # 注释没有结束，跳过剩余内容
                else:
                    result.append(text[i])
            else:
                # 在字符串中
                if text[i] == string_char and (i == 0 or text[i-1] != '\\'):
                    in_string = False
                    string_char = None
                result.append(text[i])
            
            i += 1
        
        return ''.join(result)
    
    content = remove_multiline_comments(content)
    
    # 3. 删除JavaScript单行注释 //
    # 按行处理，避免删除URL中的//
    lines = content.split('\n')
    processed_lines = []
    
    for line in lines:
        # 检查是否在字符串中
        in_string = False
        string_char = None
        comment_pos = -1
        
        i = 0
        while i < len(line):
            if not in_string:
                if line[i] in ['"', "'"]:
                    in_string = True
                    string_char = line[i]
                elif i < len(line) - 1 and line[i:i+2] == '//':
                    # 检查是否是URL的一部分
                    if i > 0 and line[i-1] == ':':
                        # 可能是URL，跳过
                        pass
                    else:
                        comment_pos = i
                        break
            else:
                if line[i] == string_char and (i == 0 or line[i-1] != '\\'):
                    in_string = False
                    string_char = None
            i += 1
        
        if comment_pos != -1:
            line = line[:comment_pos].rstrip()
        
        processed_lines.append(line)
    
    content = '\n'.join(processed_lines)
    
    # 4. 清理多余的空行（保留必要的空行）
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    # 5. 清理行尾空白
    lines = content.split('\n')
    lines = [line.rstrip() for line in lines]
    content = '\n'.join(lines)
    
    return content

def main():
    if len(sys.argv) != 2:
        print("用法: python remove_comments.py <html文件路径>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        sys.exit(1)
    
    # 备份原文件
    backup_path = file_path + '.backup'
    if not os.path.exists(backup_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"已创建备份文件: {backup_path}")
    
    # 读取文件
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                original_content = f.read()
        except UnicodeDecodeError:
            print("错误: 无法读取文件编码")
            sys.exit(1)
    
    # 删除注释
    cleaned_content = remove_comments_from_html(original_content)
    
    # 写入文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(cleaned_content)
    
    # 统计信息
    original_size = len(original_content)
    cleaned_size = len(cleaned_content)
    saved_bytes = original_size - cleaned_size
    saved_percent = (saved_bytes / original_size) * 100 if original_size > 0 else 0
    
    print(f"处理完成!")
    print(f"原始大小: {original_size:,} 字节")
    print(f"处理后大小: {cleaned_size:,} 字节")
    print(f"节省空间: {saved_bytes:,} 字节 ({saved_percent:.1f}%)")

if __name__ == "__main__":
    main()
