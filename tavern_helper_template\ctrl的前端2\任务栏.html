<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>任务栏系统</title>
    <style>
      body {
        font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
        margin: 0;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: #333;
      }

      .task-container {
        max-width: 800px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .task-header {
        background: linear-gradient(45deg, #4caf50, #45a049);
        color: white;
        padding: 20px;
        text-align: center;
        font-size: 24px;
        font-weight: bold;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .task-section {
        margin: 20px;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      .section-header {
        padding: 15px 20px;
        font-weight: bold;
        font-size: 18px;
        color: white;
        text-align: center;
        position: relative;
      }

      .accepted-tasks .section-header {
        background: linear-gradient(45deg, #2196f3, #1976d2);
      }

      .adult-tasks .section-header {
        background: linear-gradient(45deg, #e91e63, #c2185b);
      }

      .story-tasks .section-header {
        background: linear-gradient(45deg, #ff9800, #f57c00);
      }

      .task-list {
        background: white;
        min-height: 60px;
        max-height: 300px;
        overflow-y: auto;
      }

      .task-item {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        transition: all 0.3s ease;
        position: relative;
        cursor: pointer;
      }

      .task-item:last-child {
        border-bottom: none;
      }

      .task-item:hover {
        background: #f8f9fa;
        transform: translateX(5px);
      }

      .task-item::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        transition: all 0.3s ease;
      }

      .accepted-tasks .task-item::before {
        background: #2196f3;
      }

      .adult-tasks .task-item::before {
        background: #e91e63;
      }

      .story-tasks .task-item::before {
        background: #ff9800;
      }

      .task-item:hover::before {
        width: 8px;
      }

      .empty-state {
        padding: 30px;
        text-align: center;
        color: #999;
        font-style: italic;
      }

      .task-count {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.2);
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 14px;
      }

      .refresh-btn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        background: linear-gradient(45deg, #4caf50, #45a049);
        color: white;
        border: none;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        font-size: 24px;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
      }

      .refresh-btn:hover {
        transform: scale(1.1) rotate(180deg);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
      }

      /* 滚动条样式 */
      .task-list::-webkit-scrollbar {
        width: 6px;
      }

      .task-list::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      .task-list::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }

      .task-list::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }
    </style>
  </head>
  <body>
    <div class="task-container">
      <div class="task-header">🎯 任务管理系统</div>

      <!-- 已接取任务 -->
      <div class="task-section accepted-tasks">
        <div class="section-header">
          📋 已接取任务
          <span class="task-count" id="accepted-count">0</span>
        </div>
        <div class="task-list" id="accepted-list">
          <div class="empty-state">暂无已接取任务</div>
        </div>
      </div>

      <!-- 色情任务 -->
      <div class="task-section adult-tasks">
        <div class="section-header">
          💋 色情任务
          <span class="task-count" id="adult-count">0</span>
        </div>
        <div class="task-list" id="adult-list">
          <div class="empty-state">暂无色情任务</div>
        </div>
      </div>

      <!-- 剧情任务 -->
      <div class="task-section story-tasks">
        <div class="section-header">
          📖 剧情任务
          <span class="task-count" id="story-count">0</span>
        </div>
        <div class="task-list" id="story-list">
          <div class="empty-state">暂无剧情任务</div>
        </div>
      </div>
    </div>

    <button class="refresh-btn" onclick="refreshTasks()" title="刷新任务">🔄</button>

    <script>
      // 🎯 任务系统 - 参考同层私聊5的解析模式
      // 任务数据存储
      let tasks = {
        accepted: [],
        adult: [],
        story: [],
      };

      // 任务解析器配置 - 参考同层私聊5的模式
      const taskParsers = [
        {
          regex: /\[已接取任务\|(.*?)\]/g,
          type: 'accepted',
          handler: match => ({
            id: Date.now() + Math.random(),
            content: match[1].trim(),
            type: 'accepted',
          }),
        },
        {
          regex: /\[色情任务\|(.*?)\]/g,
          type: 'adult',
          handler: match => ({
            id: Date.now() + Math.random(),
            content: match[1].trim(),
            type: 'adult',
          }),
        },
        {
          regex: /\[剧情任务\|(.*?)\]/g,
          type: 'story',
          handler: match => ({
            id: Date.now() + Math.random(),
            content: match[1].trim(),
            type: 'story',
          }),
        },
      ];

      // 解析任务内容 - 简化版本
      function parseTasks(content) {
        // 清空现有任务
        tasks = {
          accepted: [],
          adult: [],
          story: [],
        };

        // 匹配所有 <task> 标签内的内容
        const taskRegex = /<task>([\s\S]*?)<\/task>/gi;
        let taskMatch;
        let hasValidTasks = false;

        while ((taskMatch = taskRegex.exec(content)) !== null) {
          const taskContent = taskMatch[1].trim();

          // 使用解析器处理任务内容
          taskParsers.forEach(parser => {
            parser.regex.lastIndex = 0; // 重置正则表达式索引
            let match;
            while ((match = parser.regex.exec(taskContent)) !== null) {
              const taskData = parser.handler(match);
              tasks[parser.type].push(taskData);
              hasValidTasks = true;
            }
          });
        }

        return hasValidTasks;
      }

      // 渲染任务列表
      function renderTasks() {
        renderTaskSection('accepted', tasks.accepted, 'accepted-list', 'accepted-count');
        renderTaskSection('adult', tasks.adult, 'adult-list', 'adult-count');
        renderTaskSection('story', tasks.story, 'story-list', 'story-count');
      }

      // 渲染单个任务分类
      function renderTaskSection(type, taskList, listId, countId) {
        const listElement = document.getElementById(listId);
        const countElement = document.getElementById(countId);

        // 更新计数
        countElement.textContent = taskList.length;

        // 清空列表
        listElement.innerHTML = '';

        if (taskList.length === 0) {
          const emptyState = document.createElement('div');
          emptyState.className = 'empty-state';
          emptyState.textContent = getEmptyMessage(type);
          listElement.appendChild(emptyState);
        } else {
          taskList.forEach(task => {
            const taskItem = document.createElement('div');
            taskItem.className = 'task-item';
            taskItem.textContent = task.content;
            taskItem.title = `任务类型: ${getTaskTypeName(task.type)}\n点击查看详情`;

            // 添加点击事件
            taskItem.addEventListener('click', () => {
              // 可以在这里添加任务详情显示逻辑
              console.log(`点击了${getTaskTypeName(task.type)}: ${task.content}`);
            });

            listElement.appendChild(taskItem);
          });
        }
      }

      // 获取空状态消息
      function getEmptyMessage(type) {
        const messages = {
          accepted: '暂无已接取任务',
          adult: '暂无色情任务',
          story: '暂无剧情任务',
        };
        return messages[type] || '暂无任务';
      }

      // 获取任务类型名称
      function getTaskTypeName(type) {
        const names = {
          accepted: '已接取任务',
          adult: '色情任务',
          story: '剧情任务',
        };
        return names[type] || '未知任务';
      }

      // 刷新任务
      function refreshTasks() {
        const messageContent = getSillyTavernContent();
        if (messageContent && parseTasks(messageContent)) {
          renderTasks();
        }
      }

      // 获取SillyTavern消息内容 - 简化版本
      function getSillyTavernContent() {
        try {
          // SillyTavern常用选择器
          const selectors = [
            '#chat .mes:last-child .mes_text',
            '.mes:last-child .mes_text',
            '.mes_text:last-child',
            '[data-message-id]:last-child .mes_text',
          ];

          for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
              return element.textContent || element.innerText;
            }
          }

          // 备用方案：获取所有消息的最后一条
          const allMessages = document.querySelectorAll('.mes_text');
          if (allMessages.length > 0) {
            return allMessages[allMessages.length - 1].textContent || allMessages[allMessages.length - 1].innerText;
          }

          return null;
        } catch (error) {
          console.error('获取SillyTavern内容时出错:', error);
          return null;
        }
      }

      // 监听DOM变化 - 简化版本
      function setupMessageObserver() {
        const observer = new MutationObserver(mutations => {
          let shouldUpdate = false;

          mutations.forEach(mutation => {
            if (mutation.type === 'childList' || mutation.type === 'characterData') {
              const target = mutation.target;
              if (
                target.classList &&
                (target.classList.contains('mes') || target.classList.contains('mes_text') || target.closest('.mes'))
              ) {
                shouldUpdate = true;
              }
            }
          });

          if (shouldUpdate) {
            setTimeout(() => {
              const content = getSillyTavernContent();
              if (content && parseTasks(content)) {
                renderTasks();
              }
            }, 500);
          }
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true,
          characterData: true,
        });
      }

      // 页面初始化
      document.addEventListener('DOMContentLoaded', function () {
        // 设置消息观察器
        setupMessageObserver();

        // 初始加载
        refreshTasks();

        // 定期检查更新（备用方案）
        setInterval(() => {
          const content = getSillyTavernContent();
          if (content) {
            const currentTaskCount = tasks.accepted.length + tasks.adult.length + tasks.story.length;
            if (parseTasks(content)) {
              const newTaskCount = tasks.accepted.length + tasks.adult.length + tasks.story.length;
              if (newTaskCount !== currentTaskCount) {
                renderTasks();
              }
            }
          }
        }, 3000);
      });

      // 测试函数（控制台调试用）
      function testTasks() {
        const testContent = `
          <task>[已接取任务|收集10个草药]</task>
          <task>[色情任务|与NPC进行亲密互动]</task>
          <task>[剧情任务|寻找失踪的村民]</task>
          <task>[已接取任务|击败森林中的怪物]</task>
        `;

        if (parseTasks(testContent)) {
          renderTasks();
          console.log('测试任务加载完成！');
        }
      }

      // 全局函数
      window.testTasks = testTasks;
      window.refreshTasks = refreshTasks;
    </script>
  </body>
</html>
