<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>好友列表</title>
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        background: linear-gradient(135deg, #c6d8b2 0%, #b8b8b8 50%, #d4d4d4 100%);
        font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
        color: #4a4a4a;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .phone-shell {
        background: linear-gradient(145deg, #a8a8a8, #c0c0c0);
        padding: 15px;
        border-radius: 45px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        position: relative;
      }

      .cute-phone {
        width: 320px;
        height: 680px;
        background: linear-gradient(180deg, #f5f5f5 0%, #e8e8e8 100%);
        border-radius: 38px;
        box-shadow: inset 0 0 0 2px #d0d0d0;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border: 3px solid #999999;
        position: relative;
      }

      .cute-phone::before,
      .cute-phone::after {
        position: absolute;
        font-size: 20px;
        pointer-events: none;
      }

      .cute-phone::before {
        content: '⚪';
        top: -10px;
        left: 25px;
        animation: sparkle 3s infinite ease-in-out;
      }

      .cute-phone::after {
        content: '⚫';
        top: -12px;
        right: 25px;
        animation: twinkle 4s infinite ease-in-out;
      }

      @keyframes sparkle {
        0%,
        100% {
          transform: scale(1) rotate(0deg);
        }
        50% {
          transform: scale(1.2) rotate(180deg);
        }
      }

      @keyframes twinkle {
        0%,
        100% {
          transform: scale(1) rotate(0deg);
          opacity: 0.8;
        }
        50% {
          transform: scale(1.1) rotate(-180deg);
          opacity: 1;
        }
      }

      .header {
        background: linear-gradient(135deg, #888888, #a0a0a0);
        color: white;
        padding: 15px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .header h1 {
        font-size: 18px;
        font-weight: 500;
      }

      .add-btn {
        background: rgba(248, 250, 160, 0.76);
        border: none;
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 18px;
      }

      .add-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
      }

      .friends-container {
        flex: 1;
        overflow-y: auto;
        padding: 10px;
      }

      .friends-container::-webkit-scrollbar {
        width: 6px;
      }

      .friends-container::-webkit-scrollbar-track {
        background: #e0e0e0;
        border-radius: 3px;
      }

      .friends-container::-webkit-scrollbar-thumb {
        background: #b0b0b0;
        border-radius: 3px;
      }

      .friend-item {
        background: white;
        margin: 8px 0;
        padding: 12px 15px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-left: 4px solid transparent;
      }

      .friend-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
      }

      .friend-item.pinned {
        border-left-color: #d4f39b;
        background: linear-gradient(135deg, #f9fce6, #ffffff);
      }

      .friend-avatar {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        background: linear-gradient(135deg, #d4f39b, #c9e661);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-right: 12px;
        font-size: 16px;
      }

      .friend-info {
        flex: 1;
      }

      .friend-name {
        font-size: 16px;
        font-weight: 500;
        color: #4a4a4a;
        margin-bottom: 2px;
      }

      .friend-type {
        font-size: 12px;
        color: #888;
      }

      .friend-actions {
        display: flex;
        gap: 8px;
      }

      .action-btn {
        width: 28px;
        height: 28px;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        transition: all 0.3s ease;
      }

      .pin-btn {
        background: #f5f4b7;
        color: #888;
      }

      .pin-btn.pinned {
        background: #d4f39b;
        color: white;
      }

      .delete-btn {
        background: #ffebee;
        color: #d32f2f;
      }

      .action-btn:hover {
        transform: scale(1.1);
      }

      .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 1000;
      }

      .modal-content {
        background: white;
        padding: 25px;
        border-radius: 20px;
        width: 280px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      }

      .modal h3 {
        color: #4a4a4a;
        margin-bottom: 15px;
        font-size: 18px;
      }

      .modal input,
      .modal select {
        width: 100%;
        padding: 12px;
        border: 2px solid #f5f4b7;
        border-radius: 10px;
        margin: 8px 0;
        font-size: 14px;
        outline: none;
        transition: border-color 0.3s ease;
      }

      .modal input:focus,
      .modal select:focus {
        border-color: #d4f39b;
      }

      .modal-buttons {
        display: flex;
        gap: 10px;
        margin-top: 20px;
      }

      .modal-btn {
        flex: 1;
        padding: 12px;
        border: none;
        border-radius: 10px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .confirm-btn {
        background: #d4f39b;
        color: white;
      }

      .cancel-btn {
        background: #f5f4b7;
        color: #888;
      }

      .modal-btn:hover {
        transform: translateY(-1px);
      }

      .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #888;
      }

      .empty-state .icon {
        font-size: 48px;
        margin-bottom: 15px;
      }

      .group-members {
        font-size: 11px;
        color: #999;
        margin-top: 3px;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    </style>
  </head>
  <body>
    <div class="phone-shell">
      <div class="cute-phone">
        <div class="header">
          <h1>好友列表</h1>
          <button class="add-btn" onclick="openAddModal()">+</button>
        </div>

        <div class="friends-container" id="friendsContainer">
          <div class="empty-state">
            <div class="icon">👥</div>
            <div>暂无好友</div>
            <div style="font-size: 12px; margin-top: 5px">点击右上角 + 号添加好友</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加好友模态框 -->
    <div class="modal" id="addModal">
      <div class="modal-content">
        <h3>添加好友</h3>
        <select id="friendType" onchange="toggleGroupInput()">
          <option value="friend">个人好友</option>
          <option value="group">群聊</option>
        </select>
        <input type="text" id="friendName" placeholder="请输入好友名称或群聊名称" />
        <input type="text" id="groupMembers" placeholder="请输入群成员，用逗号隔开" style="display: none" />
        <div class="modal-buttons">
          <button class="modal-btn cancel-btn" onclick="closeAddModal()">取消</button>
          <button class="modal-btn confirm-btn" onclick="addFriend()">添加</button>
        </div>
      </div>
    </div>

    <script>
      let friends = JSON.parse(localStorage.getItem('friendsList') || '[]');

      function renderFriends() {
        const container = document.getElementById('friendsContainer');

        if (friends.length === 0) {
          container.innerHTML = `
                    <div class="empty-state">
                        <div class="icon">👥</div>
                        <div>暂无好友</div>
                        <div style="font-size: 12px; margin-top: 5px;">点击右上角 + 号添加好友</div>
                    </div>
                `;
          return;
        }

        // 排序：置顶的在前面
        const sortedFriends = [...friends].sort((a, b) => {
          if (a.pinned && !b.pinned) return -1;
          if (!a.pinned && b.pinned) return 1;
          return 0;
        });

        container.innerHTML = sortedFriends
          .map(
            (friend, index) => `
                <div class="friend-item ${friend.pinned ? 'pinned' : ''}" onclick="openChat('${friend.name}', '${
              friend.type
            }', '${friend.members || ''}')">
                    <div class="friend-avatar">
                        ${friend.type === 'group' ? '👥' : friend.name.charAt(0)}
                    </div>
                    <div class="friend-info">
                        <div class="friend-name">${friend.name}</div>
                        <div class="friend-type">${friend.type === 'group' ? '群聊' : '好友'}</div>
                        ${friend.members ? `<div class="group-members">${friend.members}</div>` : ''}
                    </div>
                    <div class="friend-actions">
                        <button class="action-btn pin-btn ${friend.pinned ? 'pinned' : ''}" 
                                onclick="event.stopPropagation(); togglePin(${index})" 
                                title="${friend.pinned ? '取消置顶' : '置顶'}">
                            📌
                        </button>
                        <button class="action-btn delete-btn" 
                                onclick="event.stopPropagation(); deleteFriend(${index})" 
                                title="删除">
                            🗑️
                        </button>
                    </div>
                </div>
            `,
          )
          .join('');
      }

      function openAddModal() {
        document.getElementById('addModal').style.display = 'flex';
        document.getElementById('friendName').value = '';
        document.getElementById('groupMembers').value = '';
        document.getElementById('friendType').value = 'friend';
        toggleGroupInput();
      }

      function closeAddModal() {
        document.getElementById('addModal').style.display = 'none';
      }

      function toggleGroupInput() {
        const type = document.getElementById('friendType').value;
        const groupInput = document.getElementById('groupMembers');
        groupInput.style.display = type === 'group' ? 'block' : 'none';
      }

      function addFriend() {
        const name = document.getElementById('friendName').value.trim();
        const type = document.getElementById('friendType').value;
        const members = document.getElementById('groupMembers').value.trim();

        if (!name) {
          alert('请输入名称');
          return;
        }

        if (type === 'group' && !members) {
          alert('请输入群成员');
          return;
        }

        // 检查是否已存在
        if (friends.some(f => f.name === name)) {
          alert('该好友已存在');
          return;
        }

        const newFriend = {
          name,
          type,
          pinned: false,
          members: type === 'group' ? members : null,
        };

        friends.push(newFriend);
        localStorage.setItem('friendsList', JSON.stringify(friends));
        renderFriends();
        closeAddModal();
      }

      function togglePin(index) {
        friends[index].pinned = !friends[index].pinned;
        localStorage.setItem('friendsList', JSON.stringify(friends));
        renderFriends();
      }

      function deleteFriend(index) {
        if (confirm(`确定要删除 ${friends[index].name} 吗？`)) {
          friends.splice(index, 1);
          localStorage.setItem('friendsList', JSON.stringify(friends));
          renderFriends();
        }
      }

      function openChat(name, type, members) {
        if (typeof triggerSlash === 'function') {
          if (type === 'group') {
            const chatContent = `<群成员：${members}>\n【和${name}的聊天】\n<qunliao>\n</qunliao>`;
            triggerSlash(`/send ${chatContent}`);
          } else {
            const chatContent = `【和${name}的聊天】\n<shouji>\n</shouji>`;
            triggerSlash(`/send ${chatContent}`);
          }
        } else {
          console.log('triggerSlash function not available');
          alert(`打开与 ${name} 的聊天`);
        }
      }

      // 点击模态框外部关闭
      document.getElementById('addModal').addEventListener('click', function (e) {
        if (e.target === this) {
          closeAddModal();
        }
      });

      // 初始化渲染
      renderFriends();
    </script>
  </body>
</html>
